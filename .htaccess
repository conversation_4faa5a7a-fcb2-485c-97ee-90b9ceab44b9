RewriteEngine On

# Redirect to HTTPS (if using SSL)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Block access to hidden files
<FilesMatch "^\.">
    Order allow,deny
    <PERSON><PERSON> from all
</FilesMatch>

# Protect against common attacks
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
</IfModule> 