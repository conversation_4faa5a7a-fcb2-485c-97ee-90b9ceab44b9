<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Define upload directory for profile photos
$upload_dir = "../uploads/profile_photos/";

// Create directory if it doesn't exist
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

// Check if role column exists in users table
$check_role_column = $conn->query("SHOW COLUMNS FROM users LIKE 'role'");
if ($check_role_column->num_rows == 0) {
    $conn->query("ALTER TABLE users ADD COLUMN role VARCHAR(20) NOT NULL DEFAULT 'student'");
}

// Check if role column exists in students table
$check_student_role_column = $conn->query("SHOW COLUMNS FROM students LIKE 'role'");
if ($check_student_role_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD COLUMN role VARCHAR(50) NULL");
}

// Check if roll_number column exists in students table
$check_roll_number_column = $conn->query("SHOW COLUMNS FROM students LIKE 'roll_number'");
if ($check_roll_number_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD COLUMN roll_number VARCHAR(20) NULL");
}

// Check if guardian columns exist in the students table and add if they don't
$guardian_columns = [
    "guardian_name" => "VARCHAR(100)",
    "guardian_relation" => "VARCHAR(50)",
    "guardian_phone" => "VARCHAR(20)",
    "guardian_email" => "VARCHAR(100)",
    "guardian_address" => "TEXT",
    "guardian_occupation" => "VARCHAR(100)"
];

foreach ($guardian_columns as $column => $type) {
    $check_column = $conn->query("SHOW COLUMNS FROM students LIKE '$column'");
    if ($check_column->num_rows == 0) {
        $conn->query("ALTER TABLE students ADD $column $type");
    }
}

// Check if profile_photo column exists in students table
$check_column = $conn->query("SHOW COLUMNS FROM students LIKE 'profile_photo'");
if ($check_column->num_rows == 0) {
    $conn->query("ALTER TABLE students ADD profile_photo VARCHAR(255)");
}

// Fetch sessions for dropdown
$sessionsQuery = $conn->query("SELECT id, session_name as name FROM sessions ORDER BY session_name");
$sessionList = [];
while ($row = $sessionsQuery->fetch_assoc()) {
    $sessionList[$row['id']] = $row['name'];
}

// Fetch departments for dropdown
$departmentsQuery = $conn->query("SELECT id, department_name FROM departments ORDER BY department_name");
$departmentList = [];
while ($row = $departmentsQuery->fetch_assoc()) {
    $departmentList[$row['id']] = $row['department_name'];
}

// Fetch classes for dropdown
$classesQuery = $conn->query("SELECT id, class_name FROM classes ORDER BY class_name");
$classList = [];
while ($row = $classesQuery->fetch_assoc()) {
    $classList[$row['id']] = $row['class_name'];
}

$errorMessage = '';
$successMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate form input
    $required_fields = ['student_id', 'first_name', 'last_name', 'phone', 'address', 
                       'dob', 'gender', 'batch', 'admission_date', 'department_id',
                       'username', 'password'];
    
    $missing_fields = [];
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        $errorMessage = 'সমস্ত প্রয়োজনীয় ফিল্ড পূরণ করুন। ইমেইল, সেশন, ক্লাস, প্রোফাইল ছবি এবং অভিভাবকের তথ্য বাদে অন্যান্য সব ফিল্ড প্রয়োজনীয়।';
    } else {
        // Validate student ID is unique
        $check_id = $conn->prepare("SELECT id FROM students WHERE student_id = ? AND id != ?");
        $student_id = $_POST['student_id'];
        $id = 0; // for new student
        $check_id->bind_param("si", $student_id, $id);
        $check_id->execute();
        $id_result = $check_id->get_result();
        
        if ($id_result->num_rows > 0) {
            $errorMessage = 'শিক্ষার্থী আইডি ইতিমধ্যে ব্যবহৃত হয়েছে। দয়া করে অন্য একটি চেষ্টা করুন।';
        } else {
            // Validate username is unique
            $check_username = $conn->prepare("SELECT id FROM users WHERE username = ?");
            $username = $_POST['username'];
            $check_username->bind_param("s", $username);
            $check_username->execute();
            $username_result = $check_username->get_result();
            
            if ($username_result->num_rows > 0) {
                $errorMessage = 'ইউজারনেম ইতিমধ্যে ব্যবহৃত হয়েছে। দয়া করে অন্য একটি চেষ্টা করুন।';
            } else {
                // Process file upload if a file is selected
                $profile_photo_path = null;
                if (isset($_FILES['profile_photo']) && $_FILES['profile_photo']['error'] === UPLOAD_ERR_OK) {
                    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                    $file_extension = strtolower(pathinfo($_FILES['profile_photo']['name'], PATHINFO_EXTENSION));
                    
                    if (!in_array($file_extension, $allowed_extensions)) {
                        $errorMessage = 'অবৈধ ফাইল টাইপ। শুধুমাত্র JPG, JPEG, PNG, এবং GIF ফাইল অনুমোদিত।';
                    } elseif ($_FILES['profile_photo']['size'] > 5000000) { // 5MB max
                        $errorMessage = 'ফাইল সাইজ খুব বড়। সর্বাধিক ফাইল সাইজ 5MB।';
                    } else {
                        $file_name = uniqid() . '.' . $file_extension;
                        $target_file = $upload_dir . $file_name;
                        
                        if (move_uploaded_file($_FILES['profile_photo']['tmp_name'], $target_file)) {
                            $profile_photo_path = 'uploads/profile_photos/' . $file_name;
                        } else {
                            $errorMessage = 'ফাইল আপলোড করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন।';
                        }
                    }
                }
                
                if (empty($errorMessage)) {
                    try {
                        $conn->begin_transaction();
                        
                        // Insert into users table
                        $user_stmt = $conn->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, 'student')");
                        $hashed_password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                        $user_stmt->bind_param("ss", $_POST['username'], $hashed_password);
                        $user_stmt->execute();
                        $user_id = $conn->insert_id;
                        
                        // Insert into students table
                        $student_id = $_POST['student_id'];
                        $roll_number = !empty($_POST['roll_number']) ? $_POST['roll_number'] : null;
                        $first_name = $_POST['first_name'];
                        $last_name = $_POST['last_name'];
                        $email = !empty($_POST['email']) ? $_POST['email'] : null;
                        $phone = $_POST['phone'];
                        $address = $_POST['address'];
                        $dob = $_POST['dob'];
                        $gender = $_POST['gender'];
                        $batch = $_POST['batch'];
                        $admission_date = $_POST['admission_date'];
                        $department_id = $_POST['department_id'];
                        $class_id = !empty($_POST['class_id']) ? $_POST['class_id'] : null;
                        $session_id = !empty($_POST['session_id']) ? $_POST['session_id'] : null;
                        $guardian_name = !empty($_POST['guardian_name']) ? $_POST['guardian_name'] : null;
                        $guardian_relation = !empty($_POST['guardian_relation']) ? $_POST['guardian_relation'] : null;
                        $guardian_phone = !empty($_POST['guardian_phone']) ? $_POST['guardian_phone'] : null;
                        $guardian_email = !empty($_POST['guardian_email']) ? $_POST['guardian_email'] : null;
                        $guardian_address = !empty($_POST['guardian_address']) ? $_POST['guardian_address'] : null;
                        $guardian_occupation = !empty($_POST['guardian_occupation']) ? $_POST['guardian_occupation'] : null;
                        $student_role = !empty($_POST['student_role']) ? $_POST['student_role'] : null;
                        
                        // Instead of using the complex bind_param function directly, let's use a direct insert approach
                        // This avoids the parameter binding issues

                        // First, create an array of the values
                        $values = [
                            'user_id' => $user_id,
                            'student_id' => $student_id,
                            'roll_number' => $roll_number,
                            'first_name' => $first_name,
                            'last_name' => $last_name,
                            'email' => $email,
                            'phone' => $phone,
                            'address' => $address,
                            'dob' => $dob,
                            'gender' => $gender,
                            'batch' => $batch,
                            'admission_date' => $admission_date,
                            'department_id' => $department_id,
                            'class_id' => $class_id,
                            'session_id' => $session_id,
                            'profile_photo' => $profile_photo_path,
                            'guardian_name' => $guardian_name,
                            'guardian_relation' => $guardian_relation,
                            'guardian_phone' => $guardian_phone,
                            'guardian_email' => $guardian_email,
                            'guardian_address' => $guardian_address,
                            'guardian_occupation' => $guardian_occupation,
                            'role' => $student_role
                        ];
                        
                        // Create the columns and placeholders parts of the query
                        $columns = implode(', ', array_keys($values));
                        
                        // Create placeholders with proper escaping
                        $placeholders = [];
                        foreach ($values as $key => $value) {
                            // Skip NULL values
                            if ($value === NULL) {
                                $placeholders[] = "NULL";
                            } else {
                                // Quote string values
                                $placeholders[] = "'" . $conn->real_escape_string($value) . "'";
                            }
                        }
                        $placeholderString = implode(', ', $placeholders);
                        
                        // Create and execute the direct INSERT query
                        $insertQuery = "INSERT INTO students ($columns) VALUES ($placeholderString)";
                        $insertResult = $conn->query($insertQuery);
                        
                        if (!$insertResult) {
                            throw new Exception("Database error: " . $conn->error);
                        }
                        
                        $conn->commit();
                        
                        $successMessage = 'শিক্ষার্থী সফলভাবে যোগ করা হয়েছে।';
                        
                        // Reset POST data after successful submission
                        $_POST = [];
                    } catch (Exception $e) {
                        $conn->rollback();
                        $errorMessage = 'শিক্ষার্থী যোগ করতে সমস্যা হয়েছে: ' . $e->getMessage();
                    }
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Student - College Management System</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .profile-photo-preview {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #ccc;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #adb5bd;
            overflow: hidden;
            margin: 0 auto 1rem auto;
        }
        .profile-photo-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .photo-label {
            display: block;
            text-align: center;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>এডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> স্টাফ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>নতুন শিক্ষার্থী যোগ করুন</h2>
                        <p class="text-muted">একটি নতুন শিক্ষার্থীর রেকর্ড তৈরি করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="POST" action="add_student.php" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Academic Settings -->
                                <div class="col-12 mb-4">
                                    <h4 class="card-title mb-3">একাডেমিক সেটিংস</h4>
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <label for="session_id" class="form-label">সেশন</label>
                                            <select class="form-select" id="session_id" name="session_id">
                                                <option value="">সেশন নির্বাচন করুন</option>
                                                <?php foreach ($sessionList as $id => $name): ?>
                                                    <option value="<?php echo $id; ?>" <?php echo (isset($_POST['session_id']) && $_POST['session_id'] == $id) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="department_id" class="form-label">বিভাগ*</label>
                                            <select class="form-select" id="department_id" name="department_id" required>
                                                <option value="">বিভাগ নির্বাচন করুন</option>
                                                <?php foreach ($departmentList as $id => $name): ?>
                                                    <option value="<?php echo $id; ?>" <?php echo (isset($_POST['department_id']) && $_POST['department_id'] == $id) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($name); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="class_id" class="form-label">ক্লাস</label>
                                            <div class="input-group">
                                                <select class="form-select" id="class_id" name="class_id">
                                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                                    <?php foreach ($classList as $id => $name): ?>
                                                        <option value="<?php echo $id; ?>" <?php echo (isset($_POST['class_id']) && $_POST['class_id'] == $id) ? 'selected' : ''; ?>>
                                                            <?php echo htmlspecialchars($name); ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <a href="classes.php" class="btn btn-outline-secondary" title="ক্লাস ব্যবস্থাপনা">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Student Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">শিক্ষার্থীর তথ্য</h4>
                                    
                                    <div class="mb-3">
                                        <label for="student_id" class="form-label">শিক্ষার্থী আইডি*</label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo isset($_POST['student_id']) ? htmlspecialchars($_POST['student_id']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="roll_number" class="form-label">রোল নম্বর</label>
                                        <input type="text" class="form-control" id="roll_number" name="roll_number" value="<?php echo isset($_POST['roll_number']) ? htmlspecialchars($_POST['roll_number']) : ''; ?>">
                                        <small class="form-text text-muted">শিক্ষার্থীর ক্লাস রোল নম্বর</small>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">নামের প্রথম অংশ*</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">নামের শেষ অংশ*</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন নম্বর*</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">ঠিকানা*</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                                    </div>
                                </div>
                                
                                <!-- Academic Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">একাডেমিক তথ্য</h4>
                                    
                                    <div class="mb-3">
                                        <label for="dob" class="form-label">জন্ম তারিখ*</label>
                                        <input type="date" class="form-control" id="dob" name="dob" value="<?php echo isset($_POST['dob']) ? htmlspecialchars($_POST['dob']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">লিঙ্গ*</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="Male" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Male') ? 'selected' : ''; ?>>পুরুষ</option>
                                            <option value="Female" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Female') ? 'selected' : ''; ?>>মহিলা</option>
                                            <option value="Other" <?php echo (isset($_POST['gender']) && $_POST['gender'] === 'Other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="batch" class="form-label">ব্যাচ*</label>
                                        <input type="text" class="form-control" id="batch" name="batch" value="<?php echo isset($_POST['batch']) ? htmlspecialchars($_POST['batch']) : ''; ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="admission_date" class="form-label">ভর্তির তারিখ*</label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" value="<?php echo isset($_POST['admission_date']) ? htmlspecialchars($_POST['admission_date']) : date('Y-m-d'); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="student_role" class="form-label">শিক্ষার্থী রোল</label>
                                        <select class="form-select" id="student_role" name="student_role">
                                            <option value="">নির্বাচন করুন</option>
                                            <option value="Class Captain" <?php echo (isset($_POST['student_role']) && $_POST['student_role'] === 'Class Captain') ? 'selected' : ''; ?>>ক্লাস ক্যাপ্টেন</option>
                                            <option value="Class Representative" <?php echo (isset($_POST['student_role']) && $_POST['student_role'] === 'Class Representative') ? 'selected' : ''; ?>>ক্লাস প্রতিনিধি</option>
                                            <option value="Class Monitor" <?php echo (isset($_POST['student_role']) && $_POST['student_role'] === 'Class Monitor') ? 'selected' : ''; ?>>ক্লাস মনিটর</option>
                                            <option value="Student Council Member" <?php echo (isset($_POST['student_role']) && $_POST['student_role'] === 'Student Council Member') ? 'selected' : ''; ?>>ছাত্র সংসদ সদস্য</option>
                                            <option value="Regular Student" <?php echo (isset($_POST['student_role']) && $_POST['student_role'] === 'Regular Student') ? 'selected' : ''; ?>>সাধারণ শিক্ষার্থী</option>
                                        </select>
                                        <small class="form-text text-muted">শিক্ষার্থীর বিশেষ ভূমিকা নির্বাচন করুন (যদি থাকে)</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Account Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">অ্যাকাউন্ট তথ্য</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">ইউজারনেম*</label>
                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">পাসওয়ার্ড*</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            
                            <!-- Guardian Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">অভিভাবকের তথ্য</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_name" class="form-label">নাম</label>
                                    <input type="text" class="form-control" id="guardian_name" name="guardian_name" value="<?php echo isset($_POST['guardian_name']) ? htmlspecialchars($_POST['guardian_name']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_relation" class="form-label">সম্পর্ক</label>
                                    <select class="form-select" id="guardian_relation" name="guardian_relation">
                                        <option value="">নির্বাচন করুন</option>
                                        <option value="Father" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Father') ? 'selected' : ''; ?>>বাবা</option>
                                        <option value="Mother" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Mother') ? 'selected' : ''; ?>>মা</option>
                                        <option value="Brother" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Brother') ? 'selected' : ''; ?>>ভাই</option>
                                        <option value="Sister" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Sister') ? 'selected' : ''; ?>>বোন</option>
                                        <option value="Uncle" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Uncle') ? 'selected' : ''; ?>>চাচা/মামা</option>
                                        <option value="Aunt" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Aunt') ? 'selected' : ''; ?>>চাচী/মামী</option>
                                        <option value="Other" <?php echo (isset($_POST['guardian_relation']) && $_POST['guardian_relation'] === 'Other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_phone" class="form-label">ফোন নম্বর</label>
                                    <input type="tel" class="form-control" id="guardian_phone" name="guardian_phone" value="<?php echo isset($_POST['guardian_phone']) ? htmlspecialchars($_POST['guardian_phone']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_email" class="form-label">ইমেইল</label>
                                    <input type="email" class="form-control" id="guardian_email" name="guardian_email" value="<?php echo isset($_POST['guardian_email']) ? htmlspecialchars($_POST['guardian_email']) : ''; ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_address" class="form-label">ঠিকানা</label>
                                    <div class="mb-2 form-check">
                                        <input type="checkbox" class="form-check-input" id="same_address" onclick="copyAddress()">
                                        <label class="form-check-label" for="same_address">শিক্ষার্থীর ঠিকানার সাথে একই</label>
                                    </div>
                                    <textarea class="form-control" id="guardian_address" name="guardian_address" rows="3"><?php echo isset($_POST['guardian_address']) ? htmlspecialchars($_POST['guardian_address']) : ''; ?></textarea>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="guardian_occupation" class="form-label">পেশা</label>
                                    <input type="text" class="form-control" id="guardian_occupation" name="guardian_occupation" value="<?php echo isset($_POST['guardian_occupation']) ? htmlspecialchars($_POST['guardian_occupation']) : ''; ?>">
                                </div>
                            </div>
                            
                            <!-- Profile Photo -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">প্রোফাইল ছবি</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="profile-photo-preview" id="photoPreview">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <label for="profile_photo" class="photo-label">ছবি আপলোড করুন</label>
                                    <input type="file" class="form-control" id="profile_photo" name="profile_photo" accept="image/*" onchange="previewImage(this)">
                                    <small class="form-text text-muted">সর্বাধিক ফাইল সাইজ: 5MB। অনুমোদিত ফরম্যাট: JPG, JPEG, PNG, GIF</small>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="text-end">
                                <button type="reset" class="btn btn-secondary">রিসেট</button>
                                <button type="submit" class="btn btn-primary">শিক্ষার্থী যোগ করুন</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Function to update class options based on selected department - removed
        
        // Initialize class options on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add form validation
            const form = document.querySelector('form');
            form.addEventListener('submit', function(event) {
                let isValid = true;
                let errorMessage = '';
                
                // Required fields validation
                const requiredFields = [
                    { id: 'student_id', label: 'শিক্ষার্থী আইডি' },
                    { id: 'first_name', label: 'নামের প্রথম অংশ' },
                    { id: 'last_name', label: 'নামের শেষ অংশ' },
                    { id: 'phone', label: 'ফোন নম্বর' },
                    { id: 'address', label: 'ঠিকানা' },
                    { id: 'dob', label: 'জন্ম তারিখ' },
                    { id: 'gender', label: 'লিঙ্গ' },
                    { id: 'batch', label: 'ব্যাচ' },
                    { id: 'admission_date', label: 'ভর্তির তারিখ' },
                    { id: 'department_id', label: 'বিভাগ' },
                    { id: 'username', label: 'ইউজারনেম' },
                    { id: 'password', label: 'পাসওয়ার্ড' }
                ];
                
                // Check all required fields
                requiredFields.forEach(field => {
                    const input = document.getElementById(field.id);
                    if (!input.value.trim()) {
                        isValid = false;
                        errorMessage += `${field.label} প্রয়োজনীয়।<br>`;
                        input.classList.add('is-invalid');
                    } else {
                        input.classList.remove('is-invalid');
                    }
                });
                
                // Email validation - optional but must be valid if provided
                const emailInput = document.getElementById('email');
                const emailValue = emailInput.value.trim();
                if (emailValue && !isValidEmail(emailValue)) {
                    isValid = false;
                    errorMessage += 'সঠিক ইমেইল প্রদান করুন।<br>';
                    emailInput.classList.add('is-invalid');
                } else {
                    emailInput.classList.remove('is-invalid');
                }
                
                // Phone validation
                const phoneInput = document.getElementById('phone');
                const phoneValue = phoneInput.value.trim();
                if (phoneValue && !isValidPhone(phoneValue)) {
                    isValid = false;
                    errorMessage += 'সঠিক ফোন নম্বর প্রদান করুন।<br>';
                    phoneInput.classList.add('is-invalid');
                }
                
                // Guardian email validation if provided
                const guardianEmailInput = document.getElementById('guardian_email');
                const guardianEmailValue = guardianEmailInput.value.trim();
                if (guardianEmailValue && !isValidEmail(guardianEmailValue)) {
                    isValid = false;
                    errorMessage += 'অভিভাবকের সঠিক ইমেইল প্রদান করুন।<br>';
                    guardianEmailInput.classList.add('is-invalid');
                } else {
                    guardianEmailInput.classList.remove('is-invalid');
                }
                
                // If validation fails, prevent form submission and show error
                if (!isValid) {
                    event.preventDefault();
                    const errorContainer = document.createElement('div');
                    errorContainer.className = 'alert alert-danger';
                    errorContainer.innerHTML = errorMessage;
                    
                    // Remove any existing error alerts
                    const existingAlerts = document.querySelectorAll('.alert-danger');
                    existingAlerts.forEach(alert => alert.remove());
                    
                    // Insert error message at the top of the form
                    form.parentNode.insertBefore(errorContainer, form);
                    
                    // Scroll to top of the form
                    window.scrollTo(0, 0);
                }
            });
        });
        
        // Function to preview profile photo before upload
        function previewImage(input) {
            const preview = document.getElementById('photoPreview');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    preview.innerHTML = '<img src="' + e.target.result + '" alt="Profile Preview">';
                }
                
                reader.readAsDataURL(input.files[0]);
            } else {
                preview.innerHTML = '<i class="fas fa-user"></i>';
            }
        }
        
        // Function to copy student's address to guardian address
        function copyAddress() {
            if (document.getElementById('same_address').checked) {
                document.getElementById('guardian_address').value = document.getElementById('address').value;
            } else {
                document.getElementById('guardian_address').value = '';
            }
        }
        
        // Helper validation functions
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function isValidPhone(phone) {
            // Basic phone validation for Bangladesh numbers
            const phoneRegex = /^(\+?88)?0?1[3-9]\d{8}$/;
            return phoneRegex.test(phone);
        }
    </script>
</body>
</html> 