<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

require_once '../../includes/dbh.inc.php';

header('Content-Type: application/json');

// Get request parameters
$type = isset($_GET['type']) ? $_GET['type'] : '';
$feeTypeId = isset($_GET['fee_type_id']) ? (int)$_GET['fee_type_id'] : 0;

$response = ['success' => false, 'amount' => 0];

try {
    switch ($type) {
        case 'class':
            $classId = isset($_GET['class_id']) ? (int)$_GET['class_id'] : 0;
            
            if ($feeTypeId && $classId) {
                // Get the most recent academic year
                $yearQuery = "SELECT academic_year FROM fee_map_class 
                            WHERE fee_type_id = ? AND class_id = ? 
                            ORDER BY academic_year DESC LIMIT 1";
                $stmt = $conn->prepare($yearQuery);
                $stmt->bind_param("ii", $feeTypeId, $classId);
                $stmt->execute();
                $yearResult = $stmt->get_result();
                
                if ($yearResult && $yearResult->num_rows > 0) {
                    $academicYear = $yearResult->fetch_assoc()['academic_year'];
                    
                    // Get the fee amount for this combination
                    $amountQuery = "SELECT amount FROM fee_map_class 
                                  WHERE fee_type_id = ? AND class_id = ? AND academic_year = ? 
                                  AND is_active = 1 LIMIT 1";
                    $stmt = $conn->prepare($amountQuery);
                    $stmt->bind_param("iis", $feeTypeId, $classId, $academicYear);
                    $stmt->execute();
                    $amountResult = $stmt->get_result();
                    
                    if ($amountResult && $amountResult->num_rows > 0) {
                        $response['amount'] = $amountResult->fetch_assoc()['amount'];
                        $response['success'] = true;
                    }
                }
            }
            break;
            
        case 'session':
            $sessionName = isset($_GET['session']) ? $_GET['session'] : '';
            
            if ($feeTypeId && $sessionName) {
                $query = "SELECT amount FROM fee_map_session 
                        WHERE fee_type_id = ? AND session_name = ? AND is_active = 1 
                        LIMIT 1";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("is", $feeTypeId, $sessionName);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result && $result->num_rows > 0) {
                    $response['amount'] = $result->fetch_assoc()['amount'];
                    $response['success'] = true;
                }
            }
            break;
            
        case 'student':
            $studentId = isset($_GET['student_id']) ? (int)$_GET['student_id'] : 0;
            
            if ($feeTypeId && $studentId) {
                $query = "SELECT amount FROM fee_map_student 
                        WHERE fee_type_id = ? AND student_id = ? AND is_active = 1 
                        LIMIT 1";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ii", $feeTypeId, $studentId);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result && $result->num_rows > 0) {
                    $response['amount'] = $result->fetch_assoc()['amount'];
                    $response['success'] = true;
                } else {
                    // If no student-specific mapping, try to get class-based fee
                    $classQuery = "SELECT class_id FROM students WHERE id = ? LIMIT 1";
                    $stmt = $conn->prepare($classQuery);
                    $stmt->bind_param("i", $studentId);
                    $stmt->execute();
                    $classResult = $stmt->get_result();
                    
                    if ($classResult && $classResult->num_rows > 0) {
                        $classId = $classResult->fetch_assoc()['class_id'];
                        
                        // Get the current academic year or most recent one
                        $yearQuery = "SELECT academic_year FROM fee_map_class 
                                    WHERE fee_type_id = ? AND class_id = ? 
                                    ORDER BY academic_year DESC LIMIT 1";
                        $stmt = $conn->prepare($yearQuery);
                        $stmt->bind_param("ii", $feeTypeId, $classId);
                        $stmt->execute();
                        $yearResult = $stmt->get_result();
                        
                        if ($yearResult && $yearResult->num_rows > 0) {
                            $academicYear = $yearResult->fetch_assoc()['academic_year'];
                            
                            // Get the fee amount for this combination
                            $amountQuery = "SELECT amount FROM fee_map_class 
                                          WHERE fee_type_id = ? AND class_id = ? AND academic_year = ? 
                                          AND is_active = 1 LIMIT 1";
                            $stmt = $conn->prepare($amountQuery);
                            $stmt->bind_param("iis", $feeTypeId, $classId, $academicYear);
                            $stmt->execute();
                            $amountResult = $stmt->get_result();
                            
                            if ($amountResult && $amountResult->num_rows > 0) {
                                $response['amount'] = $amountResult->fetch_assoc()['amount'];
                                $response['success'] = true;
                            }
                        }
                    }
                }
            }
            break;
            
        case 'bulk':
            $targetType = isset($_GET['target_type']) ? $_GET['target_type'] : 'all';
            
            if ($feeTypeId) {
                if ($targetType == 'class' && isset($_GET['class_id'])) {
                    $classId = (int)$_GET['class_id'];
                    
                    // Get the most recent academic year
                    $yearQuery = "SELECT academic_year FROM fee_map_class 
                                WHERE fee_type_id = ? AND class_id = ? 
                                ORDER BY academic_year DESC LIMIT 1";
                    $stmt = $conn->prepare($yearQuery);
                    $stmt->bind_param("ii", $feeTypeId, $classId);
                    $stmt->execute();
                    $yearResult = $stmt->get_result();
                    
                    if ($yearResult && $yearResult->num_rows > 0) {
                        $academicYear = $yearResult->fetch_assoc()['academic_year'];
                        
                        // Get the fee amount
                        $amountQuery = "SELECT amount FROM fee_map_class 
                                      WHERE fee_type_id = ? AND class_id = ? AND academic_year = ? 
                                      AND is_active = 1 LIMIT 1";
                        $stmt = $conn->prepare($amountQuery);
                        $stmt->bind_param("iis", $feeTypeId, $classId, $academicYear);
                        $stmt->execute();
                        $amountResult = $stmt->get_result();
                        
                        if ($amountResult && $amountResult->num_rows > 0) {
                            $response['amount'] = $amountResult->fetch_assoc()['amount'];
                            $response['success'] = true;
                        }
                    }
                } else if ($targetType == 'session' && isset($_GET['session'])) {
                    $sessionName = $_GET['session'];
                    
                    // Get the fee amount
                    $query = "SELECT amount FROM fee_map_session 
                            WHERE fee_type_id = ? AND session_name = ? AND is_active = 1 
                            LIMIT 1";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("is", $feeTypeId, $sessionName);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $result->num_rows > 0) {
                        $response['amount'] = $result->fetch_assoc()['amount'];
                        $response['success'] = true;
                    }
                } else {
                    // Get average amount for this fee type
                    $query = "SELECT AVG(amount) as avg_amount FROM (
                             SELECT amount FROM fee_map_class WHERE fee_type_id = ? AND is_active = 1
                             UNION ALL
                             SELECT amount FROM fee_map_session WHERE fee_type_id = ? AND is_active = 1
                             UNION ALL
                             SELECT amount FROM fee_map_student WHERE fee_type_id = ? AND is_active = 1
                            ) as amounts";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("iii", $feeTypeId, $feeTypeId, $feeTypeId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    
                    if ($result && $result->num_rows > 0) {
                        $avgAmount = $result->fetch_assoc()['avg_amount'];
                        if ($avgAmount) {
                            $response['amount'] = round($avgAmount, 2);
                            $response['success'] = true;
                        }
                    }
                }
            }
            break;
            
        default:
            $response['message'] = 'Invalid request type';
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
}

echo json_encode($response);
?> 