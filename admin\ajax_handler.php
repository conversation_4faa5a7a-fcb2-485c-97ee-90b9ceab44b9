<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Unauthorized access'
    ]);
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get_classes_by_session':
            getClassesBySession($conn);
            break;
            
        case 'get_students_by_class_session':
            getStudentsByClassSession($conn);
            break;
            
        case 'get_students_by_class':
            getStudentsByClass($conn);
            break;
            
        case 'get_student_fees':
            getStudentFees($conn);
            break;
            
        case 'bulk_payment':
            processBulkPayment($conn);
            break;
            
        case 'get_fee_type_amount':
            getFeeTypeAmount($conn);
            break;
            
        case 'get_fee_payment_history':
            getFeePaymentHistory($conn);
            break;
            
        case 'get_fee_details':
            getFeeDetails($conn);
            break;
            
        case 'get_student_payment_history':
            getStudentPaymentHistory($conn);
            break;
            
        case 'collect_fee_payment':
            collectFeePayment($conn);
            break;
            
        case 'get_recent_payments':
            getRecentPayments($conn);
            break;
            
        case 'update_payment':
            updatePayment($conn);
            break;
            
        case 'delete_payment':
            deletePayment($conn);
            break;
            
        case 'delete_fee':
            deleteFee($conn);
            break;
            
        default:
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'error',
                'message' => 'Invalid action'
            ]);
            break;
    }
}

// Check if this is an AJAX request to get subject marks
if (isset($_GET['action']) && $_GET['action'] === 'get_subject_marks') {
    header('Content-Type: application/json');
    
    // Check if subject ID is provided
    if (!isset($_GET['subject_id']) || empty($_GET['subject_id'])) {
        echo json_encode(['success' => false, 'message' => 'Subject ID is required']);
        exit;
    }
    
    $subjectId = intval($_GET['subject_id']);
    
    // Get marks for the selected subject
    $marksQuery = "SELECT marks_type_id, marks_value, passing_marks FROM subject_marks WHERE subject_id = ?";
    $stmt = $conn->prepare($marksQuery);
    $stmt->bind_param("i", $subjectId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $marks = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $marks[] = [
                'marks_type_id' => $row['marks_type_id'],
                'marks_value' => $row['marks_value'],
                'passing_marks' => $row['passing_marks']
            ];
        }
    }
    
    echo json_encode(['success' => true, 'marks' => $marks]);
    exit;
}

/**
 * Get classes for a specific session
 */
function getClassesBySession($conn) {
    $sessionId = $_POST['session_id'] ?? 0;
    
    if (empty($sessionId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Session ID is required'
        ]);
        return;
    }
    
    // Just return all classes for now since class_session relationship might not be set up
    $query = "SELECT * FROM classes ORDER BY class_name";
    $result = $conn->query($query);
    
    $classes = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $classes[] = $row;
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'classes' => $classes
    ]);
}

/**
 * Get students for a specific class and session
 */
function getStudentsByClassSession($conn) {
    $classId = $_POST['class_id'] ?? 0;
    $sessionId = $_POST['session_id'] ?? 0;
    
    if (empty($classId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Class ID is required'
        ]);
        return;
    }
    
    // Enhanced query to get more student details
    $query = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.email, s.phone, s.gender, 
                    c.class_name, ss.session_name, d.department_name 
              FROM students s
              LEFT JOIN classes c ON s.class_id = c.id 
              LEFT JOIN sessions ss ON s.session_id = ss.id
              LEFT JOIN departments d ON s.department_id = d.id
              WHERE s.class_id = ?";
              
    // Add session filter if provided
    if (!empty($sessionId)) {
        $query .= " AND s.session_id = ?";
    }
    
    $query .= " ORDER BY s.first_name, s.last_name";
    
    $stmt = $conn->prepare($query);
    
    if (!empty($sessionId)) {
        $stmt->bind_param("ii", $classId, $sessionId);
    } else {
        $stmt->bind_param("i", $classId);
    }
    
    $stmt->execute();
    $result = $stmt->get_result();
    
    $students = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'status' => 'success',
        'students' => $students,
        'count' => count($students)
    ]);
}

/**
 * Get students for a specific class
 */
function getStudentsByClass($conn) {
    $classId = $_POST['class_id'] ?? 0;
    $sessionId = $_POST['session_id'] ?? 0;
    
    if (empty($classId) || empty($sessionId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'ক্লাস আইডি এবং সেশন আইডি প্রয়োজন'
        ]);
        return;
    }
    
    $query = "SELECT s.id, s.student_id, s.first_name, s.last_name, s.email, s.phone, d.department_name 
              FROM students s 
              LEFT JOIN departments d ON s.department_id = d.id 
              WHERE s.class_id = ? AND s.session_id = ?
              ORDER BY s.first_name, s.last_name";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $classId, $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $students = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $students[] = $row;
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'students' => $students
    ]);
}

/**
 * Get student details and fees
 */
function getStudentFees($conn) {
    $studentId = $_POST['student_id'] ?? 0;
    
    if (empty($studentId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'শিক্ষার্থী আইডি প্রয়োজন'
        ]);
        return;
    }
    
    try {
    // Get student details
    $studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name
                    FROM students s
                    LEFT JOIN classes c ON s.class_id = c.id
                    LEFT JOIN departments d ON s.department_id = d.id
                    LEFT JOIN sessions ss ON s.session_id = ss.id
                    WHERE s.id = ?";
    
    $stmt = $conn->prepare($studentQuery);
        if (!$stmt) {
            throw new Exception("SQL প্রস্তুত করতে সমস্যা হয়েছে: " . $conn->error);
        }
        
    $stmt->bind_param("i", $studentId);
        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("SQL চালাতে সমস্যা হয়েছে: " . $stmt->error);
        }
        
    $studentResult = $stmt->get_result();
    
    if ($studentResult->num_rows === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
                'message' => 'শিক্ষার্থী খুঁজে পাওয়া যায়নি'
        ]);
        return;
    }
    
    $student = $studentResult->fetch_assoc();
    
    // Get student fees
    $feesQuery = "SELECT * FROM fees WHERE student_id = ? ORDER BY due_date DESC";
    $stmt = $conn->prepare($feesQuery);
        if (!$stmt) {
            throw new Exception("ফি তথ্য এসকিউএল প্রস্তুত করতে সমস্যা হয়েছে: " . $conn->error);
        }
        
    $stmt->bind_param("i", $studentId);
        $success = $stmt->execute();
        if (!$success) {
            throw new Exception("ফি তথ্য লোড করতে সমস্যা হয়েছে: " . $stmt->error);
        }
        
    $feesResult = $stmt->get_result();
    
    $fees = [];
    $totalDue = 0;
    
    while ($fee = $feesResult->fetch_assoc()) {
        $due = $fee['amount'] - $fee['paid'];
        $totalDue += $due;
        $fee['due_date'] = date('d/m/Y', strtotime($fee['due_date']));
        $fees[] = $fee;
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'student' => $student,
        'fees' => $fees,
        'total_due' => $totalDue
    ]);
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'ফি তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।',
            'debug_message' => $e->getMessage()
        ]);
    }
}

/**
 * Process bulk payment for multiple fees
 */
function processBulkPayment($conn) {
    $studentId = $_POST['student_id'] ?? 0;
    $feeIds = $_POST['fee_ids'] ?? [];
    $paymentAmounts = $_POST['payment_amounts'] ?? [];
    $paymentMethod = $_POST['payment_method'] ?? '';
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentNote = $_POST['payment_note'] ?? '';
    
    if (empty($studentId) || empty($feeIds) || empty($paymentAmounts) || empty($paymentMethod)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Missing required parameters'
        ]);
        return;
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Create payment receipt
        $receiptQuery = "INSERT INTO payment_receipts (student_id, payment_date, payment_method, total_amount, notes) VALUES (?, ?, ?, 0, ?)";
        $stmt = $conn->prepare($receiptQuery);
        $stmt->bind_param("isss", $studentId, $paymentDate, $paymentMethod, $paymentNote);
        $stmt->execute();
        $receiptId = $conn->insert_id;
        
        $totalAmount = 0;
        
        // Process each fee payment
        for ($i = 0; $i < count($feeIds); $i++) {
            $feeId = $feeIds[$i];
            $paymentAmount = floatval($paymentAmounts[$i]);
            
            if ($paymentAmount <= 0) {
                continue;
            }
            
            // Get current fee info
            $feeQuery = "SELECT * FROM fees WHERE id = ?";
            $stmt = $conn->prepare($feeQuery);
            $stmt->bind_param("i", $feeId);
            $stmt->execute();
            $feeResult = $stmt->get_result();
            $fee = $feeResult->fetch_assoc();
            
            if (!$fee) {
                throw new Exception("Fee not found: {$feeId}");
            }
            
            // Calculate new paid amount and status
            $newPaidAmount = $fee['paid'] + $paymentAmount;
            $newStatus = 'due';
            
            if ($newPaidAmount >= $fee['amount']) {
                $newStatus = 'paid';
            } elseif ($newPaidAmount > 0) {
                $newStatus = 'partial';
            }
            
            // Update fee
            $updateQuery = "UPDATE fees SET paid = ?, payment_status = ?, payment_date = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("dssi", $newPaidAmount, $newStatus, $paymentDate, $feeId);
            $stmt->execute();
            
            // Add payment record
            $paymentQuery = "INSERT INTO fee_payments (fee_id, receipt_id, amount, payment_date, payment_method, notes) 
                           VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($paymentQuery);
            $stmt->bind_param("iidsss", $feeId, $receiptId, $paymentAmount, $paymentDate, $paymentMethod, $paymentNote);
            $stmt->execute();
            
            $totalAmount += $paymentAmount;
        }
        
        // Update receipt total
        $updateReceiptQuery = "UPDATE payment_receipts SET total_amount = ? WHERE id = ?";
        $stmt = $conn->prepare($updateReceiptQuery);
        $stmt->bind_param("di", $totalAmount, $receiptId);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Payment processed successfully',
            'receipt_id' => $receiptId
        ]);
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Error processing payment: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get fee type amount
 */
function getFeeTypeAmount($conn) {
    $feeType = $_POST['fee_type'] ?? '';
    
    if (empty($feeType)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee type is required'
        ]);
        return;
    }
    
    // First get the fee type ID
    $query = "SELECT id FROM fee_types WHERE name = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $feeType);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee type not found',
            'amount' => 0
        ]);
        return;
    }
    
    $feeTypeId = $result->fetch_assoc()['id'];
    
    // Check if fee_types table has an amount column
    $checkQuery = "SHOW COLUMNS FROM fee_types LIKE 'amount'";
    $amountColumnExists = $conn->query($checkQuery);
    
    if ($amountColumnExists && $amountColumnExists->num_rows > 0) {
        // Get amount directly from fee_types
        $query = "SELECT amount FROM fee_types WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $feeTypeId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $amount = $result->fetch_assoc()['amount'];
        } else {
            $amount = 0;
        }
    } else {
        // Try to get amount from fee mapping tables
        
        // First check fee_map_class table
        $queryMapClass = "SELECT AVG(amount) as avg_amount FROM fee_map_class WHERE fee_type_id = ? AND is_active = 1";
        $stmt = $conn->prepare($queryMapClass);
        $stmt->bind_param("i", $feeTypeId);
        $stmt->execute();
        $result = $stmt->get_result();
        $avgClassAmount = $result->fetch_assoc()['avg_amount'];
        
        // Then check fee_map_session table
        $queryMapSession = "SELECT AVG(amount) as avg_amount FROM fee_map_session WHERE fee_type_id = ? AND is_active = 1";
        $stmt = $conn->prepare($queryMapSession);
        $stmt->bind_param("i", $feeTypeId);
        $stmt->execute();
        $result = $stmt->get_result();
        $avgSessionAmount = $result->fetch_assoc()['avg_amount'];
        
        // Finally check fee_map_student table
        $queryMapStudent = "SELECT AVG(amount) as avg_amount FROM fee_map_student WHERE fee_type_id = ? AND is_active = 1";
        $stmt = $conn->prepare($queryMapStudent);
        $stmt->bind_param("i", $feeTypeId);
        $stmt->execute();
        $result = $stmt->get_result();
        $avgStudentAmount = $result->fetch_assoc()['avg_amount'];
        
        // Use the first non-zero amount found
        if ($avgClassAmount > 0) {
            $amount = $avgClassAmount;
        } elseif ($avgSessionAmount > 0) {
            $amount = $avgSessionAmount;
        } elseif ($avgStudentAmount > 0) {
            $amount = $avgStudentAmount;
        } else {
            // As a last resort, check the fees table
            $queryFees = "SELECT AVG(amount) as avg_amount FROM fees WHERE fee_type = ?";
            $stmt = $conn->prepare($queryFees);
            $stmt->bind_param("s", $feeType);
            $stmt->execute();
            $result = $stmt->get_result();
            $avgFeesAmount = $result->fetch_assoc()['avg_amount'];
            
            $amount = $avgFeesAmount > 0 ? $avgFeesAmount : 0;
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'amount' => $amount
    ]);
}

/**
 * Get fee payment history
 */
function getFeePaymentHistory($conn) {
    $feeId = $_POST['fee_id'] ?? 0;
    $studentId = $_POST['student_id'] ?? 0;
    $feeType = $_POST['fee_type'] ?? '';
    
    if (empty($feeId) || empty($studentId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee ID and Student ID are required'
        ]);
        return;
    }
    
    // Check if the fee_payments table exists
    $tableCheckQuery = "SHOW TABLES LIKE 'fee_payments'";
    $tableResult = $conn->query($tableCheckQuery);
    
    $payments = [];
    $monthlyProgress = null;
    
    if ($tableResult && $tableResult->num_rows > 0) {
        // Get fee payment history from fee_payments table
        $paymentsQuery = "SELECT fp.*, pr.payment_method 
                          FROM fee_payments fp
                          LEFT JOIN payment_receipts pr ON fp.receipt_id = pr.id
                          WHERE fp.fee_id = ?
                          ORDER BY fp.payment_date DESC";
        $stmt = $conn->prepare($paymentsQuery);
        $stmt->bind_param("i", $feeId);
        $stmt->execute();
        $paymentsResult = $stmt->get_result();
        
        while ($row = $paymentsResult->fetch_assoc()) {
            $payments[] = $row;
        }
    } else {
        // If fee_payments table doesn't exist, get history info from the fees table
        $feeQuery = "SELECT * FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param("i", $feeId);
        $stmt->execute();
        $feeResult = $stmt->get_result();
        
        if ($feeResult && $feeResult->num_rows > 0) {
            $fee = $feeResult->fetch_assoc();
            
            // Only add to payments if there was any payment
            if ($fee['paid'] > 0) {
                $payments[] = [
                    'payment_date' => $fee['payment_date'] ?? $fee['created_at'] ?? date('Y-m-d'),
                    'amount' => $fee['paid'],
                    'payment_method' => 'নগদ', // Default method
                    'notes' => ''
                ];
            }
        }
    }
    
    // If this is a monthly fee, get all related monthly fees for this student
    if (strpos($feeType, 'মাসিক বেতন') !== false) {
        $monthlyProgress = [];
        
        // Get all monthly fees for this student
        $monthlyFeesQuery = "SELECT * FROM fees 
                           WHERE student_id = ? 
                           AND fee_type LIKE 'মাসিক বেতন%'
                           ORDER BY fee_type";
        $stmt = $conn->prepare($monthlyFeesQuery);
        $stmt->bind_param("i", $studentId);
        $stmt->execute();
        $monthlyFeesResult = $stmt->get_result();
        
        while ($monthlyFee = $monthlyFeesResult->fetch_assoc()) {
            $monthlyProgress[$monthlyFee['fee_type']] = [
                'id' => $monthlyFee['id'],
                'amount' => $monthlyFee['amount'],
                'paid' => $monthlyFee['paid'],
                'due_date' => $monthlyFee['due_date'],
                'payment_status' => $monthlyFee['payment_status'],
                'payment_date' => $monthlyFee['payment_date'] ?? null
            ];
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'payments' => $payments,
        'monthly_progress' => $monthlyProgress
    ]);
}

/**
 * Get fee details
 */
function getFeeDetails($conn) {
    $feeId = $_POST['fee_id'] ?? 0;
    
    if (empty($feeId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee ID is required'
        ]);
        return;
    }
    
    // Get fee details along with student information
    $query = "SELECT f.*, s.first_name, s.last_name, s.student_id as student_roll, 
              c.class_name, d.department_name
              FROM fees f
              LEFT JOIN students s ON f.student_id = s.id
              LEFT JOIN classes c ON s.class_id = c.id
              LEFT JOIN departments d ON s.department_id = d.id
              WHERE f.id = ?";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $feeId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee not found'
        ]);
        return;
    }
    
    $fee = $result->fetch_assoc();
    
    // Format date
    $fee['due_date'] = date('d/m/Y', strtotime($fee['due_date']));
    
    // Add student name field for convenience
    $fee['student_name'] = $fee['first_name'] . ' ' . $fee['last_name'];
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'fee' => $fee
    ]);
}

/**
 * Get student payment history
 */
function getStudentPaymentHistory($conn) {
    $studentId = $_POST['student_id'] ?? 0;
    
    if (empty($studentId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Student ID is required'
        ]);
        return;
    }
    
    $payments = [];
    
    // Check if fee_payments table exists
    $tableCheckQuery = "SHOW TABLES LIKE 'fee_payments'";
    $tableResult = $conn->query($tableCheckQuery);
    
    if ($tableResult && $tableResult->num_rows > 0) {
        // Get payment history from fee_payments table
        $paymentsQuery = "SELECT fp.*, f.fee_type, pr.payment_method 
                         FROM fee_payments fp
                         LEFT JOIN fees f ON fp.fee_id = f.id
                         LEFT JOIN payment_receipts pr ON fp.receipt_id = pr.id
                         WHERE f.student_id = ?
                         ORDER BY fp.payment_date DESC
                         LIMIT 50";
        $stmt = $conn->prepare($paymentsQuery);
        $stmt->bind_param("i", $studentId);
        $stmt->execute();
        $paymentsResult = $stmt->get_result();
        
        while ($row = $paymentsResult->fetch_assoc()) {
            // Format date to a more readable format
            $row['payment_date'] = date('d/m/Y', strtotime($row['payment_date']));
            $payments[] = $row;
        }
    } else {
        // If fee_payments table doesn't exist, get payment data from fees table
        $feesQuery = "SELECT id, fee_type, paid as amount, payment_date, 'নগদ' as payment_method 
                     FROM fees 
                     WHERE student_id = ? AND paid > 0
                     ORDER BY payment_date DESC
                     LIMIT 50";
        $stmt = $conn->prepare($feesQuery);
        $stmt->bind_param("i", $studentId);
        $stmt->execute();
        $feesResult = $stmt->get_result();
        
        while ($row = $feesResult->fetch_assoc()) {
            // Format date to a more readable format
            $row['payment_date'] = date('d/m/Y', strtotime($row['payment_date'] ?? date('Y-m-d')));
            $payments[] = $row;
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'payments' => $payments
    ]);
}

/**
 * Process fee payment collection
 */
function collectFeePayment($conn) {
    $feeId = $_POST['fee_id'] ?? 0;
    $paymentAmount = floatval($_POST['payment_amount'] ?? 0);
    $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $receiptNo = $_POST['receipt_no'] ?? '';
    $notes = $_POST['notes'] ?? '';
    
    // Debug information
    error_log("Payment processing for fee ID: $feeId, Amount: $paymentAmount, Date: $paymentDate, Method: $paymentMethod");
    
    if (empty($feeId) || $paymentAmount <= 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid fee ID or payment amount'
        ]);
        return;
    }
    
    // Check if payment_date column exists in fees table, add it if it doesn't
    $checkPaymentDateColumn = "SHOW COLUMNS FROM fees LIKE 'payment_date'";
    $columnResult = $conn->query($checkPaymentDateColumn);
    
    if (!$columnResult || $columnResult->num_rows == 0) {
        // Column doesn't exist, add it
        $addColumn = "ALTER TABLE fees ADD COLUMN payment_date DATE NULL";
        $conn->query($addColumn);
        error_log("Added payment_date column to fees table");
    }
    
    // Get fee details
    $feeQuery = "SELECT f.*, s.first_name, s.last_name, s.student_id as student_roll, 
                c.class_name
                FROM fees f
                LEFT JOIN students s ON f.student_id = s.id
                LEFT JOIN classes c ON s.class_id = c.id
                WHERE f.id = ?";
    
    $stmt = $conn->prepare($feeQuery);
    $stmt->bind_param("i", $feeId);
    $stmt->execute();
    $feeResult = $stmt->get_result();
    
    if ($feeResult->num_rows === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Fee not found'
        ]);
        return;
    }
    
    $fee = $feeResult->fetch_assoc();
    $currentPaid = floatval($fee['paid']);
    $totalAmount = floatval($fee['amount']);
    $newPaidTotal = $currentPaid + $paymentAmount;
    $studentId = $fee['student_id'];
    $studentName = $fee['first_name'] . ' ' . $fee['last_name'];
    
    // Verify payment amount doesn't exceed due amount
    if ($newPaidTotal > $totalAmount) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'Payment amount exceeds due amount'
        ]);
        return;
    }
    
    // Determine new payment status
    $newPaymentStatus = 'due';
    if ($newPaidTotal >= $totalAmount) {
        $newPaymentStatus = 'paid';
    } else if ($newPaidTotal > 0) {
        $newPaymentStatus = 'partial';
    }
    
    // Start transaction
    $conn->begin_transaction();
    
    try {
        // Generate receipt number if not provided
        if (empty($receiptNo)) {
            $receiptNo = generateReceiptNumber();
        }
        
        // 1. Update the fee record directly - This is the most important part
        $updateQuery = "UPDATE fees 
                      SET paid = ?, payment_status = ?, payment_date = ? 
                      WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("dssi", $newPaidTotal, $newPaymentStatus, $paymentDate, $feeId);
        
        if (!$stmt->execute()) {
            throw new Exception("Failed to update fee record: " . $stmt->error);
        }
        
        // 2. Create receipt record
        $receiptId = null;
        
        // Ensure payment_receipts table exists
        $createReceiptTable = "CREATE TABLE IF NOT EXISTS payment_receipts (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            student_id INT(11) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL,
            receipt_no VARCHAR(50) NULL,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createReceiptTable);
        
        // Insert receipt record
        $receiptQuery = "INSERT INTO payment_receipts 
                       (student_id, payment_date, payment_method, total_amount, notes, receipt_no) 
                       VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($receiptQuery);
        $stmt->bind_param("issdss", $studentId, $paymentDate, $paymentMethod, $paymentAmount, $notes, $receiptNo);
        
        if ($stmt->execute()) {
            $receiptId = $conn->insert_id;
            error_log("Created receipt record ID: $receiptId with receipt_no: $receiptNo");
        } else {
            error_log("Failed to create receipt: " . $stmt->error);
        }
        
        // 3. Log payment in fee_payments table - ensure table exists first
        $createPaymentTable = "CREATE TABLE IF NOT EXISTS fee_payments (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            fee_id INT(11) NOT NULL,
            receipt_id INT(11) NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_date DATE NOT NULL,
            payment_method VARCHAR(50) NULL,
            notes TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $conn->query($createPaymentTable);
        
        // Check if receipt_id column exists in fee_payments table
        $checkReceiptIdColumn = "SHOW COLUMNS FROM fee_payments LIKE 'receipt_id'";
        $receiptIdColumnResult = $conn->query($checkReceiptIdColumn);
        
        if (!$receiptIdColumnResult || $receiptIdColumnResult->num_rows == 0) {
            // Column doesn't exist, add it
            $addReceiptIdColumn = "ALTER TABLE fee_payments ADD COLUMN receipt_id INT(11) NULL AFTER fee_id";
            $conn->query($addReceiptIdColumn);
            error_log("Added receipt_id column to fee_payments table");
        }
        
        // Check if payment_method column exists in fee_payments table
        $checkPaymentMethodColumn = "SHOW COLUMNS FROM fee_payments LIKE 'payment_method'";
        $paymentMethodColumnResult = $conn->query($checkPaymentMethodColumn);
        
        if (!$paymentMethodColumnResult || $paymentMethodColumnResult->num_rows == 0) {
            // Column doesn't exist, add it
            $addPaymentMethodColumn = "ALTER TABLE fee_payments ADD COLUMN payment_method VARCHAR(50) NULL AFTER payment_date";
            $conn->query($addPaymentMethodColumn);
            error_log("Added payment_method column to fee_payments table");
        }
        
        // Check if notes column exists in fee_payments table
        $checkNotesColumn = "SHOW COLUMNS FROM fee_payments LIKE 'notes'";
        $notesColumnResult = $conn->query($checkNotesColumn);
        
        if (!$notesColumnResult || $notesColumnResult->num_rows == 0) {
            // Column doesn't exist, add it
            $addNotesColumn = "ALTER TABLE fee_payments ADD COLUMN notes TEXT NULL AFTER payment_method";
            $conn->query($addNotesColumn);
            error_log("Added notes column to fee_payments table");
        }
        
        // Prepare SQL based on existing columns
        if ($receiptIdColumnResult && $receiptIdColumnResult->num_rows > 0 && 
            $paymentMethodColumnResult && $paymentMethodColumnResult->num_rows > 0 && 
            $notesColumnResult && $notesColumnResult->num_rows > 0) {
            
            // Insert payment record with all fields
        $paymentQuery = "INSERT INTO fee_payments (fee_id, receipt_id, amount, payment_date, payment_method, notes) 
                       VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($paymentQuery);
        $stmt->bind_param("iidsss", $feeId, $receiptId, $paymentAmount, $paymentDate, $paymentMethod, $notes);
        } else {
            // Insert with only basic fields that definitely exist
            $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date) 
                            VALUES (?, ?, ?)";
            $stmt = $conn->prepare($paymentQuery);
            $stmt->bind_param("ids", $feeId, $paymentAmount, $paymentDate);
        }
        
        if (!$stmt->execute()) {
            error_log("Failed to log payment: " . $stmt->error);
            throw new Exception("Failed to log payment: " . $stmt->error);
        } else {
            $paymentId = $conn->insert_id;
            error_log("Created payment record ID: $paymentId for fee ID: $feeId with receipt ID: $receiptId");
        }
                
        // Commit the transaction
        $conn->commit();
        
        // Return success response
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'Payment processed successfully',
            'data' => [
                'receipt_id' => $receiptId ?? 0,
                'receipt_no' => $receiptNo,
                'fee_id' => $feeId,
                'student_id' => $fee['student_roll'],
                'student_name' => $studentName,
                'class_name' => $fee['class_name'],
                'fee_type' => $fee['fee_type'],
                'amount' => $paymentAmount,
                'payment_date' => date('d/m/Y', strtotime($paymentDate)),
                'payment_method' => $paymentMethod,
                'notes' => $notes,
                'payment_status' => $newPaymentStatus
            ]
        ]);
        
    } catch (Exception $e) {
        // Rollback in case of error
        $conn->rollback();
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'পেমেন্ট প্রক্রিয়াকরণে সমস্যা: ' . $e->getMessage()
        ]);
    }
}

/**
 * Generate a unique receipt number
 */
function generateReceiptNumber() {
    // Format: REC-[DATE]-[RANDOM]
    return 'REC-' . date('Ymd') . '-' . mt_rand(1000, 9999);
}

/**
 * Get recent payments for the payment history section
 */
function getRecentPayments($conn) {
    $dateFrom = $_POST['date_from'] ?? '';
    $dateTo = $_POST['date_to'] ?? '';
    $paymentMethod = $_POST['payment_method'] ?? '';
    
    error_log("Fetching payment history from: $dateFrom to: $dateTo method: $paymentMethod");
    
    try {
    $payments = [];
        
        // First check if payment_date column exists in fees table
        $checkPaymentDateColumn = "SHOW COLUMNS FROM fees LIKE 'payment_date'";
        $paymentDateColumnExists = $conn->query($checkPaymentDateColumn);
        
        if (!$paymentDateColumnExists || $paymentDateColumnExists->num_rows == 0) {
            // Add payment_date column if it doesn't exist
            $addColumn = "ALTER TABLE fees ADD COLUMN payment_date DATE NULL";
            $conn->query($addColumn);
            error_log("Added payment_date column to fees table");
        }
    
    // Check if fee_payments table exists
    $tableCheckQuery = "SHOW TABLES LIKE 'fee_payments'";
    $tableResult = $conn->query($tableCheckQuery);
    
        // APPROACH 1: Get data from fees table directly (simpler and more reliable)
        // Get all fees with payments
        $query = "SELECT 
                  f.id, 
                    f.fee_type, 
                  f.paid as amount, 
                  f.payment_date, 
                  f.payment_status,
                    f.student_id,
                  COALESCE(f.payment_date, f.updated_at, f.created_at) AS sorted_date
                  FROM fees f
                  WHERE f.paid > 0";
        
        // Add filters
        if (!empty($dateFrom)) {
            $query .= " AND (f.payment_date >= '$dateFrom' OR (f.payment_date IS NULL AND f.updated_at >= '$dateFrom'))";
        }
        
        if (!empty($dateTo)) {
            $query .= " AND (f.payment_date <= '$dateTo' OR (f.payment_date IS NULL AND f.updated_at <= '$dateTo'))";
        }
        
        $query .= " ORDER BY sorted_date DESC LIMIT 50";
        
        error_log("Executing fees query: $query");
        $result = $conn->query($query);
        
        if (!$result) {
            throw new Exception("Fees query error: " . $conn->error);
        }
            
            if ($result->num_rows > 0) {
            error_log("Found " . $result->num_rows . " payments from fees table");
            
            while ($fee = $result->fetch_assoc()) {
                // Get student information
                if (!empty($fee['student_id'])) {
                    $studentQuery = "SELECT s.first_name, s.last_name, s.student_id as student_roll 
                                    FROM students s 
                                    WHERE s.id = ?";
                            $stmt = $conn->prepare($studentQuery);
                    $stmt->bind_param("i", $fee['student_id']);
                            $stmt->execute();
                            $studentResult = $stmt->get_result();
                    
                            if ($studentResult && $studentResult->num_rows > 0) {
                                $student = $studentResult->fetch_assoc();
                        $studentName = trim($student['first_name'] . ' ' . $student['last_name']);
                        $studentRoll = $student['student_roll'];
                    } else {
                        $studentName = 'N/A';
                        $studentRoll = 'N/A';
                    }
                } else {
                    $studentName = 'N/A';
                    $studentRoll = 'N/A';
                }
                
                // Create payment record
                $payment = [
                    'id' => $fee['id'],
                    'fee_type' => $fee['fee_type'],
                    'amount' => $fee['amount'],
                    'payment_date' => $fee['payment_date'] ? 
                                      date('d/m/Y', strtotime($fee['payment_date'])) : 
                                      date('d/m/Y', strtotime($fee['sorted_date'])),
                    'payment_date_raw' => $fee['payment_date'] ? 
                                      $fee['payment_date'] : 
                                      date('Y-m-d', strtotime($fee['sorted_date'])),
                    'payment_method' => 'cash', // Default method
                    'receipt_no' => 'FEE-' . $fee['id'],
                    'student_name' => $studentName,
                    'student_roll' => $studentRoll,
                    'fee_id' => $fee['id']
                ];
                
                $payments[] = $payment;
                }
            } else {
            error_log("No payments found in fees table");
        }
        
        // APPROACH 2 (only if needed): Get data from fee_payments if it exists and approach 1 returned no results
        if (empty($payments) && $tableResult && $tableResult->num_rows > 0) {
            error_log("Trying fee_payments table as fallback");
            
            // First check payment_date column exists in fee_payments
            $checkFPPaymentDateColumn = "SHOW COLUMNS FROM fee_payments LIKE 'payment_date'";
            $fpPaymentDateExists = $conn->query($checkFPPaymentDateColumn);
            
            if ($fpPaymentDateExists && $fpPaymentDateExists->num_rows > 0) {
                // Use a simpler query with minimal joins
                $fpQuery = "SELECT fp.id, fp.fee_id, fp.amount, fp.payment_date, f.fee_type, f.student_id
                           FROM fee_payments fp
                           LEFT JOIN fees f ON fp.fee_id = f.id
                           WHERE 1=1";
        
        // Add filters
        if (!empty($dateFrom)) {
                    $fpQuery .= " AND fp.payment_date >= '$dateFrom'";
        }
        
        if (!empty($dateTo)) {
                    $fpQuery .= " AND fp.payment_date <= '$dateTo'";
                }
                
                $fpQuery .= " ORDER BY fp.payment_date DESC, fp.id DESC LIMIT 50";
                
                error_log("Executing fee_payments query: $fpQuery");
                $fpResult = $conn->query($fpQuery);
                
                if ($fpResult && $fpResult->num_rows > 0) {
                    error_log("Found " . $fpResult->num_rows . " payments from fee_payments table");
                    
                    while ($fpPayment = $fpResult->fetch_assoc()) {
                        // Get student information
                        if (!empty($fpPayment['student_id'])) {
                            $studentQuery = "SELECT s.first_name, s.last_name, s.student_id as student_roll 
                                            FROM students s 
                                            WHERE s.id = ?";
                            $stmt = $conn->prepare($studentQuery);
                            $stmt->bind_param("i", $fpPayment['student_id']);
                            $stmt->execute();
                            $studentResult = $stmt->get_result();
                            
                            if ($studentResult && $studentResult->num_rows > 0) {
                                $student = $studentResult->fetch_assoc();
                                $studentName = trim($student['first_name'] . ' ' . $student['last_name']);
                                $studentRoll = $student['student_roll'];
            } else {
                                $studentName = 'N/A';
                                $studentRoll = 'N/A';
            }
        } else {
                            $studentName = 'N/A';
                            $studentRoll = 'N/A';
                        }
                        
                        // Create payment record
                        $payment = [
                            'id' => $fpPayment['id'],
                            'fee_id' => $fpPayment['fee_id'],
                            'fee_type' => $fpPayment['fee_type'] ?? 'N/A',
                            'amount' => $fpPayment['amount'],
                            'payment_date' => date('d/m/Y', strtotime($fpPayment['payment_date'])),
                            'payment_date_raw' => $fpPayment['payment_date'] ? 
                                                  $fpPayment['payment_date'] : 
                                                  date('Y-m-d', strtotime($fpPayment['payment_date'])),
                            'payment_method' => 'cash', // Default method
                            'receipt_no' => 'FP-' . $fpPayment['id'],
                            'student_name' => $studentName,
                            'student_roll' => $studentRoll,
                            'fee_id' => $fpPayment['fee_id']
                        ];
                        
                        $payments[] = $payment;
                    }
                }
            }
        }
        
        // Log how many payments we're returning
        error_log("Returning " . count($payments) . " payment records");
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
            'payments' => $payments
        ]);
        
    } catch (Exception $e) {
        error_log("Error in getRecentPayments: " . $e->getMessage());
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'পেমেন্ট ইতিহাস লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।',
            'debug_message' => $e->getMessage()
        ]);
    }
}

/**
 * Update a payment record
 */
function updatePayment($conn) {
    $paymentId = $_POST['payment_id'] ?? 0;
    $feeId = $_POST['fee_id'] ?? 0;
    $amount = floatval($_POST['payment_amount'] ?? 0);
    $paymentDate = $_POST['payment_date'] ?? '';
    $paymentMethod = $_POST['payment_method'] ?? 'cash';
    $receiptNo = $_POST['receipt_no'] ?? '';
    
    // Validate inputs
    if (empty($paymentId) || empty($feeId) || $amount <= 0 || empty($paymentDate)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'প্রয়োজনীয় ফিল্ড অনুপস্থিত'
        ]);
        return;
    }
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        // Get the original payment amount
        $getOriginalQuery = "SELECT amount FROM fee_payments WHERE id = ?";
        $stmt = $conn->prepare($getOriginalQuery);
        $stmt->bind_param("i", $paymentId);
    $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            throw new Exception("পেমেন্ট রেকর্ড পাওয়া যায়নি");
        }
        
        $originalAmount = $result->fetch_assoc()['amount'];
        $amountDifference = $amount - $originalAmount;
        
        // Update the fee_payments record
        $updatePaymentQuery = "UPDATE fee_payments SET 
                              amount = ?, 
                              payment_date = ?, 
                              payment_method = ?,
                              receipt_no = ?
                              WHERE id = ?";
        $stmt = $conn->prepare($updatePaymentQuery);
        $stmt->bind_param("dsssi", $amount, $paymentDate, $paymentMethod, $receiptNo, $paymentId);
    $stmt->execute();
        
        // Update the fees record total paid amount
        if ($amountDifference != 0) {
            // Get current fee info
            $feeQuery = "SELECT amount, paid FROM fees WHERE id = ?";
            $stmt = $conn->prepare($feeQuery);
            $stmt->bind_param("i", $feeId);
            $stmt->execute();
            $feeResult = $stmt->get_result();
            
            if ($feeResult->num_rows === 0) {
                throw new Exception("ফি রেকর্ড পাওয়া যায়নি");
            }
            
            $fee = $feeResult->fetch_assoc();
            $newPaidAmount = $fee['paid'] + $amountDifference;
            
            // Ensure paid amount doesn't go below 0 or exceed total
            if ($newPaidAmount < 0) {
                $newPaidAmount = 0;
            } else if ($newPaidAmount > $fee['amount']) {
                $newPaidAmount = $fee['amount'];
            }
            
            // Determine payment status
            $paymentStatus = 'due';
            if ($newPaidAmount >= $fee['amount']) {
                $paymentStatus = 'paid';
            } else if ($newPaidAmount > 0) {
                $paymentStatus = 'partial';
            }
            
            // Update fee record
            $updateFeeQuery = "UPDATE fees SET 
                              paid = ?,
                              payment_status = ?,
                              updated_at = CURRENT_TIMESTAMP
                              WHERE id = ?";
            $stmt = $conn->prepare($updateFeeQuery);
            $stmt->bind_param("dsi", $newPaidAmount, $paymentStatus, $feeId);
            $stmt->execute();
        }
        
        // Commit the transaction
        $conn->commit();
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'পেমেন্ট সফলভাবে আপডেট করা হয়েছে'
        ]);
        
    } catch (Exception $e) {
        // Rollback the transaction
        $conn->rollback();
        
        error_log("Error updating payment: " . $e->getMessage());
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'পেমেন্ট আপডেট করতে সমস্যা: ' . $e->getMessage()
        ]);
    }
}

/**
 * Delete a payment record
 */
function deletePayment($conn) {
    $paymentId = $_POST['payment_id'] ?? 0;
    
    if (empty($paymentId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'পেমেন্ট আইডি অনুপস্থিত'
        ]);
        return;
    }
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        // Get payment and fee info
        $getPaymentQuery = "SELECT fp.amount, fp.fee_id, f.paid 
                           FROM fee_payments fp
                           JOIN fees f ON fp.fee_id = f.id
                           WHERE fp.id = ?";
        $stmt = $conn->prepare($getPaymentQuery);
        $stmt->bind_param("i", $paymentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
            throw new Exception("পেমেন্ট রেকর্ড পাওয়া যায়নি");
        }
        
        $payment = $result->fetch_assoc();
        $feeId = $payment['fee_id'];
        $paymentAmount = $payment['amount'];
        $currentPaid = $payment['paid'];
        
        // Calculate new paid amount
        $newPaidAmount = $currentPaid - $paymentAmount;
        if ($newPaidAmount < 0) {
            $newPaidAmount = 0;
        }
        
        // Get fee info for payment status
        $feeQuery = "SELECT amount FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param("i", $feeId);
        $stmt->execute();
        $feeResult = $stmt->get_result();
        
        if ($feeResult->num_rows === 0) {
            throw new Exception("ফি রেকর্ড পাওয়া যায়নি");
        }
        
        $fee = $feeResult->fetch_assoc();
        
        // Determine payment status
        $paymentStatus = 'due';
        if ($newPaidAmount >= $fee['amount']) {
            $paymentStatus = 'paid';
        } else if ($newPaidAmount > 0) {
            $paymentStatus = 'partial';
        }
        
        // Update fee record
        $updateFeeQuery = "UPDATE fees SET 
                          paid = ?,
                          payment_status = ?,
                          updated_at = CURRENT_TIMESTAMP
                          WHERE id = ?";
        $stmt = $conn->prepare($updateFeeQuery);
        $stmt->bind_param("dsi", $newPaidAmount, $paymentStatus, $feeId);
        $stmt->execute();
        
        // Delete payment record
        $deletePaymentQuery = "DELETE FROM fee_payments WHERE id = ?";
        $stmt = $conn->prepare($deletePaymentQuery);
        $stmt->bind_param("i", $paymentId);
            $stmt->execute();
            
        // Commit the transaction
        $conn->commit();
    
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
            'message' => 'পেমেন্ট সফলভাবে মুছে ফেলা হয়েছে'
        ]);
        
    } catch (Exception $e) {
        // Rollback the transaction
        $conn->rollback();
        
        error_log("Error deleting payment: " . $e->getMessage());
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'পেমেন্ট মুছে ফেলতে সমস্যা: ' . $e->getMessage()
        ]);
    }
}

/**
 * Delete a fee record
 */
function deleteFee($conn) {
    $feeId = $_POST['fee_id'] ?? 0;
    
    if (empty($feeId)) {
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'ফি আইডি অনুপস্থিত'
        ]);
        return;
    }
    
    try {
        // Begin transaction
        $conn->begin_transaction();
        
        // Check if fee exists
        $checkFeeQuery = "SELECT id, student_id FROM fees WHERE id = ?";
        $stmt = $conn->prepare($checkFeeQuery);
    $stmt->bind_param("i", $feeId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
            throw new Exception("ফি রেকর্ড পাওয়া যায়নি");
    }
    
    $fee = $result->fetch_assoc();
        $studentId = $fee['student_id'];
        
        // First delete any associated payment records from fee_payments
        $checkPaymentsQuery = "SELECT COUNT(*) as payment_count FROM fee_payments WHERE fee_id = ?";
        $stmt = $conn->prepare($checkPaymentsQuery);
        $stmt->bind_param("i", $feeId);
        $stmt->execute();
        $result = $stmt->get_result();
        $paymentCount = $result->fetch_assoc()['payment_count'];
        
        if ($paymentCount > 0) {
            // Delete associated payment records
            $deletePaymentsQuery = "DELETE FROM fee_payments WHERE fee_id = ?";
            $stmt = $conn->prepare($deletePaymentsQuery);
            $stmt->bind_param("i", $feeId);
        $stmt->execute();
            
            error_log("Deleted $paymentCount payment records associated with fee ID: $feeId");
        }
        
        // Now delete the fee record
        $deleteFeeQuery = "DELETE FROM fees WHERE id = ?";
        $stmt = $conn->prepare($deleteFeeQuery);
    $stmt->bind_param("i", $feeId);
    $stmt->execute();
        
        if ($stmt->affected_rows === 0) {
            throw new Exception("ফি রেকর্ড মুছতে ব্যর্থ");
        }
                
        // Commit the transaction
        $conn->commit();
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'success',
            'message' => 'ফি রেকর্ড সফলভাবে মুছে ফেলা হয়েছে',
            'student_id' => $studentId
        ]);
        
    } catch (Exception $e) {
        // Rollback the transaction
        $conn->rollback();
        
        error_log("Error deleting fee: " . $e->getMessage());
        
        header('Content-Type: application/json');
        echo json_encode([
            'status' => 'error',
            'message' => 'ফি রেকর্ড মুছে ফেলতে সমস্যা: ' . $e->getMessage()
        ]);
    }
}
?> 