<?php
// <PERSON><PERSON><PERSON> to check and fix the fees table structure
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    die("Unauthorized access. Please log in as admin.");
}

// Check if payment_date column exists in fees table
$checkColumn = "SHOW COLUMNS FROM fees LIKE 'payment_date'";
$columnResult = $conn->query($checkColumn);

if ($columnResult && $columnResult->num_rows == 0) {
    // Column doesn't exist, add it
    echo "<p>Payment date column doesn't exist in fees table. Adding it now...</p>";
    $addColumn = "ALTER TABLE fees ADD COLUMN payment_date DATE NULL";
    
    if ($conn->query($addColumn) === TRUE) {
        echo "<p>Successfully added payment_date column to fees table.</p>";
    } else {
        echo "<p>Error adding column: " . $conn->error . "</p>";
    }
} else {
    echo "<p>Payment date column already exists in fees table.</p>";
}

// Display current fees table structure
echo "<h3>Fees table structure:</h3>";
$columnsQuery = "SHOW COLUMNS FROM fees";
$columnsResult = $conn->query($columnsQuery);

if ($columnsResult && $columnsResult->num_rows > 0) {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($column = $columnsResult->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>Unable to fetch column information.</p>";
}

// Show a sample of fees records
echo "<h3>Sample fees records:</h3>";
$sampleQuery = "SELECT * FROM fees LIMIT 5";
$sampleResult = $conn->query($sampleQuery);

if ($sampleResult && $sampleResult->num_rows > 0) {
    echo "<table border='1'>";
    
    // Get column names for the header
    $firstRow = $sampleResult->fetch_assoc();
    $sampleResult->data_seek(0);
    
    echo "<tr>";
    foreach (array_keys($firstRow) as $columnName) {
        echo "<th>" . $columnName . "</th>";
    }
    echo "</tr>";
    
    // Display records
    while ($row = $sampleResult->fetch_assoc()) {
        echo "<tr>";
        foreach ($row as $value) {
            echo "<td>" . $value . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
} else {
    echo "<p>No fees records found or unable to fetch records.</p>";
}

// Return to admin dashboard
echo "<p><a href='index.php'>Return to Admin Dashboard</a></p>";
?> 