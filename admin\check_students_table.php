<?php
session_start();
include '../config.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

echo "<h1>Students Table Structure</h1>";

// Get table structure 
$result = $conn->query("DESCRIBE students");
if ($result) {
    echo "<h2>Columns in students table:</h2>";
    echo "<ul>";
    while($row = $result->fetch_assoc()) {
        echo "<li><strong>" . $row['Field'] . "</strong> - Type: " . $row['Type'] . "</li>";
    }
    echo "</ul>";
} else {
    echo "Error getting table structure: " . $conn->error;
}

// Get sample data
$sample = $conn->query("SELECT * FROM students LIMIT 1");
if ($sample && $sample->num_rows > 0) {
    echo "<h2>Sample student data:</h2>";
    $student = $sample->fetch_assoc();
    echo "<pre>";
    print_r($student);
    echo "</pre>";
} else {
    echo "No student records found or error: " . $conn->error;
}

$conn->close();
?> 