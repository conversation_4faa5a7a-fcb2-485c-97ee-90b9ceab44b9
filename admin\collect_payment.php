<?php
// Include database connection
include '../includes/dbh.inc.php';

// Check if the payment_amount column exists in the fee_payments table
$columnCheckQuery = "SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'fee_payments' 
                    AND COLUMN_NAME = 'payment_amount'";
$columnResult = $conn->query($columnCheckQuery);

// If the column doesn't exist, add it as an alias to amount
if ($columnResult && $columnResult->num_rows === 0) {
    $addColumnQuery = "ALTER TABLE fee_payments ADD COLUMN payment_amount DECIMAL(10,2) 
                      GENERATED ALWAYS AS (amount) STORED";
    try {
        $conn->query($addColumnQuery);
    } catch (Exception $e) {
        // If virtual column not supported, add a real column with trigger
        $alterQuery = "ALTER TABLE fee_payments ADD COLUMN payment_amount DECIMAL(10,2) AFTER amount";
        $conn->query($alterQuery);
        
        // Add trigger to keep payment_amount synced with amount
        $triggerQuery = "CREATE TRIGGER IF NOT EXISTS sync_amount_payment_amount 
                         BEFORE INSERT ON fee_payments 
                         FOR EACH ROW 
                         SET NEW.payment_amount = NEW.amount";
        $conn->query($triggerQuery);
        
        $updateTriggerQuery = "CREATE TRIGGER IF NOT EXISTS sync_amount_payment_amount_update 
                              BEFORE UPDATE ON fee_payments 
                              FOR EACH ROW 
                              SET NEW.payment_amount = NEW.amount";
        $conn->query($updateTriggerQuery);
        
        // Update existing records
        $updateQuery = "UPDATE fee_payments SET payment_amount = amount";
        $conn->query($updateQuery);
    }
}

// Check if fee ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: fees.php');
    exit;
}

$feeId = $_GET['id'];

// Fetch fee details with student information
$query = "SELECT f.*, s.student_id, s.first_name, s.last_name, c.class_name, 
         f.amount - f.paid as due_amount, 
         f.fee_type
         FROM fees f
         LEFT JOIN students s ON f.student_id = s.id
         LEFT JOIN classes c ON s.class_id = c.id
         WHERE f.id = ?";

$stmt = $conn->prepare($query);
    $stmt->bind_param('i', $feeId);
    $stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: fees.php');
    exit;
}

$fee = $result->fetch_assoc();

// Get payment history
$paymentHistoryQuery = "SELECT * FROM fee_payments WHERE fee_id = ? ORDER BY payment_date DESC";
$paymentStmt = $conn->prepare($paymentHistoryQuery);
$paymentStmt->bind_param('i', $feeId);
$paymentStmt->execute();
$paymentHistory = $paymentStmt->get_result();

// Process payment form if submitted
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_payment') {
    if (isset($_POST['paid_amount']) && is_numeric($_POST['paid_amount'])) {
        $paidAmount = floatval($_POST['paid_amount']);
        $paymentMethod = $_POST['payment_method'] ?? 'cash';
        $notes = $conn->real_escape_string($_POST['notes'] ?? 'Fee payment');
        
        // Calculate total paid amount
        $totalPaid = $fee['paid'] + $paidAmount;
        
        // Determine payment status
        $paymentStatus = 'due';
        if ($totalPaid > $fee['amount']) {
            $paymentStatus = 'overpaid';
        } elseif ($totalPaid == $fee['amount']) {
            $paymentStatus = 'paid';
        } elseif ($totalPaid > 0) {
            $paymentStatus = 'partial';
        }
        
        // Update fee record
        $updateQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param('dsi', $totalPaid, $paymentStatus, $feeId);
        
        if ($updateStmt->execute()) {
            // Record the payment in payment history
            $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, notes) 
                             VALUES (?, ?, NOW(), ?, ?)";
            $paymentStmt = $conn->prepare($paymentQuery);
            $paymentStmt->bind_param('idss', $feeId, $paidAmount, $paymentMethod, $notes);
            $paymentStmt->execute();
            
            $successMessage = "পেমেন্ট সফলভাবে আপডেট করা হয়েছে!";
            
            // Refresh fee data
            $stmt->execute();
            $result = $stmt->get_result();
            $fee = $result->fetch_assoc();
            
            // Refresh payment history
            $paymentStmt = $conn->prepare($paymentHistoryQuery);
            $paymentStmt->bind_param('i', $feeId);
        $paymentStmt->execute();
            $paymentHistory = $paymentStmt->get_result();
        } else {
            $errorMessage = "পেমেন্ট আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } else {
        $errorMessage = "অবৈধ পেমেন্ট পরিমাণ!";
    }
}

// Set page title
$pageTitle = "ফি পরিশোধ করুন";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3f51b5;
            --primary-light: #e8eaf6;
            --primary-dark: #303f9f;
            --success-color: #4caf50;
            --success-light: #e8f5e9;
            --danger-color: #f44336;
            --danger-light: #ffebee;
            --warning-color: #ff9800;
            --warning-light: #fff8e1;
            --card-border-radius: 12px;
            --input-border-radius: 8px;
        }
        
        body {
            background-color: #f9f9fb;
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            color: #333;
        }
        
        .card {
            border-radius: var(--card-border-radius);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.06);
            border: none;
            overflow: hidden;
        }
        
        .card-header {
            font-weight: 600;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .primary-gradient {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }
        
        .section-title {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 1.25rem;
            position: relative;
            padding-left: 15px;
            font-weight: 600;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 4px;
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        .info-card {
            background-color: #fff;
            transition: all 0.3s ease;
        }
        
        .info-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .info-group {
            padding: 12px 0;
            border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
        }
        
        .info-group:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #555;
            font-size: 0.95rem;
        }
        
        .info-value {
            font-weight: 500;
            color: #333;
        }
        
        .amount-card {
            border-radius: 10px;
            padding: 1.25rem;
            height: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .amount-card::after {
            content: '';
            position: absolute;
            right: -15px;
            top: -15px;
            width: 90px;
            height: 90px;
            border-radius: 50%;
            opacity: 0.1;
            z-index: 0;
        }
        
        .amount-card:hover {
            transform: translateY(-5px);
        }
        
        .primary-card {
            background-color: var(--primary-light);
            color: var(--primary-color);
        }
        
        .primary-card::after {
            background-color: var(--primary-color);
        }
        
        .success-card {
            background-color: var(--success-light);
            color: var(--success-color);
        }
        
        .success-card::after {
            background-color: var(--success-color);
        }
        
        .danger-card {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }
        
        .danger-card::after {
            background-color: var(--danger-color);
        }
        
        .amount-label {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }
        
        .amount-value {
            font-size: 1.8rem;
            font-weight: 700;
            position: relative;
            z-index: 1;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 30px;
            font-weight: 600;
            font-size: 0.85rem;
        }
        
        .badge-paid {
            background-color: var(--success-light);
            color: var(--success-color);
        }
        
        .badge-partial {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }
        
        .badge-due {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }
        
        .form-control, .form-select {
            padding: 0.75rem 1rem;
            border-radius: var(--input-border-radius);
            border: 1px solid #e0e0e0;
            font-size: 1rem;
        }
        
        .form-control:focus, .form-select:focus {
            box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.2);
            border-color: var(--primary-color);
        }
        
        .form-label {
            font-weight: 600;
            color: #555;
            margin-bottom: 0.5rem;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            border-radius: var(--input-border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }
        
        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .alert {
            border-radius: var(--card-border-radius);
            border: none;
        }
        
        .payment-history-table th {
            background-color: var(--primary-light);
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        .payment-history-table td, .payment-history-table th {
            padding: 0.75rem 1rem;
            vertical-align: middle;
        }
        
        .payment-method-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        .hover-shadow:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        
        .student-card-status {
            font-size: 0.85rem;
            padding: 0.35rem 0.7rem;
            border-radius: 30px;
            font-weight: 500;
            text-align: center;
            display: inline-block;
            min-width: 90px;
        }
        
        .status-paid {
            background-color: var(--success-light);
            color: var(--success-color);
        }
        
        .status-partial {
            background-color: var(--warning-light);
            color: var(--warning-color);
        }
        
        .status-due {
            background-color: var(--danger-light);
            color: var(--danger-color);
        }
        
        .status-overpaid {
            background-color: #e3f2fd;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <?php if (!empty($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-check-circle fa-2x me-3"></i>
                        <div>
                            <strong>সাফল্য!</strong> <?= $successMessage ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <strong>ত্রুটি!</strong> <?= $errorMessage ?>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="card mb-4 shadow">
                    <div class="card-header primary-gradient text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-hand-holding-usd me-2"></i> ফি পরিশোধ করুন</h5>
                            <a href="fees.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="row mb-4">
                            <!-- Student Information -->
                            <div class="col-md-6 mb-4 mb-md-0">
                                <h6 class="section-title">
                                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী তথ্য
                                </h6>
                                <div class="card info-card hover-shadow">
                                    <div class="card-body">
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">শিক্ষার্থী আইডি</div>
                                                <div class="col-7 info-value"><?= htmlspecialchars($fee['student_id']) ?></div>
                                            </div>
                                        </div>
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">নাম</div>
                                                <div class="col-7 info-value"><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></div>
                                            </div>
                                        </div>
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">ক্লাস</div>
                                                <div class="col-7 info-value"><?= htmlspecialchars($fee['class_name'] ?? 'অনির্দিষ্ট') ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Fee Information -->
                            <div class="col-md-6">
                                <h6 class="section-title">
                                    <i class="fas fa-file-invoice-dollar me-2"></i> ফি তথ্য
                                </h6>
                                <div class="card info-card hover-shadow">
                                    <div class="card-body">
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">ফি টাইপ</div>
                                                <div class="col-7 info-value"><?= htmlspecialchars($fee['fee_type']) ?></div>
                                            </div>
                                        </div>
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">শেষ তারিখ</div>
                                                <div class="col-7 info-value"><?= date('d F, Y', strtotime($fee['due_date'])) ?></div>
                                            </div>
                                        </div>
                                        <div class="info-group">
                                            <div class="row align-items-center">
                                                <div class="col-5 info-label">পেমেন্ট স্টেটাস</div>
                                                <div class="col-7">
                                                    <?php
                                                    $statusClass = 'status-due';
                                                    $statusText = 'বকেয়া';
                                                    
                                                    if ($fee['payment_status'] === 'paid') {
                                                        $statusClass = 'status-paid';
                                                        $statusText = 'পরিশোধিত';
                                                    } elseif ($fee['payment_status'] === 'partial') {
                                                        $statusClass = 'status-partial';
                                                        $statusText = 'আংশিক';
                                                    } elseif ($fee['payment_status'] === 'overpaid') {
                                                        $statusClass = 'status-overpaid';
                                                        $statusText = 'অতিরিক্ত পরিশোধিত';
                                                    }
                                                    ?>
                                                    <span class="student-card-status <?= $statusClass ?>"><?= $statusText ?></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fee Amounts -->
                        <div class="row mb-4">
                            <div class="col-md-4 mb-3 mb-md-0">
                                <div class="amount-card primary-card">
                                    <div class="amount-label">মোট পরিমাণ</div>
                                    <div class="amount-value">৳ <?= number_format($fee['amount'], 2) ?></div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3 mb-md-0">
                                <div class="amount-card success-card">
                                    <div class="amount-label">পরিশোধিত</div>
                                    <div class="amount-value">৳ <?= number_format($fee['paid'], 2) ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="amount-card danger-card">
                                    <div class="amount-label">বকেয়া</div>
                                    <div class="amount-value">৳ <?= number_format($fee['due_amount'], 2) ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Form or Success Message -->
                        <?php if ($fee['payment_status'] !== 'paid'): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card shadow-sm">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i> নতুন পেমেন্ট যোগ করুন</h6>
                                    </div>
                                    <div class="card-body">
                                        <form method="POST" action="" class="row g-3">
                                            <input type="hidden" name="action" value="update_payment">
                                            <input type="hidden" name="fee_id" value="<?= $feeId ?>">
                                            
                                            <div class="col-md-6">
                                                <label for="paid_amount" class="form-label">পরিশোধিত পরিমাণ</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">৳</span>
                                                    <input type="number" step="0.01" min="0" max="<?= $fee['due_amount'] ?>" 
                                                        class="form-control form-control-lg" id="paid_amount" name="paid_amount" 
                                                        value="<?= $fee['due_amount'] ?>" required>
                                                </div>
                                                <div class="form-text">সর্বোচ্চ বকেয়া: ৳ <?= number_format($fee['due_amount'], 2) ?></div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                                <select class="form-select form-select-lg" id="payment_method" name="payment_method">
                                                    <option value="cash">নগদ</option>
                                                    <option value="bank">ব্যাংক ট্রান্সফার</option>
                                                    <option value="bkash">বিকাশ</option>
                                                    <option value="nagad">নগদ</option>
                                                    <option value="rocket">রকেট</option>
                                                    <option value="other">অন্যান্য</option>
                                                </select>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label for="notes" class="form-label">নোট (ঐচ্ছিক)</label>
                                                <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="পেমেন্ট সম্পর্কে যেকোনো অতিরিক্ত তথ্য লিখুন..."></textarea>
                                            </div>
                                            
                                            <div class="col-12 mt-4">
                                                <button type="submit" class="btn btn-primary btn-lg w-100">
                                                    <i class="fas fa-hand-holding-usd me-2"></i> পেমেন্ট আপডেট করুন
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="card bg-success-light shadow-sm mb-4">
                            <div class="card-body text-center p-4">
                                <div class="py-3">
                                    <i class="fas fa-check-circle text-success fa-4x mb-3"></i>
                                    <h4 class="text-success">এই ফি পুরোপুরি পরিশোধ করা হয়েছে!</h4>
                                    <p class="mb-0">ফির সম্পূর্ণ পরিমাণ পরিশোধ করা হয়েছে। আর কোন পেমেন্ট প্রয়োজন নেই।</p>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Payment History -->
                        <?php if($paymentHistory && $paymentHistory->num_rows > 0): ?>
                        <div class="mt-4">
                            <h6 class="section-title mb-3">
                                <i class="fas fa-history me-2"></i> পেমেন্ট ইতিহাস
                            </h6>
                            <div class="card shadow-sm">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0 payment-history-table">
                                        <thead>
                                            <tr>
                                                <th scope="col" width="50">#</th>
                                                <th scope="col">তারিখ</th>
                                                <th scope="col">পরিমাণ</th>
                                                <th scope="col">পেমেন্ট পদ্ধতি</th>
                                                <th scope="col">নোট</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php
                                            $counter = 1;
                                            while($payment = $paymentHistory->fetch_assoc()):
                                                $paymentMethod = $payment['payment_method'];
                                                $methodDisplay = $paymentMethod;
                                                switch($paymentMethod) {
                                                    case 'cash': $methodDisplay = 'নগদ'; break;
                                                    case 'bank': $methodDisplay = 'ব্যাংক ট্রান্সফার'; break;
                                                    case 'bkash': $methodDisplay = 'বিকাশ'; break;
                                                    case 'nagad': $methodDisplay = 'নগদ'; break;
                                                    case 'rocket': $methodDisplay = 'রকেট'; break;
                                                }
                                            ?>
                                            <tr>
                                                <td><?= $counter++ ?></td>
                                                <td><?= date('d M, Y', strtotime($payment['payment_date'])) ?></td>
                                                <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                                <td><span class="payment-method-badge"><?= $methodDisplay ?></span></td>
                                                <td><?= htmlspecialchars($payment['notes'] ?? '-') ?></td>
                                            </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 