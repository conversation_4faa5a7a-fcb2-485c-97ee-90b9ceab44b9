<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['create_exam'])) {
    // Collect form data
    $exam_name = trim($_POST['exam_name']);
    $course_name = trim($_POST['course_name']);
    $class_id = intval($_POST['class_id']);
    $exam_date = $_POST['exam_date'];
    $total_marks = intval($_POST['total_marks']);
    $description = trim($_POST['description']);
    $status = $_POST['status'];
    $start_time = trim($_POST['start_time']);
    $end_time = trim($_POST['end_time']);
    $exam_type = trim($_POST['exam_type']);
    $pass_marks = intval($_POST['pass_marks']);
    
    // Validation
    $errors = [];
    
    if (empty($exam_name)) {
        $errors[] = "পরীক্ষার নাম প্রয়োজন";
    }
    
    if (empty($course_name)) {
        $errors[] = "বিষয়ের নাম প্রয়োজন";
    }
    
    if (empty($class_id)) {
        $errors[] = "শ্রেণী নির্বাচন করুন";
    }
    
    if (empty($exam_date)) {
        $errors[] = "পরীক্ষার তারিখ নির্বাচন করুন";
    }
    
    if (empty($total_marks) || $total_marks <= 0) {
        $errors[] = "বৈধ মোট নম্বর প্রদান করুন";
    }
    
    if (empty($pass_marks) || $pass_marks <= 0 || $pass_marks > $total_marks) {
        $errors[] = "বৈধ পাস মার্কস প্রদান করুন";
    }
    
    // Process if no errors
    if (empty($errors)) {
        // Check if exam_type, start_time, end_time, pass_marks columns exist in the table
        $columns_to_check = [
            'exam_type' => 'VARCHAR(50) DEFAULT NULL',
            'start_time' => 'TIME DEFAULT NULL',
            'end_time' => 'TIME DEFAULT NULL',
            'pass_marks' => 'INT NOT NULL DEFAULT 33'
        ];
        
        foreach ($columns_to_check as $column => $definition) {
            $check_column = "SHOW COLUMNS FROM exams LIKE '$column'";
            $column_result = $conn->query($check_column);
            
            if ($column_result->num_rows == 0) {
                // Column doesn't exist, add it
                $add_column = "ALTER TABLE exams ADD COLUMN $column $definition";
                $conn->query($add_column);
            }
        }
        
        // Prepare SQL statement for insert
        $sql = "INSERT INTO exams (exam_name, course_name, class_id, exam_date, total_marks, description, status, exam_type, start_time, end_time, pass_marks) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssisssssssi", $exam_name, $course_name, $class_id, $exam_date, $total_marks, $description, $status, $exam_type, $start_time, $end_time, $pass_marks);
        
        if ($stmt->execute()) {
            $success_message = "পরীক্ষা সফলভাবে যোগ করা হয়েছে!";
            // Get the ID of the newly created exam
            $new_exam_id = $stmt->insert_id;
            
            // Redirect to subject assignment page if required
            if (isset($_POST['assign_subjects']) && $_POST['assign_subjects'] == 'yes') {
                header("Location: exam_subject_assign.php?exam_id=$new_exam_id");
                exit();
            }
        } else {
            $error_message = "পরীক্ষা যোগ করতে সমস্যা হয়েছে: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        $error_message = "পরীক্ষা যোগ করতে সমস্যা হয়েছে:<br>" . implode("<br>", $errors);
    }
}

// Get all classes for dropdown
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);

// Get all subjects for autocomplete/suggestions
$subjects_sql = "SELECT DISTINCT subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_sql);
$subjects = [];
if ($subjects_result->num_rows > 0) {
    while ($subject = $subjects_result->fetch_assoc()) {
        $subjects[] = $subject['subject_name'];
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নতুন পরীক্ষা তৈরি করুন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .form-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 30px;
            margin-bottom: 30px;
        }
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .form-section h4 {
            margin-bottom: 20px;
            color: #333;
            font-size: 1.2rem;
        }
        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .card-header-custom {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            margin: -30px -30px 25px -30px;
            padding: 20px 30px;
            border-radius: 8px 8px 0 0;
        }
        .help-text {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .required-field::after {
            content: "*";
            color: red;
            margin-left: 5px;
        }
        .action-buttons {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
        }
        .suggestions-container {
            position: absolute;
            background-color: white;
            border: 1px solid #ddd;
            width: 100%;
            max-height: 150px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .suggestion-item {
            padding: 8px 12px;
            cursor: pointer;
        }
        .suggestion-item:hover {
            background-color: #f0f0f0;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">নতুন পরীক্ষা তৈরি করুন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> পরীক্ষা ব্যবস্থাপনায় ফিরে যান
                        </a>
                    </div>
                </div>
                
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="form-card">
                    <div class="card-header-custom">
                        <h3><i class="fas fa-pencil-alt me-2"></i>পরীক্ষার তথ্য</h3>
                        <p class="text-muted mb-0">নতুন পরীক্ষা তৈরি করতে নিচের ফর্মটি পূরণ করুন</p>
                    </div>
                    
                    <form method="post" action="">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <h4>মৌলিক তথ্য</h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="exam_name" class="form-label required-field">পরীক্ষার নাম</label>
                                    <input type="text" class="form-control" id="exam_name" name="exam_name" required>
                                    <div class="help-text">উদাহরণ: প্রথম সামায়িক পরীক্ষা, বার্ষিক পরীক্ষা</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="exam_type" class="form-label">পরীক্ষার ধরন</label>
                                    <select class="form-select" id="exam_type" name="exam_type">
                                        <option value="">পরীক্ষার ধরন নির্বাচন করুন</option>
                                        <option value="সামায়িক">সামায়িক</option>
                                        <option value="অর্ধ-বার্ষিক">অর্ধ-বার্ষিক</option>
                                        <option value="বার্ষিক">বার্ষিক</option>
                                        <option value="মডেল টেস্ট">মডেল টেস্ট</option>
                                        <option value="নির্বাচনী">নির্বাচনী</option>
                                        <option value="সাপ্তাহিক">সাপ্তাহিক</option>
                                        <option value="মাসিক">মাসিক</option>
                                        <option value="অন্যান্য">অন্যান্য</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3 position-relative">
                                    <label for="course_name" class="form-label required-field">বিষয়ের নাম</label>
                                    <input type="text" class="form-control" id="course_name" name="course_name" required autocomplete="off">
                                    <div class="suggestions-container" id="subjectSuggestions"></div>
                                    <div class="help-text">মাল্টি-বিষয়ক পরীক্ষার ক্ষেত্রে 'সকল বিষয়' লিখুন</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="class_id" class="form-label required-field">শ্রেণী</label>
                                    <select class="form-select" id="class_id" name="class_id" required>
                                        <option value="">শ্রেণী নির্বাচন করুন</option>
                                        <?php if ($classes_result && $classes_result->num_rows > 0): ?>
                                            <?php while ($class = $classes_result->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>"><?php echo $class['class_name']; ?></option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Exam Details Section -->
                        <div class="form-section">
                            <h4>পরীক্ষার বিবরণ</h4>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="exam_date" class="form-label required-field">পরীক্ষার তারিখ</label>
                                    <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="start_time" class="form-label">শুরুর সময়</label>
                                    <input type="time" class="form-control" id="start_time" name="start_time">
                                </div>
                                
                                <div class="col-md-4 mb-3">
                                    <label for="end_time" class="form-label">শেষের সময়</label>
                                    <input type="time" class="form-control" id="end_time" name="end_time">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="total_marks" class="form-label required-field">মোট নম্বর</label>
                                    <input type="number" class="form-control" id="total_marks" name="total_marks" required min="1" value="100">
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="pass_marks" class="form-label">পাস মার্কস</label>
                                    <input type="number" class="form-control" id="pass_marks" name="pass_marks" min="1" value="33">
                                    <div class="help-text">সাধারণত মোট নম্বরের ৩৩% বা প্রতিষ্ঠান অনুযায়ী</div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="description" class="form-label">পরীক্ষার বিবরণ</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    <div class="help-text">পরীক্ষা সম্পর্কে অতিরিক্ত তথ্য এখানে লিখুন (ঐচ্ছিক)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Additional Options Section -->
                        <div class="form-section">
                            <h4>অতিরিক্ত বিকল্প</h4>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">পরীক্ষার স্ট্যাটাস</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active">সক্রিয়</option>
                                        <option value="inactive">নিষ্ক্রিয়</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label class="form-label d-block">পরবর্তী পদক্ষেপ</label>
                                    <div class="form-check form-check-inline mt-2">
                                        <input class="form-check-input" type="radio" name="assign_subjects" id="assign_yes" value="yes">
                                        <label class="form-check-label" for="assign_yes">বিষয় সংযুক্ত করুন</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="assign_subjects" id="assign_no" value="no" checked>
                                        <label class="form-check-label" for="assign_no">পরে বিষয় সংযুক্ত করুন</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="exam_dashboard.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> বাতিল করুন
                            </a>
                            <button type="submit" name="create_exam" class="btn btn-primary">
                                <i class="fas fa-save"></i> পরীক্ষা তৈরি করুন
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Set default date to today
            document.getElementById('exam_date').valueAsDate = new Date();
            
            // Subject suggestions
            const subjects = <?php echo json_encode($subjects); ?>;
            const subjectInput = $('#course_name');
            const suggestionsContainer = $('#subjectSuggestions');
            
            subjectInput.on('input', function() {
                const inputValue = $(this).val().toLowerCase();
                let suggestions = '';
                
                if (inputValue.length > 0) {
                    const matchedSubjects = subjects.filter(subject => 
                        subject.toLowerCase().includes(inputValue)
                    );
                    
                    if (matchedSubjects.length > 0) {
                        matchedSubjects.forEach(subject => {
                            suggestions += `<div class="suggestion-item">${subject}</div>`;
                        });
                        
                        suggestionsContainer.html(suggestions);
                        suggestionsContainer.show();
                    } else {
                        suggestionsContainer.hide();
                    }
                } else {
                    suggestionsContainer.hide();
                }
            });
            
            // Handle selection of suggestion
            $(document).on('click', '.suggestion-item', function() {
                subjectInput.val($(this).text());
                suggestionsContainer.hide();
            });
            
            // Hide suggestions when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#course_name, #subjectSuggestions').length) {
                    suggestionsContainer.hide();
                }
            });
            
            // Pass marks calculator
            $('#total_marks').on('input', function() {
                const totalMarks = parseInt($(this).val());
                if (totalMarks > 0) {
                    // Calculate default pass marks (33% of total)
                    const passMarks = Math.ceil(totalMarks * 0.33);
                    $('#pass_marks').val(passMarks);
                }
            });
        });
    </script>
</body>
</html> 