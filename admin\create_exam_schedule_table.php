<?php
// Include database connection
require_once 'includes/db_connection.php';

// Display header
echo "<h2>টেবিল তৈরির স্ক্রিপ্ট</h2>";

// Check if tables exist first
echo "<h3>বর্তমান টেবিলের তালিকা:</h3>";
$tables_query = "SHOW TABLES";
$tables_result = $conn->query($tables_query);

echo "<ul>";
if ($tables_result->num_rows > 0) {
    while($table = $tables_result->fetch_row()) {
        echo "<li>". $table[0] ."</li>";
    }
} else {
    echo "<li>কোন টেবিল পাওয়া যায়নি</li>";
}
echo "</ul>";

// Check if exam_schedule table already exists
$table_check = $conn->query("SHOW TABLES LIKE 'exam_schedule'");
if ($table_check->num_rows > 0) {
    echo "<p style='color:orange'>exam_schedule টেবিল ইতিমধ্যে বিদ্যমান আছে। আমরা এটি ড্রপ করে পুনরায় তৈরি করছি।</p>";
    
    // Drop the table
    $drop_result = $conn->query("DROP TABLE IF EXISTS `exam_schedule`");
    if($drop_result) {
        echo "<p style='color:green'>পুরাতন টেবিল সফলভাবে মুছে ফেলা হয়েছে।</p>";
    } else {
        echo "<p style='color:red'>টেবিল ড্রপ করতে সমস্যা: " . $conn->error . "</p>";
    }
}

// Create exam_schedule table
$sql = "CREATE TABLE IF NOT EXISTS `exam_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) NOT NULL,
  `subject_id` int(11) NOT NULL,
  `exam_date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `room_no` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `exam_id` (`exam_id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

if ($conn->query($sql) === TRUE) {
    echo "<p style='color:green'>exam_schedule টেবিল সফলভাবে তৈরি হয়েছে!</p>";
    
    // Check if we can add foreign keys
    try {
        $add_foreign_keys = "ALTER TABLE `exam_schedule` 
            ADD CONSTRAINT `exam_schedule_ibfk_1` FOREIGN KEY (`exam_id`) REFERENCES `exams` (`id`) ON DELETE CASCADE,
            ADD CONSTRAINT `exam_schedule_ibfk_2` FOREIGN KEY (`subject_id`) REFERENCES `subjects` (`id`) ON DELETE CASCADE";
        
        if ($conn->query($add_foreign_keys) === TRUE) {
            echo "<p style='color:green'>ফরেন কি সফলভাবে যোগ করা হয়েছে।</p>";
        } else {
            echo "<p style='color:orange'>ফরেন কি যোগ করতে ব্যর্থ: " . $conn->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color:orange'>ফরেন কি যোগ করতে ব্যর্থ: " . $e->getMessage() . "</p>";
    }
    
    // Now let's check if 'exams' and 'subjects' tables exist
    echo "<h3>নির্ভরশীল টেবিল:</h3>";
    $exams_check = $conn->query("SHOW TABLES LIKE 'exams'");
    if ($exams_check->num_rows > 0) {
        echo "<p style='color:green'>exams টেবিল বিদ্যমান আছে।</p>";
    } else {
        echo "<p style='color:red'>exams টেবিল পাওয়া যায়নি! এটি তৈরি করুন।</p>";
    }
    
    $subjects_check = $conn->query("SHOW TABLES LIKE 'subjects'");
    if ($subjects_check->num_rows > 0) {
        echo "<p style='color:green'>subjects টেবিল বিদ্যমান আছে।</p>";
    } else {
        echo "<p style='color:red'>subjects টেবিল পাওয়া যায়নি! এটি তৈরি করুন।</p>";
    }
} else {
    echo "<p style='color:red'>টেবিল তৈরি করার সময় ত্রুটি: " . $conn->error . "</p>";
}

// Close connection
$conn->close();

echo "<p><a href='exam_schedule.php'>পরীক্ষার সময়সূচী পেইজে ফিরে যান</a></p>";
?> 