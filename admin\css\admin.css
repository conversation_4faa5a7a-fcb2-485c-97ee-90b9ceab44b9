/* Admin Dashboard Styles */

/* Sidebar Styles */
.sidebar {
    background-color: #2c3e50;
    color: white;
    height: 100vh;
    position: fixed;
    overflow-y: auto;
    padding-top: 20px;
    padding-bottom: 60px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    transition: all 0.3s ease;
    z-index: 100;
    width: 16.66%; /* Match col-md-2 width */
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    margin-bottom: 5px;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Main Content styles */
.main-content {
    margin-left: 16.66%; /* col-md-2 width */
    padding-left: 25px;
    padding-right: 25px;
    padding-top: 20px;
    padding-bottom: 20px;
    transition: all 0.3s ease;
    width: 83.33%; /* Match col-md-10 width */
}

.col-md-9, .col-lg-10 {
    margin-left: auto;
    padding-left: 15px;
    padding-right: 15px;
}

/* Media queries for responsiveness */
@media (max-width: 991.98px) {
    .sidebar {
        width: 25%; /* Match col-md-3 width */
    }
    
    .main-content {
        margin-left: 25%;
        width: 75%;
    }
}

@media (max-width: 767.98px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
}

/* Custom card styles */
.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Table styles */
.table tbody tr {
    transition: all 0.2s;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

/* Button styles */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    transition: all 0.3s;
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions .btn {
    margin: 5px;
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Search container */
.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

/* Table actions */
.table-actions {
    white-space: nowrap;
} 