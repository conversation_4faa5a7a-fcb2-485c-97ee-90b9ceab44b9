<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if form was submitted with a payment ID
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $paymentId = intval($_POST['id']);
    
    // Get payment details before deletion
    $paymentQuery = "SELECT fp.*, f.paid as fee_paid FROM fee_payments fp
                    JOIN fees f ON fp.fee_id = f.id
                    WHERE fp.id = ?";
    $stmt = $conn->prepare($paymentQuery);
    $stmt->bind_param('i', $paymentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        $_SESSION['error'] = 'পেমেন্ট রেকর্ড খুঁজে পাওয়া যায়নি!';
        header('Location: payment_dashboard.php');
        exit();
    }
    
    $payment = $result->fetch_assoc();
    $feeId = $payment['fee_id'];
    $paymentAmount = $payment['amount'];
    
    // Get the fee details
    $feeQuery = "SELECT * FROM fees WHERE id = ?";
    $stmt = $conn->prepare($feeQuery);
    $stmt->bind_param('i', $feeId);
    $stmt->execute();
    $feeResult = $stmt->get_result();
    $fee = $feeResult->fetch_assoc();
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Delete the payment
        $deleteQuery = "DELETE FROM fee_payments WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param('i', $paymentId);
        $stmt->execute();
        
        // Update the fee record
        $newPaidAmount = max(0, $fee['paid'] - $paymentAmount);
        $newPaymentStatus = 'due';
        
        if ($newPaidAmount >= $fee['amount']) {
            $newPaymentStatus = 'paid';
        } elseif ($newPaidAmount > 0) {
            $newPaymentStatus = 'partial';
        }
        
        $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
        $stmt = $conn->prepare($updateFeeQuery);
        $stmt->bind_param('dsi', $newPaidAmount, $newPaymentStatus, $feeId);
        $stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        $_SESSION['success'] = 'পেমেন্ট সফলভাবে মুছে ফেলা হয়েছে!';
        header('Location: payment_dashboard.php');
        exit();
        
    } catch (Exception $e) {
        // Roll back transaction on error
        $conn->rollback();
        $_SESSION['error'] = 'পেমেন্ট মুছতে সমস্যা: ' . $e->getMessage();
        header('Location: payment_dashboard.php');
        exit();
    }
    
} else {
    // If not POST request or no ID provided, redirect to dashboard
    header('Location: payment_dashboard.php');
    exit();
}
?> 