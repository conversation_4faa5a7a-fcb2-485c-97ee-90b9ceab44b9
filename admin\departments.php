<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create departments table if it doesn't exist
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($departmentsTableQuery);

// Update students table to add department_id if it doesn't exist
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM students LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE students ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Handle department addition
if (isset($_POST['add_department'])) {
    $departmentName = $conn->real_escape_string($_POST['department_name']);
    $description = $conn->real_escape_string($_POST['description'] ?? '');
    
    if (empty($departmentName)) {
        $error_msg = "বিভাগের নাম আবশ্যক";
    } else {
        $insertQuery = "INSERT INTO departments (department_name, description) VALUES ('$departmentName', '$description')";
        
        if ($conn->query($insertQuery)) {
            $success_msg = "বিভাগ সফলভাবে যোগ করা হয়েছে";
        } else {
            $error_msg = "বিভাগ যোগ করতে সমস্যা: " . $conn->error;
        }
    }
}

// Handle department deletion
if (isset($_GET['delete'])) {
    $departmentId = $_GET['delete'];
    
    // Check if any students are in this department
    $checkStudentsQuery = "SELECT COUNT(*) as count FROM students WHERE department_id = ?";
    $stmt = $conn->prepare($checkStudentsQuery);
    $stmt->bind_param("i", $departmentId);
    $stmt->execute();
    $result = $stmt->get_result();
    $studentCount = $result->fetch_assoc()['count'];
    
    if ($studentCount > 0) {
        $error_msg = "বিভাগ মুছতে পারবেন না কারণ $studentCount জন শিক্ষার্থী এই বিভাগে নিযুক্ত আছে";
    } else {
        $deleteQuery = "DELETE FROM departments WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $departmentId);
        
        if ($stmt->execute()) {
            $success_msg = "বিভাগ সফলভাবে মুছে ফেলা হয়েছে";
        } else {
            $error_msg = "বিভাগ মুছতে সমস্যা: " . $conn->error;
        }
    }
}

// Get all departments
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিভাগ ব্যবস্থাপনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <h2 class="text-white text-center mb-4">অ্যাডমিন প্যানেল</h2>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php" class="active"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <a href="subjects.php"><i class="fas fa-book-open me-2"></i> বিষয়</a>
                <a href="../includes/logout.inc.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 content">
                <h1 class="mb-4">বিভাগ ব্যবস্থাপনা</h1>
                
                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add Department Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">নতুন বিভাগ যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="departments.php">
                                    <div class="mb-3">
                                        <label for="department_name" class="form-label">বিভাগের নাম*</label>
                                        <input type="text" class="form-control" id="department_name" name="department_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">বিবরণ</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    <button type="submit" name="add_department" class="btn btn-primary">বিভাগ যোগ করুন</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Departments List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বিভাগের তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>বিভাগের নাম</th>
                                                <th>বিবরণ</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($dept['description'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <a href="edit_department.php?id=<?php echo $dept['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="departments.php?delete=<?php echo $dept['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই বিভাগটি মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন বিভাগ পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 