<?php
session_start();
include '../config.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if detailed_results table exists, create if it doesn't
$conn->query("CREATE TABLE IF NOT EXISTS detailed_results (
    id INT AUTO_INCREMENT PRIMARY KEY,
    result_id INT,
    cq_marks DECIMAL(10,2) DEFAULT 0,
    mcq_marks DECIMAL(10,2) DEFAULT 0,
    practical_marks DECIMAL(10,2) DEFAULT 0,
    FOREIGN KEY (result_id) REFERENCES results(id) ON DELETE CASCADE
)");

// Process form submission for marks entry
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_detailed_marks'])) {
    $examId = $_POST['exam_id'];
    $studentId = $_POST['student_id'];
    $cq_marks = isset($_POST['cq_marks']) ? $_POST['cq_marks'] : 0;
    $mcq_marks = isset($_POST['mcq_marks']) ? $_POST['mcq_marks'] : 0;
    $practical_marks = isset($_POST['practical_marks']) ? $_POST['practical_marks'] : 0;
    
    // Debug info - store in a variable to display later
    $debug_info = "Processing marks submission: Student ID: $studentId, Exam ID: $examId, CQ: $cq_marks, MCQ: $mcq_marks, Practical: $practical_marks";
    
    // Convert to float to ensure they are numeric
    $cq_marks = floatval($cq_marks);
    $mcq_marks = floatval($mcq_marks);
    $practical_marks = floatval($practical_marks);
    
    $total_marks = $cq_marks + $mcq_marks + $practical_marks;
    
    // Calculate grade based on total marks
    // Get total marks for the exam
    $examTotalQuery = "SELECT total_marks FROM exams WHERE id = ?";
    $examTotalStmt = $conn->prepare($examTotalQuery);
    $examTotalStmt->bind_param("i", $examId);
    $examTotalStmt->execute();
    $examTotalResult = $examTotalStmt->get_result();
    $examTotal = $examTotalResult->fetch_assoc();
    $examTotalMarks = $examTotal['total_marks'];
    
    // Calculate grade based on percentage
    $percentage = ($total_marks / $examTotalMarks) * 100;
    $grade = '';
    
    if ($percentage >= 80) {
        $grade = 'A+';
    } elseif ($percentage >= 70) {
        $grade = 'A';
    } elseif ($percentage >= 60) {
        $grade = 'A-';
    } elseif ($percentage >= 50) {
        $grade = 'B';
    } elseif ($percentage >= 40) {
        $grade = 'C';
    } elseif ($percentage >= 33) {
        $grade = 'D';
    } else {
        $grade = 'F';
    }
    $examTotalStmt->close();
    
    // Check if marks already exist for this student and exam
    $checkSql = "SELECT * FROM results WHERE exam_id = ? AND student_id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("ii", $examId, $studentId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows > 0) {
        // Update existing marks
        $resultRow = $result->fetch_assoc();
        $resultId = $resultRow['id'];
        
        // Update main results table
        $updateSql = "UPDATE results SET marks_obtained = ?, grade = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);
        $updateStmt->bind_param("dsi", $total_marks, $grade, $resultId);
        
        if ($updateStmt->execute()) {
            // Check if detailed result exists
            $checkDetailSql = "SELECT * FROM detailed_results WHERE result_id = ?";
            $checkDetailStmt = $conn->prepare($checkDetailSql);
            $checkDetailStmt->bind_param("i", $resultId);
            $checkDetailStmt->execute();
            $detailResult = $checkDetailStmt->get_result();
            
            if ($detailResult->num_rows > 0) {
                // Update detailed results
                $updateDetailSql = "UPDATE detailed_results SET cq_marks = ?, mcq_marks = ?, practical_marks = ? WHERE result_id = ?";
                $updateDetailStmt = $conn->prepare($updateDetailSql);
                $updateDetailStmt->bind_param("dddi", $cq_marks, $mcq_marks, $practical_marks, $resultId);
                
                if ($updateDetailStmt->execute()) {
                    $success_message = "বিস্তারিত মার্কস সফলভাবে আপডেট করা হয়েছে!";
                } else {
                    $error_message = "বিস্তারিত মার্কস আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
                }
                $updateDetailStmt->close();
            } else {
                // Insert detailed results
                $insertDetailSql = "INSERT INTO detailed_results (result_id, cq_marks, mcq_marks, practical_marks) VALUES (?, ?, ?, ?)";
                $insertDetailStmt = $conn->prepare($insertDetailSql);
                $insertDetailStmt->bind_param("iddd", $resultId, $cq_marks, $mcq_marks, $practical_marks);
                
                if ($insertDetailStmt->execute()) {
                    $success_message = "বিস্তারিত মার্কস সফলভাবে সংরক্ষণ করা হয়েছে!";
                } else {
                    $error_message = "বিস্তারিত মার্কস সংরক্ষণ করতে সমস্যা হয়েছে: " . $conn->error;
                }
                $insertDetailStmt->close();
            }
            $checkDetailStmt->close();
        } else {
            $error_message = "মূল মার্কস আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
        $updateStmt->close();
    } else {
        // Insert new marks
        $insertSql = "INSERT INTO results (exam_id, student_id, marks_obtained, grade, created_at) VALUES (?, ?, ?, ?, NOW())";
        $insertStmt = $conn->prepare($insertSql);
        $insertStmt->bind_param("iids", $examId, $studentId, $total_marks, $grade);
        
        if ($insertStmt->execute()) {
            $resultId = $conn->insert_id;
            
            // Insert detailed results
            $insertDetailSql = "INSERT INTO detailed_results (result_id, cq_marks, mcq_marks, practical_marks) VALUES (?, ?, ?, ?)";
            $insertDetailStmt = $conn->prepare($insertDetailSql);
            $insertDetailStmt->bind_param("iddd", $resultId, $cq_marks, $mcq_marks, $practical_marks);
            
            if ($insertDetailStmt->execute()) {
                $success_message = "বিস্তারিত মার্কস সফলভাবে সংরক্ষণ করা হয়েছে!";
            } else {
                $error_message = "বিস্তারিত মার্কস সংরক্ষণ করতে সমস্যা হয়েছে: " . $conn->error;
            }
            $insertDetailStmt->close();
        } else {
            $error_message = "মূল মার্কস সংরক্ষণ করতে সমস্যা হয়েছে: " . $conn->error;
        }
        $insertStmt->close();
    }
    $checkStmt->close();
}

// Process batch detailed marks submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_batch_detailed_marks'])) {
    $examId = $_POST['batch_exam_id'];
    $classId = $_POST['class_id'];
    
    if (isset($_POST['student_cq_marks']) && is_array($_POST['student_cq_marks']) &&
        isset($_POST['student_mcq_marks']) && is_array($_POST['student_mcq_marks']) &&
        isset($_POST['student_practical_marks']) && is_array($_POST['student_practical_marks'])) {
        
        $success_count = 0;
        $error_count = 0;
        
        foreach ($_POST['student_cq_marks'] as $studentId => $cq_marks) {
            $mcq_marks = isset($_POST['student_mcq_marks'][$studentId]) ? $_POST['student_mcq_marks'][$studentId] : 0;
            $practical_marks = isset($_POST['student_practical_marks'][$studentId]) ? $_POST['student_practical_marks'][$studentId] : 0;
            
            // Convert to float to ensure they are numeric
            $cq_marks = floatval($cq_marks);
            $mcq_marks = floatval($mcq_marks);
            $practical_marks = floatval($practical_marks);
            
            $total_marks = $cq_marks + $mcq_marks + $practical_marks;
            
            // Calculate grade based on total marks
            // Get total marks for the exam
            $examTotalQuery = "SELECT total_marks FROM exams WHERE id = ?";
            $examTotalStmt = $conn->prepare($examTotalQuery);
            $examTotalStmt->bind_param("i", $examId);
            $examTotalStmt->execute();
            $examTotalResult = $examTotalStmt->get_result();
            $examTotal = $examTotalResult->fetch_assoc();
            $examTotalMarks = $examTotal['total_marks'];
            
            // Calculate grade based on percentage
            $percentage = ($total_marks / $examTotalMarks) * 100;
            $grade = '';
            
            if ($percentage >= 80) {
                $grade = 'A+';
            } elseif ($percentage >= 70) {
                $grade = 'A';
            } elseif ($percentage >= 60) {
                $grade = 'A-';
            } elseif ($percentage >= 50) {
                $grade = 'B';
            } elseif ($percentage >= 40) {
                $grade = 'C';
            } elseif ($percentage >= 33) {
                $grade = 'D';
            } else {
                $grade = 'F';
            }
            $examTotalStmt->close();
            
            // Check if marks already exist
            $checkSql = "SELECT * FROM results WHERE exam_id = ? AND student_id = ?";
            $checkStmt = $conn->prepare($checkSql);
            $checkStmt->bind_param("ii", $examId, $studentId);
            $checkStmt->execute();
            $result = $checkStmt->get_result();
            
            if ($result->num_rows > 0) {
                // Update existing marks
                $resultRow = $result->fetch_assoc();
                $resultId = $resultRow['id'];
                
                // Update main results table
                $updateSql = "UPDATE results SET marks_obtained = ?, grade = ? WHERE id = ?";
                $updateStmt = $conn->prepare($updateSql);
                $updateStmt->bind_param("dsi", $total_marks, $grade, $resultId);
                
                if ($updateStmt->execute()) {
                    // Check if detailed result exists
                    $checkDetailSql = "SELECT * FROM detailed_results WHERE result_id = ?";
                    $checkDetailStmt = $conn->prepare($checkDetailSql);
                    $checkDetailStmt->bind_param("i", $resultId);
                    $checkDetailStmt->execute();
                    $detailResult = $checkDetailStmt->get_result();
                    
                    if ($detailResult->num_rows > 0) {
                        // Update detailed results
                        $updateDetailSql = "UPDATE detailed_results SET cq_marks = ?, mcq_marks = ?, practical_marks = ? WHERE result_id = ?";
                        $updateDetailStmt = $conn->prepare($updateDetailSql);
                        $updateDetailStmt->bind_param("dddi", $cq_marks, $mcq_marks, $practical_marks, $resultId);
                        
                        if ($updateDetailStmt->execute()) {
                            $success_count++;
                        } else {
                            $error_count++;
                        }
                        $updateDetailStmt->close();
                    } else {
                        // Insert detailed results
                        $insertDetailSql = "INSERT INTO detailed_results (result_id, cq_marks, mcq_marks, practical_marks) VALUES (?, ?, ?, ?)";
                        $insertDetailStmt = $conn->prepare($insertDetailSql);
                        $insertDetailStmt->bind_param("iddd", $resultId, $cq_marks, $mcq_marks, $practical_marks);
                        
                        if ($insertDetailStmt->execute()) {
                            $success_count++;
                        } else {
                            $error_count++;
                        }
                        $insertDetailStmt->close();
                    }
                    $checkDetailStmt->close();
                } else {
                    $error_count++;
                }
                $updateStmt->close();
            } else {
                // Insert new marks
                $insertSql = "INSERT INTO results (exam_id, student_id, marks_obtained, grade, created_at) VALUES (?, ?, ?, ?, NOW())";
                $insertStmt = $conn->prepare($insertSql);
                $insertStmt->bind_param("iids", $examId, $studentId, $total_marks, $grade);
                
                if ($insertStmt->execute()) {
                    $resultId = $conn->insert_id;
                    
                    // Insert detailed results
                    $insertDetailSql = "INSERT INTO detailed_results (result_id, cq_marks, mcq_marks, practical_marks) VALUES (?, ?, ?, ?)";
                    $insertDetailStmt = $conn->prepare($insertDetailSql);
                    $insertDetailStmt->bind_param("iddd", $resultId, $cq_marks, $mcq_marks, $practical_marks);
                    
                    if ($insertDetailStmt->execute()) {
                        $success_count++;
                    } else {
                        $error_count++;
                    }
                    $insertDetailStmt->close();
                } else {
                    $error_count++;
                }
                $insertStmt->close();
            }
            $checkStmt->close();
        }
        
        if ($success_count > 0) {
            $success_message = "$success_count জন ছাত্র/ছাত্রীর বিস্তারিত মার্কস সফলভাবে যোগ করা হয়েছে!";
            if ($error_count > 0) {
                $success_message .= " কিন্তু $error_count জন ছাত্র/ছাত্রীর মার্কস যোগ করতে সমস্যা হয়েছে।";
            }
        } else if ($error_count > 0) {
            $error_message = "বিস্তারিত মার্কস যোগ করতে সমস্যা হয়েছে।";
        }
    }
}

// Get all exams
$examSql = "SELECT e.*, c.class_name 
            FROM exams e 
            JOIN classes c ON e.class_id = c.id 
            ORDER BY e.exam_date DESC";
$examResult = $conn->query($examSql);

// Get specific student if selected
$selectedStudent = null;
if (isset($_GET['student_id'])) {
    $studentId = $_GET['student_id'];
    $studentSql = "SELECT s.*, c.class_name 
                  FROM students s 
                  JOIN classes c ON s.class_id = c.id 
                  WHERE s.id = ?";
    $studentStmt = $conn->prepare($studentSql);
    $studentStmt->bind_param("i", $studentId);
    $studentStmt->execute();
    $selectedStudent = $studentStmt->get_result()->fetch_assoc();
    $studentStmt->close();
}

// Get existing detailed marks if any
$detailedMarks = null;
if (isset($_GET['student_id']) && isset($_GET['exam_id'])) {
    $studentId = $_GET['student_id'];
    $examId = $_GET['exam_id'];
    
    $marksSql = "SELECT r.*, dr.cq_marks, dr.mcq_marks, dr.practical_marks 
                FROM results r 
                LEFT JOIN detailed_results dr ON r.id = dr.result_id
                WHERE r.student_id = ? AND r.exam_id = ?";
    $marksStmt = $conn->prepare($marksSql);
    $marksStmt->bind_param("ii", $studentId, $examId);
    $marksStmt->execute();
    $detailedMarks = $marksStmt->get_result()->fetch_assoc();
    $marksStmt->close();
    
    // Debug
    if (!$detailedMarks) {
        // Check if there's a result without detailed marks
        $basicMarksSql = "SELECT r.* FROM results r WHERE r.student_id = ? AND r.exam_id = ?";
        $basicMarksStmt = $conn->prepare($basicMarksSql);
        $basicMarksStmt->bind_param("ii", $studentId, $examId);
        $basicMarksStmt->execute();
        $basicResult = $basicMarksStmt->get_result();
        
        if ($basicResult->num_rows > 0) {
            // We have a result entry but no detailed marks
            $detailedMarks = $basicResult->fetch_assoc();
            // Set default values for detailed marks
            $detailedMarks['cq_marks'] = 0;
            $detailedMarks['mcq_marks'] = 0;
            $detailedMarks['practical_marks'] = 0;
        }
        $basicMarksStmt->close();
    }
}

// Get all student marks with detailed breakdown
try {
    $allDetailedMarksSql = "SELECT r.*, e.exam_name, e.course_name, e.total_marks, 
                           s.student_id, c.class_name, 
                           dr.cq_marks, dr.mcq_marks, dr.practical_marks
                          FROM results r 
                          JOIN exams e ON r.exam_id = e.id 
                          JOIN students s ON r.student_id = s.id
                          JOIN classes c ON e.class_id = c.id 
                          LEFT JOIN detailed_results dr ON r.id = dr.result_id
                          ORDER BY r.created_at DESC LIMIT 50";
    $allDetailedMarksResult = $conn->query($allDetailedMarksSql);
} catch (Exception $e) {
    $error_message = "টেবিল আনুসন্ধানে সমস্যা হয়েছে: " . $e->getMessage();
    $allDetailedMarksResult = null;
}

// Get all classes
$classSql = "SELECT * FROM classes ORDER BY class_name";
$classResult = $conn->query($classSql);

// Function to get students by class
function getStudentsByClass($conn, $classId) {
    $sql = "SELECT * FROM students WHERE class_id = ? ORDER BY id";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $result = $stmt->get_result();
    $students = [];
    
    while ($row = $result->fetch_assoc()) {
        $students[] = $row;
    }
    
    $stmt->close();
    return $students;
}

// Get students by class if class is selected for batch entry
$batchStudents = [];
if (isset($_GET['class_id'])) {
    $selectedClassId = $_GET['class_id'];
    $batchStudents = getStudentsByClass($conn, $selectedClassId);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিস্তারিত মার্কস এন্ট্রি - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        @media (max-width: 767.98px) {
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">বিস্তারিত মার্কস এন্ট্রি</h1>
                </div>
                
                <?php if(isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_message; ?>
                        <?php if(isset($debug_info)): ?><div><small><?php echo $debug_info; ?></small></div><?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if(isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_message; ?>
                        <?php if(isset($debug_info)): ?><div><small><?php echo $debug_info; ?></small></div><?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if(isset($debug_info) && !isset($success_message) && !isset($error_message)): ?>
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <div><small><?php echo $debug_info; ?></small></div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <ul class="nav nav-tabs mb-4" id="marksTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo !isset($_GET['class_id']) ? 'active' : ''; ?>" id="individual-tab" data-bs-toggle="tab" data-bs-target="#individual" type="button" role="tab" aria-controls="individual" aria-selected="<?php echo !isset($_GET['class_id']) ? 'true' : 'false'; ?>">ব্যক্তিগত মার্কস এন্ট্রি</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link <?php echo isset($_GET['class_id']) ? 'active' : ''; ?>" id="batch-tab" data-bs-toggle="tab" data-bs-target="#batch" type="button" role="tab" aria-controls="batch" aria-selected="<?php echo isset($_GET['class_id']) ? 'true' : 'false'; ?>">ব্যাচ মার্কস এন্ট্রি</button>
                    </li>
                </ul>
                
                <div class="tab-content" id="marksTabContent">
                    <!-- Individual Marks Entry Tab -->
                    <div class="tab-pane fade <?php echo !isset($_GET['class_id']) ? 'show active' : ''; ?>" id="individual" role="tabpanel" aria-labelledby="individual-tab">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>ছাত্র/ছাত্রী খুঁজুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="">
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="student_search" class="form-label">ছাত্র/ছাত্রী আইডি</label>
                                            <input type="text" class="form-control" id="student_search" name="student_search" placeholder="আইডি দিয়ে অনুসন্ধান করুন">
                                        </div>
                                        <div class="col-md-2 align-self-end">
                                            <button type="submit" class="btn btn-primary">অনুসন্ধান করুন</button>
                                        </div>
                                    </div>
                                </form>

                                <?php
                                // Search for students if search term is provided
                                if (isset($_GET['student_search']) && !empty($_GET['student_search'])) {
                                    $searchTerm = $_GET['student_search'];
                                    $searchSql = "SELECT s.*, c.class_name 
                                                FROM students s 
                                                JOIN classes c ON s.class_id = c.id 
                                                WHERE s.student_id = ?
                                                LIMIT 20";
                                    $searchStmt = $conn->prepare($searchSql);
                                    $searchStmt->bind_param("s", $searchTerm);
                                    $searchStmt->execute();
                                    $searchResult = $searchStmt->get_result();
                                    
                                    if ($searchResult->num_rows > 0) {
                                        echo '<div class="table-responsive mb-4">';
                                        echo '<table class="table table-bordered table-hover">';
                                        echo '<thead class="table-light">';
                                        echo '<tr>';
                                        echo '<th>আইডি</th>';
                                        echo '<th>শ্রেণী</th>';
                                        echo '<th>কার্যক্রম</th>';
                                        echo '</tr>';
                                        echo '</thead>';
                                        echo '<tbody>';
                                        
                                        while ($row = $searchResult->fetch_assoc()) {
                                            echo '<tr>';
                                            echo '<td>' . $row['student_id'] . '</td>';
                                            echo '<td>' . $row['class_name'] . '</td>';
                                            echo '<td><a href="?student_id=' . $row['id'] . '" class="btn btn-sm btn-primary">বিস্তারিত মার্কস দিন</a></td>';
                                            echo '</tr>';
                                        }
                                        
                                        echo '</tbody>';
                                        echo '</table>';
                                        echo '</div>';
                                    } else {
                                        echo '<div class="alert alert-warning">কোন ছাত্র/ছাত্রী পাওয়া যায়নি!</div>';
                                    }
                                    
                                    $searchStmt->close();
                                }
                                ?>
                            </div>
                        </div>
                        
                        <?php if ($selectedStudent): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5>বিস্তারিত মার্কস এন্ট্রি</h5>
                                <small>শ্রেণী: <?php echo $selectedStudent['class_name']; ?> | আইডি: <?php echo $selectedStudent['student_id']; ?></small>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>" id="detailedMarksForm">
                                    <input type="hidden" name="student_id" value="<?php echo $selectedStudent['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="exam_id" class="form-label">পরীক্ষা নির্বাচন করুন</label>
                                        <select class="form-select" id="exam_id" name="exam_id" required onchange="this.form.action='?student_id=<?php echo $selectedStudent['id']; ?>&exam_id='+this.value; this.form.submit();">
                                            <option value="">পরীক্ষা নির্বাচন করুন</option>
                                            <?php
                                            if ($examResult->num_rows > 0) {
                                                $examResult->data_seek(0);
                                                while ($exam = $examResult->fetch_assoc()) {
                                                    $selected = (isset($_GET['exam_id']) && $_GET['exam_id'] == $exam['id']) ? 'selected' : '';
                                                    echo '<option value="' . $exam['id'] . '" ' . $selected . '>' . $exam['exam_name'] . ' - ' . $exam['course_name'] . ' (' . $exam['class_name'] . ')</option>';
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <?php if (isset($_GET['exam_id'])): ?>
                                    <div class="row mb-3">
                                        <div class="col-md-4">
                                            <label for="cq_marks" class="form-label">CQ মার্কস</label>
                                            <input type="number" class="form-control" id="cq_marks" name="cq_marks" step="0.01" min="0" value="<?php echo isset($detailedMarks['cq_marks']) ? $detailedMarks['cq_marks'] : ''; ?>" onchange="calculateTotal()">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="mcq_marks" class="form-label">MCQ মার্কস</label>
                                            <input type="number" class="form-control" id="mcq_marks" name="mcq_marks" step="0.01" min="0" value="<?php echo isset($detailedMarks['mcq_marks']) ? $detailedMarks['mcq_marks'] : ''; ?>" onchange="calculateTotal()">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="practical_marks" class="form-label">Practical মার্কস</label>
                                            <input type="number" class="form-control" id="practical_marks" name="practical_marks" step="0.01" min="0" value="<?php echo isset($detailedMarks['practical_marks']) ? $detailedMarks['practical_marks'] : ''; ?>" onchange="calculateTotal()">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="total_marks" class="form-label">মোট মার্কস</label>
                                        <input type="text" class="form-control" id="total_marks" readonly 
                                               value="<?php 
                                                    if(isset($detailedMarks)) {
                                                        if(isset($detailedMarks['marks_obtained'])) {
                                                            echo $detailedMarks['marks_obtained'];
                                                        } else {
                                                            // Calculate from individual marks
                                                            $cq = isset($detailedMarks['cq_marks']) ? floatval($detailedMarks['cq_marks']) : 0;
                                                            $mcq = isset($detailedMarks['mcq_marks']) ? floatval($detailedMarks['mcq_marks']) : 0;
                                                            $practical = isset($detailedMarks['practical_marks']) ? floatval($detailedMarks['practical_marks']) : 0;
                                                            echo $cq + $mcq + $practical;
                                                        }
                                                    } 
                                               ?>">
                                    </div>
                                    
                                    <button type="button" onclick="submitDetailedMarksForm()" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save"></i> মার্কস সংরক্ষণ করুন
                                    </button>
                                    <input type="hidden" name="submit_detailed_marks" value="1">
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Batch Marks Entry Tab -->
                    <div class="tab-pane fade <?php echo isset($_GET['class_id']) ? 'show active' : ''; ?>" id="batch" role="tabpanel" aria-labelledby="batch-tab">
                        <div class="card">
                            <div class="card-header">
                                <h5>ব্যাচ বিস্তারিত মার্কস এন্ট্রি</h5>
                            </div>
                            <div class="card-body">
                                <form method="GET" action="" class="mb-4">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <label for="class_id" class="form-label">শ্রেণী নির্বাচন করুন</label>
                                            <select class="form-select" id="class_id" name="class_id" required onchange="this.form.submit();">
                                                <option value="">শ্রেণী নির্বাচন করুন</option>
                                                <?php
                                                if ($classResult->num_rows > 0) {
                                                    while ($class = $classResult->fetch_assoc()) {
                                                        $selected = (isset($_GET['class_id']) && $_GET['class_id'] == $class['id']) ? 'selected' : '';
                                                        echo '<option value="' . $class['id'] . '" ' . $selected . '>' . $class['class_name'] . '</option>';
                                                    }
                                                }
                                                ?>
                                            </select>
                                        </div>
                                    </div>
                                </form>
                                
                                <?php if (isset($_GET['class_id'])): ?>
                                <form method="POST" action="<?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?>">
                                    <input type="hidden" name="class_id" value="<?php echo $_GET['class_id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="batch_exam_id" class="form-label">পরীক্ষা নির্বাচন করুন</label>
                                        <select class="form-select" id="batch_exam_id" name="batch_exam_id" required>
                                            <option value="">পরীক্ষা নির্বাচন করুন</option>
                                            <?php
                                            if ($examResult->num_rows > 0) {
                                                $examResult->data_seek(0);
                                                while ($exam = $examResult->fetch_assoc()) {
                                                    echo '<option value="' . $exam['id'] . '">' . $exam['exam_name'] . ' - ' . $exam['course_name'] . ' (' . $exam['class_name'] . ')</option>';
                                                }
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>আইডি</th>
                                                    <th>নাম</th>
                                                    <th>CQ মার্কস</th>
                                                    <th>MCQ মার্কস</th>
                                                    <th>Practical মার্কস</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                if (!empty($batchStudents)) {
                                                    foreach ($batchStudents as $student) {
                                                        echo '<tr>';
                                                        echo '<td>' . $student['student_id'] . '</td>';
                                                        echo '<td>' . $student['name'] . '</td>';
                                                        echo '<td><input type="number" class="form-control" name="student_cq_marks[' . $student['id'] . ']" step="0.01" min="0" required></td>';
                                                        echo '<td><input type="number" class="form-control" name="student_mcq_marks[' . $student['id'] . ']" step="0.01" min="0" required></td>';
                                                        echo '<td><input type="number" class="form-control" name="student_practical_marks[' . $student['id'] . ']" step="0.01" min="0" required></td>';
                                                        echo '</tr>';
                                                    }
                                                }
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <button type="submit" name="submit_batch_detailed_marks" class="btn btn-primary btn-lg">
                                        <i class="fas fa-save"></i> মার্কস সংরক্ষণ করুন
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- All Detailed Marks -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5>সকল বিস্তারিত মার্কস</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        if ($allDetailedMarksResult && $allDetailedMarksResult->num_rows > 0) {
                            echo '<div class="table-responsive">';
                            echo '<table class="table table-bordered table-hover">';
                            echo '<thead class="table-light">';
                            echo '<tr>';
                            echo '<th>ছাত্র/ছাত্রী আইডি</th>';
                            echo '<th>পরীক্ষার নাম</th>';
                            echo '<th>বিষয়</th>';
                            echo '<th>শ্রেণী</th>';
                            echo '<th>CQ মার্কস</th>';
                            echo '<th>MCQ মার্কস</th>';
                            echo '<th>Practical মার্কস</th>';
                            echo '<th>মোট মার্কস</th>';
                            echo '<th>কার্যক্রম</th>';
                            echo '</tr>';
                            echo '</thead>';
                            echo '<tbody>';
                            
                            while ($marks = $allDetailedMarksResult->fetch_assoc()) {
                                echo '<tr>';
                                echo '<td>' . $marks['student_id'] . '</td>';
                                echo '<td>' . $marks['exam_name'] . '</td>';
                                echo '<td>' . $marks['course_name'] . '</td>';
                                echo '<td>' . $marks['class_name'] . '</td>';
                                echo '<td>' . (isset($marks['cq_marks']) ? $marks['cq_marks'] : '0') . '</td>';
                                echo '<td>' . (isset($marks['mcq_marks']) ? $marks['mcq_marks'] : '0') . '</td>';
                                echo '<td>' . (isset($marks['practical_marks']) ? $marks['practical_marks'] : '0') . '</td>';
                                echo '<td>' . $marks['marks_obtained'] . '</td>';
                                echo '<td>';
                                echo '<a href="?student_id=' . $marks['student_id'] . '&exam_id=' . $marks['exam_id'] . '" class="btn btn-sm btn-warning me-2"><i class="fas fa-edit"></i> সম্পাদনা</a>';
                                echo '</td>';
                                echo '</tr>';
                            }
                            
                            echo '</tbody>';
                            echo '</table>';
                            echo '</div>';
                        } else {
                            echo '<div class="alert alert-info">কোন বিস্তারিত মার্কস রেকর্ড পাওয়া যায়নি।</div>';
                        }
                        ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function calculateTotal() {
            var cq = parseFloat(document.getElementById('cq_marks').value) || 0;
            var mcq = parseFloat(document.getElementById('mcq_marks').value) || 0;
            var practical = parseFloat(document.getElementById('practical_marks').value) || 0;
            
            var total = cq + mcq + practical;
            document.getElementById('total_marks').value = total.toFixed(2);
        }
        
        function submitDetailedMarksForm() {
            document.getElementById('detailedMarksForm').submit();
        }
    </script>
</body>
</html> 