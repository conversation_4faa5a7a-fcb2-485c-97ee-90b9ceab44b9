<?php
// Path to our file - use full path
$file_path = __DIR__ . '/fees.php';

// Read the file content
$content = file_get_contents($file_path);

// Remove the first button - নতুন ফি যোগ করুন (Add New Fee)
$pattern1 = '/<button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFeeModal">\s*<i class="fas fa-plus-circle me-2"><\/i>\s*নতুন ফি যোগ করুন\s*<\/button>/';
$content = preg_replace($pattern1, '', $content);

// Remove the second button - বাল্ক ফি যোগ করুন (Add Bulk Fee)
$pattern2 = '/<button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkFeeModal">\s*<i class="fas fa-th-list me-2"><\/i>\s*বাল্ক ফি যোগ করুন\s*<\/button>/';
$content = preg_replace($pattern2, '', $content);

// Remove the third button - ফি টাইপ ম্যানেজ করুন (Manage Fee Types)
$pattern3 = '/<button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#feeTypeModal">\s*<i class="fas fa-tags me-2"><\/i>\s*ফি টাইপ ম্যানেজ করুন\s*<\/button>/';
$content = preg_replace($pattern3, '', $content);

// Remove the button in the fee list tab header
$pattern4 = '/<button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#addFeeModal">\s*<i class="fas fa-plus-circle me-1"><\/i>\s*নতুন ফি যোগ করুন\s*<\/button>/';
$content = preg_replace($pattern4, '', $content);

// Remove the title in the fee types section
$pattern5 = '/<h5 class="card-title mb-0"><i class="fas fa-plus-circle me-2"><\/i> নতুন ফি এর ধরন যোগ করুন<\/h5>/';
$content = preg_replace($pattern5, '<h5 class="card-title mb-0"><i class="fas fa-list me-2"></i> ফি এর ধরন</h5>', $content);

// Write the modified content back to the file
file_put_contents($file_path, $content);

echo "All buttons have been successfully removed!\n"; 