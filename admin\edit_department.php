<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if department ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: departments.php");
    exit();
}

$department_id = $_GET['id'];

// Handle form submission
if (isset($_POST['update_department'])) {
    $departmentName = $conn->real_escape_string($_POST['department_name']);
    $description = $conn->real_escape_string($_POST['description'] ?? '');
    
    if (empty($departmentName)) {
        $error_msg = "Department name is required";
    } else {
        $updateQuery = "UPDATE departments SET department_name = '$departmentName', description = '$description' WHERE id = '$department_id'";
        
        if ($conn->query($updateQuery)) {
            $success_msg = "Department updated successfully";
        } else {
            $error_msg = "Error updating department: " . $conn->error;
        }
    }
}

// Get department data
$departmentQuery = "SELECT * FROM departments WHERE id = '$department_id'";
$result = $conn->query($departmentQuery);

if ($result->num_rows == 0) {
    header("Location: departments.php");
    exit();
}

$department = $result->fetch_assoc();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Department | Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Admin Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> Teachers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> Staff
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="departments.php">
                            <i class="fas fa-building me-2"></i> Departments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> Classes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> Fees
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>Edit Department</h2>
                        <p class="text-muted">Update department information</p>
                    </div>
                    <div class="col-auto">
                        <a href="departments.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Departments
                        </a>
                    </div>
                </div>
                
                <!-- Success and Error Messages -->
                <?php if (isset($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Edit Department Form -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Update Department Details</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="edit_department.php?id=<?php echo $department_id; ?>">
                            <div class="mb-3">
                                <label for="department_name" class="form-label">Department Name*</label>
                                <input type="text" class="form-control" id="department_name" name="department_name" value="<?php echo htmlspecialchars($department['department_name']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($department['description'] ?? ''); ?></textarea>
                            </div>
                            <button type="submit" name="update_department" class="btn btn-primary">Update Department</button>
                        </form>
                    </div>
                </div>
                
                <!-- Department Statistics -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Department Statistics</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        // Get student count
                        $studentCountQuery = "SELECT COUNT(*) as count FROM students WHERE department_id = '$department_id'";
                        $studentResult = $conn->query($studentCountQuery);
                        $studentCount = $studentResult->fetch_assoc()['count'];
                        
                        // Get teacher count (if teachers have department_id)
                        $teacherCountQuery = "SHOW COLUMNS FROM teachers LIKE 'department_id'";
                        $teacherColumnResult = $conn->query($teacherCountQuery);
                        $teacherCount = 0;
                        
                        if ($teacherColumnResult && $teacherColumnResult->num_rows > 0) {
                            $teacherCountQuery = "SELECT COUNT(*) as count FROM teachers WHERE department_id = '$department_id'";
                            $teacherResult = $conn->query($teacherCountQuery);
                            if ($teacherResult) {
                                $teacherCount = $teacherResult->fetch_assoc()['count'];
                            }
                        }
                        ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h1 class="display-4"><?php echo $studentCount; ?></h1>
                                        <p class="lead">Students</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h1 class="display-4"><?php echo $teacherCount; ?></h1>
                                        <p class="lead">Teachers</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 