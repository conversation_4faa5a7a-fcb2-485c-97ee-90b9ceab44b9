<?php
// Include necessary files
session_start();
require_once '../includes/dbh.inc.php';
// require_once '../includes/functions.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create fee_payments table if it doesn't exist
$createPaymentsTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
)";
$conn->query($createPaymentsTableQuery);

// Check if an ID was provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = 'ফি আইডি প্রদান করা হয়নি!';
    header('Location: fees.php');
    exit;
}

$feeId = $_GET['id'];

// Get the fee details
$feeQuery = "SELECT f.*, s.first_name, s.last_name, s.student_id as student_roll, c.class_name
             FROM fees f
             JOIN students s ON f.student_id = s.id
             JOIN classes c ON s.class_id = c.id
             WHERE f.id = ?";
$stmt = $conn->prepare($feeQuery);
$stmt->bind_param('i', $feeId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    $_SESSION['error'] = 'ফি রেকর্ড খুঁজে পাওয়া যায়নি!';
    header('Location: fees.php');
    exit;
}

$fee = $result->fetch_assoc();

// Get fee types for dropdown
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add_payment') {
        // Process payment addition
        $paymentAmount = floatval($_POST['payment_amount'] ?? 0);
        $paymentMethod = $_POST['payment_method'] ?? 'cash';
        $paymentDate = $_POST['payment_date'] ?? date('Y-m-d');
        $notes = $_POST['notes'] ?? '';
        
        if ($paymentAmount <= 0) {
            $paymentError = 'পেমেন্ট পরিমাণ অবশ্যই শূন্যের বেশি হতে হবে।';
        } else {
            // Insert payment record
            $insertPaymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, notes) 
                                  VALUES (?, ?, ?, ?, ?)";
            $paymentStmt = $conn->prepare($insertPaymentQuery);
            $paymentStmt->bind_param('idsss', $feeId, $paymentAmount, $paymentDate, $paymentMethod, $notes);
            
            if ($paymentStmt->execute()) {
                // Update fee paid amount and status
                $newPaidAmount = $fee['paid'] + $paymentAmount;
                $newPaymentStatus = 'due';
                if ($newPaidAmount >= $fee['amount']) {
                    $newPaymentStatus = 'paid';
                } elseif ($newPaidAmount > 0) {
                    $newPaymentStatus = 'partial';
                }
                
                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
                $updateFeeStmt = $conn->prepare($updateFeeQuery);
                $updateFeeStmt->bind_param('dsi', $newPaidAmount, $newPaymentStatus, $feeId);
                
                if ($updateFeeStmt->execute()) {
                    // Reload the page to show updated data
                    header('Location: edit_fee.php?id=' . $feeId . '&payment_success=1');
                    exit;
                } else {
                    $paymentError = 'ফি আপডেট করতে সমস্যা: ' . $conn->error;
                }
            } else {
                $paymentError = 'পেমেন্ট যোগ করতে সমস্যা: ' . $conn->error;
            }
        }
    } else {
        // Update fee record
        $studentId = $fee['student_id']; // We don't change the student
        $feeType = $_POST['fee_type'] ?? '';
        $amount = floatval($_POST['amount'] ?? 0);
        $paid = floatval($_POST['paid'] ?? 0);
        $dueDate = $_POST['due_date'] ?? '';
        
        // Calculate payment status
        $paymentStatus = 'due';
        if ($paid >= $amount) {
            $paymentStatus = 'paid';
        } elseif ($paid > 0) {
            $paymentStatus = 'partial';
        }
        
        // Update the fee record
        $updateQuery = "UPDATE fees SET fee_type = ?, amount = ?, paid = ?, due_date = ?, payment_status = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param('sddssi', $feeType, $amount, $paid, $dueDate, $paymentStatus, $feeId);
        
        if ($updateStmt->execute()) {
            $_SESSION['success'] = 'ফি সফলভাবে আপডেট করা হয়েছে!';
            header('Location: fees.php');
            exit;
        } else {
            $error = 'ফি আপডেট করতে সমস্যা হয়েছে: ' . $conn->error;
        }
    }
}

// Format date for form input
$formattedDueDate = date('Y-m-d', strtotime($fee['due_date']));

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }
        .student-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-label {
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i> ফি সম্পাদনা
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger"><?= $error ?></div>
                        <?php endif; ?>
                        
                        <!-- Student Information -->
                        <div class="student-info">
                            <h6 class="mb-3"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী তথ্য</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <p><strong>আইডি:</strong> <?= htmlspecialchars($fee['student_roll']) ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>নাম:</strong> <?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></p>
                                </div>
                                <div class="col-md-4">
                                    <p><strong>ক্লাস:</strong> <?= htmlspecialchars($fee['class_name']) ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Summary -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">মোট পরিমাণ</h5>
                                        <h3 class="mb-0">৳ <?= number_format($fee['amount'], 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">পরিশোধিত</h5>
                                        <h3 class="mb-0">৳ <?= number_format($fee['paid'], 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-danger text-white">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">বকেয়া</h5>
                                        <h3 class="mb-0">৳ <?= number_format(max(0, $fee['amount'] - $fee['paid']), 2) ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Edit Fee Form -->
                        <form action="" method="POST">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="fee_type" class="form-label">ফি টাইপ</label>
                                        <select class="form-select" id="fee_type" name="fee_type" required>
                                            <option value="">ফি টাইপ নির্বাচন করুন</option>
                                            <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                    <option value="<?= htmlspecialchars($type['name']) ?>" <?= ($fee['fee_type'] === $type['name']) ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($type['name']) ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">পরিমাণ (টাকা)</label>
                                        <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" value="<?= $fee['amount'] ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="paid" class="form-label">পরিশোধিত পরিমাণ (টাকা)</label>
                                        <input type="number" step="0.01" min="0" class="form-control" id="paid" name="paid" value="<?= $fee['paid'] ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="due_date" class="form-label">শেষ তারিখ</label>
                                        <input type="date" class="form-control" id="due_date" name="due_date" value="<?= $formattedDueDate ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="fees.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Payment History Section -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history me-2"></i> পেমেন্ট ইতিহাস
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($paymentError)): ?>
                            <div class="alert alert-danger"><?= $paymentError ?></div>
                        <?php endif; ?>
                        
                        <?php if (isset($_GET['payment_success'])): ?>
                            <div class="alert alert-success">পেমেন্ট সফলভাবে যোগ করা হয়েছে!</div>
                        <?php endif; ?>
                        
                        <!-- Add New Payment Form -->
                        <div class="card mb-4 bg-light">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0"><i class="fas fa-plus-circle me-2"></i> নতুন পেমেন্ট যোগ করুন</h6>
                            </div>
                            <div class="card-body">
                                <form action="" method="POST">
                                    <input type="hidden" name="action" value="add_payment">
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_amount" class="form-label">পেমেন্ট পরিমাণ (টাকা)</label>
                                                <input type="number" step="0.01" min="0.01" max="<?= max(0, $fee['amount'] - $fee['paid']) ?>" 
                                                    class="form-control" id="payment_amount" name="payment_amount" 
                                                    value="<?= max(0, $fee['amount'] - $fee['paid']) ?>" required>
                                                <small class="text-muted">সর্বাধিক বকেয়া: ৳ <?= number_format(max(0, $fee['amount'] - $fee['paid']), 2) ?></small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                                                <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                                <select class="form-select" id="payment_method" name="payment_method">
                                                    <option value="cash">নগদ</option>
                                                    <option value="bank">ব্যাংক ট্রান্সফার</option>
                                                    <option value="bkash">বিকাশ</option>
                                                    <option value="nagad">নগদ</option>
                                                    <option value="rocket">রকেট</option>
                                                    <option value="other">অন্যান্য</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="notes" class="form-label">নোট</label>
                                                <input type="text" class="form-control" id="notes" name="notes" placeholder="পেমেন্ট সম্পর্কে অতিরিক্ত তথ্য">
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-plus-circle me-2"></i> পেমেন্ট যোগ করুন
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <?php
                            // Check if fee_payments table exists
                            $tableCheckQuery = "SHOW TABLES LIKE 'fee_payments'";
                            $tableCheckResult = $conn->query($tableCheckQuery);
                            $tableExists = ($tableCheckResult->num_rows > 0);
                            
                            $payments = null;
                            if ($tableExists) {
                                // Get payment history
                                $paymentQuery = "SELECT * FROM fee_payments WHERE fee_id = ? ORDER BY payment_date DESC";
                                $paymentStmt = $conn->prepare($paymentQuery);
                                $paymentStmt->bind_param('i', $feeId);
                                $paymentStmt->execute();
                                $payments = $paymentStmt->get_result();
                            }
                            ?>
                            
                            <table class="table table-hover table-striped">
                                <thead class="table-light">
                                    <tr>
                                        <th>তারিখ</th>
                                        <th>পরিমাণ</th>
                                        <th>পেমেন্ট পদ্ধতি</th>
                                        <th>নোট</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($tableExists && $payments && $payments->num_rows > 0): ?>
                                        <?php while ($payment = $payments->fetch_assoc()): ?>
                                            <tr>
                                                <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                                                <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                                <td><?= htmlspecialchars($payment['payment_method'] ?? 'নগদ') ?></td>
                                                <td><?= htmlspecialchars($payment['notes'] ?? '-') ?></td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted">
                                                <?php if (!$tableExists): ?>
                                                    পেমেন্ট টেবিল যোগ করা হয়নি। এডমিন ফি পেমেন্ট ফিচার সেটআপ করতে হবে।
                                                <?php else: ?>
                                                    কোন পেমেন্ট রেকর্ড পাওয়া যায়নি
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS & jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Calculate due amount when amount or paid changes
            $('#amount, #paid').on('input', function() {
                const amount = parseFloat($('#amount').val()) || 0;
                const paid = parseFloat($('#paid').val()) || 0;
                const due = amount - paid;
                
                // Validate paid amount
                if (paid > amount) {
                    alert('পরিশোধিত পরিমাণ মোট পরিমাণের চেয়ে বেশি হতে পারে না!');
                    $('#paid').val(amount);
                }
            });
        });
    </script>
</body>
</html> 