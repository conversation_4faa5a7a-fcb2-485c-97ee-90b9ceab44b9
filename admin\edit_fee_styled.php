<?php
// Include database connection
include '../includes/dbh.inc.php';

// Check if fee ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: fees.php');
    exit;
}

$feeId = $_GET['id'];

// Fetch fee details with student information
$query = "SELECT f.*, s.student_id, s.first_name, s.last_name, c.class_name, 
         f.amount - f.paid as due_amount, 
         f.fee_type
         FROM fees f
         LEFT JOIN students s ON f.student_id = s.id
         LEFT JOIN classes c ON s.class_id = c.id
         WHERE f.id = ?";

$stmt = $conn->prepare($query);
$stmt->bind_param('i', $feeId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header('Location: fees.php');
    exit;
}

$fee = $result->fetch_assoc();

// Get fee types for dropdown
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Process edit form if submitted
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_fee') {
    // Get form data
    $feeType = $conn->real_escape_string($_POST['fee_type']);
    $amount = floatval($_POST['amount']);
    $dueDate = $conn->real_escape_string($_POST['due_date']);
    $description = $conn->real_escape_string($_POST['description'] ?? '');
    
    // Validate data
    if (empty($feeType) || $amount <= 0 || empty($dueDate)) {
        $errorMessage = "সব প্রয়োজনীয় ক্ষেত্রগুলি পূরণ করুন। পরিমাণ অবশ্যই শূন্যের বেশি হতে হবে।";
    } else {
        // Calculate payment status based on current paid amount
        $paymentStatus = 'due';
        if ($fee['paid'] >= $amount) {
            $paymentStatus = 'paid';
        } elseif ($fee['paid'] > 0) {
            $paymentStatus = 'partial';
        }
        
        // Update fee record
        $updateQuery = "UPDATE fees SET fee_type = ?, amount = ?, due_date = ?, description = ?, payment_status = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param('sdsssi', $feeType, $amount, $dueDate, $description, $paymentStatus, $feeId);
        
        if ($updateStmt->execute()) {
            $successMessage = "ফি সফলভাবে আপডেট করা হয়েছে!";
            
            // Refresh fee data
            $stmt->execute();
            $result = $stmt->get_result();
            $fee = $result->fetch_assoc();
        } else {
            $errorMessage = "ফি আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Set page title
$pageTitle = "ফি সম্পাদনা করুন";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f5f5f5;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border: none;
        }
        .card-header {
            border-radius: 10px 10px 0 0 !important;
            font-weight: bold;
        }
        .info-group {
            margin-bottom: 1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        .info-label {
            font-weight: bold;
            color: #555;
        }
        .info-value {
            font-weight: 500;
        }
        .amount-box {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        .amount-label {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
        }
        .amount-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .primary-amount {
            background-color: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
        }
        .danger-amount {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
        .success-amount {
            background-color: rgba(25, 135, 84, 0.1);
            color: #198754;
        }
        .form-control:focus, .form-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        .form-label {
            font-weight: 600;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <?php if (!empty($successMessage)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?= $successMessage ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($errorMessage)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?= $errorMessage ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-edit me-2"></i> ফি সম্পাদনা করুন</h5>
                            <a href="fees.php" class="btn btn-sm btn-dark">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- Student Information -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="mb-3 text-muted"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী তথ্য</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row info-group">
                                            <div class="col-md-4 info-label">শিক্ষার্থী আইডি:</div>
                                            <div class="col-md-8 info-value"><?= htmlspecialchars($fee['student_id']) ?></div>
                                        </div>
                                        <div class="row info-group">
                                            <div class="col-md-4 info-label">শিক্ষার্থী নাম:</div>
                                            <div class="col-md-8 info-value"><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4 info-label">ক্লাস:</div>
                                            <div class="col-md-8 info-value"><?= htmlspecialchars($fee['class_name'] ?? 'অনির্দিষ্ট') ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current Status -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h5 class="mb-3 text-muted"><i class="fas fa-info-circle me-2"></i> বর্তমান অবস্থা</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="amount-box primary-amount">
                                            <div class="amount-label">মোট পরিমাণ</div>
                                            <div class="amount-value">৳ <?= number_format($fee['amount'], 2) ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="amount-box success-amount">
                                            <div class="amount-label">পরিশোধিত</div>
                                            <div class="amount-value">৳ <?= number_format($fee['paid'], 2) ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="amount-box danger-amount">
                                            <div class="amount-label">বকেয়া</div>
                                            <div class="amount-value">৳ <?= number_format($fee['due_amount'], 2) ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Status -->
                        <div class="alert 
                            <?php if ($fee['payment_status'] === 'paid'): ?>
                                alert-success
                            <?php elseif ($fee['payment_status'] === 'partial'): ?>
                                alert-warning
                            <?php else: ?>
                                alert-danger
                            <?php endif; ?>
                            mb-4">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <?php if ($fee['payment_status'] === 'paid'): ?>
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    <?php elseif ($fee['payment_status'] === 'partial'): ?>
                                        <i class="fas fa-exclamation-circle fa-2x"></i>
                                    <?php else: ?>
                                        <i class="fas fa-times-circle fa-2x"></i>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <h6 class="mb-0">পেমেন্ট স্ট্যাটাস:
                                    <?php if ($fee['payment_status'] === 'paid'): ?>
                                        পরিশোধিত
                                    <?php elseif ($fee['payment_status'] === 'partial'): ?>
                                        আংশিক পরিশোধিত
                                    <?php else: ?>
                                        বকেয়া
                                    <?php endif; ?>
                                    </h6>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Edit Form -->
                        <form method="POST" action="">
                            <input type="hidden" name="action" value="update_fee">
                            
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <h5 class="mb-3 text-muted"><i class="fas fa-file-invoice-dollar me-2"></i> ফি তথ্য সম্পাদনা করুন</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <label for="fee_type" class="form-label">ফি টাইপ</label>
                                                <select class="form-select form-select-lg" id="fee_type" name="fee_type" required>
                                                    <option value="">ফি টাইপ নির্বাচন করুন</option>
                                                    <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                        <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                            <option value="<?= htmlspecialchars($type['name']) ?>" 
                                                                <?= ($type['name'] === $fee['fee_type']) ? 'selected' : '' ?>>
                                                                <?= htmlspecialchars($type['name']) ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="amount" class="form-label">মোট পরিমাণ (৳)</label>
                                                <input type="number" step="0.01" min="0" class="form-control form-control-lg" 
                                                    id="amount" name="amount" value="<?= $fee['amount'] ?>" required>
                                                <div class="form-text">নোট: পরিশোধিত পরিমাণ ইতিমধ্যে ৳ <?= number_format($fee['paid'], 2) ?></div>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="due_date" class="form-label">শেষ তারিখ</label>
                                                <input type="date" class="form-control form-control-lg" id="due_date" 
                                                    name="due_date" value="<?= date('Y-m-d', strtotime($fee['due_date'])) ?>" required>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="description" class="form-label">বিবরণ (ঐচ্ছিক)</label>
                                                <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($fee['description'] ?? '') ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="alert alert-info mb-4">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">সতর্কতা</h6>
                                        <p class="mb-0">মোট পরিমাণ পরিবর্তন করলে পেমেন্ট স্ট্যাটাস স্বয়ংক্রিয়ভাবে আপডেট হবে।</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-warning btn-lg py-3">
                                    <i class="fas fa-save me-2"></i> ফি আপডেট করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 