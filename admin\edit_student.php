<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$errorMessage = "";
$successMessage = "";

// Get student ID from URL
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: students.php");
    exit();
}

$studentId = $_GET['id'];

// Get departments for the dropdown
$departmentsQuery = "SELECT DISTINCT department_id FROM students ORDER BY department_id";
$departments = $conn->query($departmentsQuery);
$departmentList = [];

if ($departments && $departments->num_rows > 0) {
    while ($dept = $departments->fetch_assoc()) {
        $departmentList[] = $dept['department_id'];
    }
}

// Get student data
$studentQuery = "SELECT s.*, u.username FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: students.php");
    exit();
}

$student = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $student_id = $_POST['student_id'];
    $firstName = $_POST['first_name'];
    $lastName = $_POST['last_name'];
    $email = $_POST['email'];
    $phone = $_POST['phone'];
    $address = $_POST['address'];
    $dob = $_POST['dob'];
    $gender = $_POST['gender'];
    $batch = $_POST['batch'];
    $roll_number = $_POST['roll_number'];
    $department_id = $_POST['department_id'];
    $admissionDate = $_POST['admission_date'];
    $username = $_POST['username'];
    $password = $_POST['password'];

    // Validate required fields
    if (empty($student_id) || empty($firstName) || empty($lastName) || empty($email) || 
        empty($phone) || empty($address) || empty($dob) || empty($gender) || 
        empty($batch) || empty($department_id) || empty($admissionDate) || 
        empty($username)) {
        $errorMessage = "All fields except password are required!";
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Check if student ID already exists (but not for this student)
            $checkQuery = "SELECT 1 FROM students WHERE student_id = ? AND id != ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("si", $student_id, $studentId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                throw new Exception("Student ID already exists for another student!");
            }
            
            // Check if username already exists (but not for this user)
            $checkQuery = "SELECT 1 FROM users WHERE username = ? AND id != ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("si", $username, $student['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                throw new Exception("Username already exists for another user!");
            }
            
            // Update user
            if (!empty($password)) {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $userQuery = "UPDATE users SET username = ?, password = ? WHERE id = ?";
                $stmt = $conn->prepare($userQuery);
                $stmt->bind_param("ssi", $username, $hashedPassword, $student['user_id']);
            } else {
                $userQuery = "UPDATE users SET username = ? WHERE id = ?";
                $stmt = $conn->prepare($userQuery);
                $stmt->bind_param("si", $username, $student['user_id']);
            }
            $stmt->execute();
            
            // Update student
            $studentQuery = "UPDATE students SET 
                          student_id = ?, 
                          first_name = ?, 
                          last_name = ?, 
                          email = ?, 
                          phone = ?, 
                          address = ?, 
                          dob = ?, 
                          gender = ?, 
                          batch = ?, 
                          roll_number = ?,
                          department_id = ?, 
                          admission_date = ? 
                          WHERE id = ?";
            $stmt = $conn->prepare($studentQuery);
            $stmt->bind_param("ssssssssssssi", $student_id, $firstName, $lastName, $email, $phone, $address, $dob, $gender, $batch, $roll_number, $department_id, $admissionDate, $studentId);
            $stmt->execute();
            
            // Commit transaction
            $conn->commit();
            $successMessage = "Student updated successfully!";
            
            // Refresh student data
            $studentQuery = "SELECT s.*, u.username FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.id = ?";
            $stmt = $conn->prepare($studentQuery);
            $stmt->bind_param("i", $studentId);
            $stmt->execute();
            $result = $stmt->get_result();
            $student = $result->fetch_assoc();
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $errorMessage = "Error: " . $e->getMessage();
        }
    }
}

// Get departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী সম্পাদনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>শিক্ষার্থী সম্পাদনা</h2>
                        <p class="text-muted">শিক্ষার্থীর তথ্য আপডেট করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="students.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>শিক্ষার্থী তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form method="POST">
                            <div class="row">
                                <!-- Student Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">শিক্ষার্থীর তথ্য</h4>
                                    
                                    <div class="mb-3">
                                        <label for="student_id" class="form-label">শিক্ষার্থী আইডি*</label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" value="<?php echo htmlspecialchars($student['student_id']); ?>" required>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="first_name" class="form-label">প্রথম নাম*</label>
                                            <input type="text" class="form-control" id="first_name" name="first_name" value="<?php echo htmlspecialchars($student['first_name']); ?>" required>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="last_name" class="form-label">শেষ নাম*</label>
                                            <input type="text" class="form-control" id="last_name" name="last_name" value="<?php echo htmlspecialchars($student['last_name']); ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল*</label>
                                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($student['email']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন*</label>
                                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($student['phone']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">ঠিকানা*</label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($student['address']); ?></textarea>
                                    </div>
                                </div>
                                
                                <!-- Academic Details -->
                                <div class="col-md-6 mb-4">
                                    <h4 class="card-title mb-3">একাডেমিক তথ্য</h4>
                                    
                                    <div class="mb-3">
                                        <label for="dob" class="form-label">জন্ম তারিখ*</label>
                                        <input type="date" class="form-control" id="dob" name="dob" value="<?php echo htmlspecialchars($student['dob']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">লিঙ্গ*</label>
                                        <select class="form-select" id="gender" name="gender" required>
                                            <option value="">লিঙ্গ নির্বাচন করুন</option>
                                            <option value="Male" <?php echo ($student['gender'] === 'Male') ? 'selected' : ''; ?>>পুরুষ</option>
                                            <option value="Female" <?php echo ($student['gender'] === 'Female') ? 'selected' : ''; ?>>মহিলা</option>
                                            <option value="Other" <?php echo ($student['gender'] === 'Other') ? 'selected' : ''; ?>>অন্যান্য</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="batch" class="form-label">ব্যাচ*</label>
                                        <input type="text" class="form-control" id="batch" name="batch" value="<?php echo htmlspecialchars($student['batch']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="roll_number" class="form-label">রোল নম্বর*</label>
                                        <input type="text" class="form-control" id="roll_number" name="roll_number" value="<?php echo htmlspecialchars($student['roll_number'] ?? ''); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ*</label>
                                        <select class="form-select" id="department_id" name="department_id" required>
                                            <option value="">বিভাগ নির্বাচন করুন</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo htmlspecialchars($dept['id']); ?>" <?php echo ($student['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($dept['department_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="admission_date" class="form-label">ভর্তির তারিখ*</label>
                                        <input type="date" class="form-control" id="admission_date" name="admission_date" value="<?php echo htmlspecialchars($student['admission_date']); ?>" required>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Account Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h4 class="card-title mb-3">অ্যাকাউন্টের তথ্য</h4>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">ইউজারনেম*</label>
                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($student['username']); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">পাসওয়ার্ড (বর্তমান রাখতে ফাঁকা রাখুন)</label>
                                    <input type="password" class="form-control" id="password" name="password">
                                    <small class="form-text text-muted">শুধুমাত্র পাসওয়ার্ড পরিবর্তন করতে চাইলে পূরণ করুন</small>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="text-end">
                                <a href="students.php" class="btn btn-secondary">বাতিল</a>
                                <button type="submit" class="btn btn-primary">শিক্ষার্থী আপডেট করুন</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 