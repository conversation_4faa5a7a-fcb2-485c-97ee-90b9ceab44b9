<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get statistics
$exams_count = $conn->query("SELECT COUNT(*) as count FROM exams")->fetch_assoc()['count'];
$subjects_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch_assoc()['count'];

// Count students
$students_count = $conn->query("SELECT COUNT(*) as count FROM students")->fetch_assoc()['count'];

// Try to count assigned subjects if table exists
try {
    $assigned_subjects_count = $conn->query("SELECT COUNT(*) as count FROM exam_subjects")->fetch_assoc()['count'];
} catch (Exception $e) {
    $assigned_subjects_count = 0;
}

// Try to count exam results if table exists
try {
    $results_count = $conn->query("SELECT COUNT(*) as count FROM exam_results")->fetch_assoc()['count'];
} catch (Exception $e) {
    $results_count = 0;
}

// Get upcoming exams
$upcoming_exams_sql = "SELECT e.* 
                     FROM exams e 
                     WHERE e.exam_date >= CURDATE() 
                     ORDER BY e.exam_date ASC LIMIT 5";
$upcoming_exams_result = $conn->query($upcoming_exams_sql);

// Get recent exams (past 30 days)
$recent_exams_sql = "SELECT e.* 
                   FROM exams e 
                   WHERE e.exam_date < CURDATE() 
                   AND e.exam_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                   ORDER BY e.exam_date DESC LIMIT 5";
$recent_exams_result = $conn->query($recent_exams_sql);

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .dashboard-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            height: 100%;
            border-top: 3px solid #007bff;
            position: relative;
            overflow: hidden;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .dashboard-card .icon {
            font-size: 36px;
            color: #007bff;
            margin-bottom: 15px;
        }
        .dashboard-card .icon-bg {
            position: absolute;
            right: -15px;
            bottom: -15px;
            font-size: 100px;
            opacity: 0.05;
            transform: rotate(-15deg);
        }
        .dashboard-card h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #343a40;
        }
        .dashboard-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
        .dashboard-card .count {
            font-size: 24px;
            font-weight: bold;
            color: #343a40;
            margin-bottom: 5px;
        }
        .workflow-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
            overflow: hidden;
        }
        .workflow-card:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .workflow-card.card-1 {
            border-left-color: #28a745;
        }
        .workflow-card.card-2 {
            border-left-color: #fd7e14;
        }
        .workflow-card.card-3 {
            border-left-color: #20c997;
        }
        .workflow-card.card-4 {
            border-left-color: #dc3545;
        }
        .workflow-card.card-5 {
            border-left-color: #6610f2;
        }
        .workflow-card.card-6 {
            border-left-color: #17a2b8;
        }
        .workflow-card .card-header {
            border-bottom: 1px solid #f0f0f0;
            background-color: transparent;
            padding: 15px 20px;
            position: relative;
        }
        .workflow-card .card-header h4 {
            margin: 0;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }
        .workflow-card .card-header .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            margin-right: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        .workflow-card.card-1 .card-header .step-number {
            background-color: #28a745;
        }
        .workflow-card.card-2 .card-header .step-number {
            background-color: #fd7e14;
        }
        .workflow-card.card-3 .card-header .step-number {
            background-color: #20c997;
        }
        .workflow-card.card-4 .card-header .step-number {
            background-color: #dc3545;
        }
        .workflow-card.card-5 .card-header .step-number {
            background-color: #6610f2;
        }
        .workflow-card.card-6 .card-header .step-number {
            background-color: #17a2b8;
        }
        .workflow-card .card-body {
            padding: 20px;
        }
        .workflow-card .action-link {
            display: inline-block;
            padding: 6px 12px;
            text-decoration: none;
            color: #fff;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 10px;
            margin-right: 5px;
            transition: all 0.3s ease;
        }
        .workflow-card .action-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .workflow-card .action-link.primary {
            background-color: #007bff;
        }
        .workflow-card .action-link.secondary {
            background-color: #6c757d;
        }
        .workflow-card.card-1 .action-link.primary {
            background-color: #28a745;
        }
        .workflow-card.card-2 .action-link.primary {
            background-color: #fd7e14;
        }
        .workflow-card.card-3 .action-link.primary {
            background-color: #20c997;
        }
        .workflow-card.card-4 .action-link.primary {
            background-color: #dc3545;
        }
        .workflow-card.card-5 .action-link.primary {
            background-color: #6610f2;
        }
        .workflow-card.card-6 .action-link.primary {
            background-color: #17a2b8;
        }
        .exam-list {
            margin-top: 15px;
        }
        .exam-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        .exam-item:hover {
            background-color: #f8f9fa;
        }
        .exam-item:last-child {
            border-bottom: none;
        }
        .exam-item .exam-date {
            color: #6c757d;
            font-size: 0.85rem;
        }
        .exam-item .exam-name {
            font-weight: 500;
            margin-bottom: 5px;
            color: #343a40;
        }
        .exam-item .exam-details {
            display: flex;
            align-items: center;
            font-size: 0.85rem;
            color: #6c757d;
        }
        .exam-item .exam-details > span {
            margin-right: 15px;
        }
        .exam-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 30px;
            font-size: 0.75rem;
            background-color: #e9ecef;
            color: #495057;
            margin-right: 5px;
        }
        .section-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #343a40;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষা ব্যবস্থাপনা ড্যাশবোর্ড</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_workflow.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-sitemap"></i> কার্যপ্রণালী দেখুন
                        </a>
                        <a href="manage_exams.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-list"></i> পরীক্ষা ব্যবস্থাপনা
                        </a>
                        <button type="button" class="btn btn-sm btn-primary" onclick="window.location.href='create_exam.php'">
                            <i class="fas fa-plus"></i> নতুন পরীক্ষা যোগ করুন
                        </button>
                    </div>
                </div>
                
                <!-- Stats Row -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <i class="fas fa-clipboard-check icon"></i>
                            <i class="fas fa-clipboard-check icon-bg"></i>
                            <div class="count"><?php echo $exams_count; ?></div>
                            <h3>মোট পরীক্ষা</h3>
                            <p>সিস্টেমে সকল পরীক্ষার সংখ্যা</p>
                            <a href="manage_exams.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-eye"></i> সব পরীক্ষা দেখুন
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <i class="fas fa-calendar-alt icon"></i>
                            <i class="fas fa-calendar-alt icon-bg"></i>
                            <div class="count"><?php echo $upcoming_exams_result->num_rows; ?></div>
                            <h3>পরীক্ষার সময়সূচি</h3>
                            <p>কোন তারিখে কোন বিষয়ের পরীক্ষা</p>
                            <a href="exam_schedule.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-calendar-day"></i> সময়সূচি দেখুন
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-book"></i></div>
                            <div class="icon-bg"><i class="fas fa-book"></i></div>
                            <div class="count"><?php echo $subjects_count; ?></div>
                            <h3>মোট বিষয়</h3>
                            <p>পাঠদানকৃত বিষয়সমূহ</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-user-graduate"></i></div>
                            <div class="icon-bg"><i class="fas fa-user-graduate"></i></div>
                            <div class="count"><?php echo $students_count; ?></div>
                            <h3>মোট ছাত্র-ছাত্রী</h3>
                            <p>নিবন্ধিত ছাত্র-ছাত্রী</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-chart-bar"></i></div>
                            <div class="icon-bg"><i class="fas fa-chart-bar"></i></div>
                            <div class="count"><?php echo $results_count; ?></div>
                            <h3>ফলাফল</h3>
                            <p>রেকর্ডকৃত ফলাফল</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-file-alt"></i></div>
                            <div class="icon-bg"><i class="fas fa-file-alt"></i></div>
                            <div class="count"><i class="fas fa-file-certificate"></i></div>
                            <h3>মার্কশিট</h3>
                            <p>পরীক্ষার মার্কশিট</p>
                            <a href="student_marksheet.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-file-alt"></i> মার্কশিট দেখুন
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-table"></i></div>
                            <div class="icon-bg"><i class="fas fa-table"></i></div>
                            <div class="count"><i class="fas fa-th"></i></div>
                            <h3>ট্যাবুলেশন শিট</h3>
                            <p>সকল ছাত্র-ছাত্রীর ফলাফল</p>
                            <a href="tabulation_sheet.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-table"></i> ট্যাবুলেশন শিট দেখুন
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-cog"></i></div>
                            <div class="icon-bg"><i class="fas fa-cog"></i></div>
                            <div class="count"><i class="fas fa-sliders-h"></i></div>
                            <h3>পাসিং মার্কস</h3>
                            <p>কাস্টম গ্রেডিং সিস্টেম</p>
                            <a href="passing_marks_config.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-cogs"></i> কনফিগারেশন
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-check-circle"></i></div>
                            <div class="icon-bg"><i class="fas fa-check-circle"></i></div>
                            <div class="count"><i class="fas fa-check"></i></div>
                            <h3>ন্যূনতম পাস মার্কস</h3>
                            <p>বিষয় ভিত্তিক সিকিউ/এমসিকিউ</p>
                            <a href="subject_minimum_pass.php" class="btn btn-sm btn-primary mt-3">
                                <i class="fas fa-wrench"></i> কনফিগারেশন
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Workflow Row -->
                <h3 class="section-title">পরীক্ষা পরিচালনার ধারাবাহিক প্রক্রিয়া</h3>
                
                <div class="row">
                    <!-- Step 1: Create Exam -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-1">
                            <div class="card-header">
                                <h4><span class="step-number">1</span> পরীক্ষা তৈরি করুন</h4>
                            </div>
                            <div class="card-body">
                                <p>নতুন পরীক্ষা যোগ করুন। পরীক্ষার নাম, তারিখ, শ্রেণী ও বিবরণ সেট করুন।</p>
                                <a href="create_exam.php" class="action-link primary">
                                    <i class="fas fa-plus-circle"></i> পরীক্ষা তৈরি
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 2: Assign Subjects -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-2">
                            <div class="card-header">
                                <h4><span class="step-number">2</span> বিষয় সংযুক্ত করুন</h4>
                            </div>
                            <div class="card-body">
                                <p>পরীক্ষার সাথে বিষয়সমূহ সংযুক্ত করুন। প্রতিটি বিষয়ের জন্য নম্বর নির্ধারণ করুন।</p>
                                <a href="exam_subject_assign.php" class="action-link primary">
                                    <i class="fas fa-book"></i> বিষয় সংযুক্ত করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 3: Generate Exam Schedule -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-3">
                            <div class="card-header">
                                <h4><span class="step-number">3</span> রুটিন তৈরি করুন</h4>
                            </div>
                            <div class="card-body">
                                <p>পরীক্ষার টাইম টেবিল তৈরি ও প্রিন্ট করুন। তারিখ ও শ্রেণী ভিত্তিক রুটিন পাবেন।</p>
                                <a href="exam_timetable.php" class="action-link primary">
                                    <i class="fas fa-calendar-alt"></i> টাইম টেবিল
                                </a>
                                <a href="generate_schedule.php" class="action-link secondary">
                                    <i class="fas fa-print"></i> প্রিন্ট
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 4: Marks Entry -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-4">
                            <div class="card-header">
                                <h4><span class="step-number">4</span> মার্কস এন্ট্রি</h4>
                            </div>
                            <div class="card-body">
                                <p>ছাত্র-ছাত্রীদের পরীক্ষার প্রাপ্ত নম্বর এন্ট্রি করুন। শ্রেণী ও বিষয় অনুযায়ী।</p>
                                <a href="marks_entry.php" class="action-link primary">
                                    <i class="fas fa-edit"></i> মার্কস এন্ট্রি
                                </a>
                                <a href="detailed_marks_entry.php" class="action-link secondary">
                                    <i class="fas fa-th-list"></i> বিস্তারিত এন্ট্রি
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 5: Generate Result -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-5">
                            <div class="card-header">
                                <h4><span class="step-number">5</span> ফলাফল প্রস্তুত করুন</h4>
                            </div>
                            <div class="card-body">
                                <p>মোট নম্বর, শতাংশ, গ্রেড সহ মেধা তালিকা তৈরি করুন। শ্রেণী ভিত্তিক ফলাফল দেখুন।</p>
                                <a href="result_management.php" class="action-link primary">
                                    <i class="fas fa-clipboard-list"></i> ফলাফল তৈরি করুন
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Step 6: Publish Result -->
                    <div class="col-md-4 mb-4">
                        <div class="workflow-card card-6">
                            <div class="card-header">
                                <h4><span class="step-number">6</span> ফলাফল প্রকাশ করুন</h4>
                            </div>
                            <div class="card-body">
                                <p>ফলাফল প্রকাশ করুন। মার্কশিট তৈরি ও প্রিন্ট করুন। ছাত্র-ছাত্রী/অভিভাবকদের জন্য।</p>
                                <a href="results.php" class="action-link primary">
                                    <i class="fas fa-chart-bar"></i> ফলাফল দেখুন
                                </a>
                                <a href="generate_marksheet.php" class="action-link secondary">
                                    <i class="fas fa-print"></i> মার্কশিট
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Exams Section -->
                <div class="row mt-4">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar-day me-2"></i> আসন্ন পরীক্ষাসমূহ</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="exam-list">
                                    <?php if ($upcoming_exams_result && $upcoming_exams_result->num_rows > 0): ?>
                                        <?php while ($exam = $upcoming_exams_result->fetch_assoc()): ?>
                                            <div class="exam-item">
                                                <div class="exam-name"><?php echo $exam['exam_name']; ?></div>
                                                <div class="exam-date">
                                                    <i class="fas fa-calendar-alt"></i> 
                                                    <?php echo date('d F Y', strtotime($exam['exam_date'])); ?>
                                                    (<?php echo date('l', strtotime($exam['exam_date'])); ?>)
                                                </div>
                                                <div class="exam-details">
                                                    <span><i class="fas fa-graduation-cap"></i> <?php echo isset($exam['class_name']) ? $exam['class_name'] : (isset($exam['course_name']) ? $exam['course_name'] : 'N/A'); ?></span>
                                                    <span><i class="fas fa-book"></i> <?php echo $exam['course_name']; ?></span>
                                                    <span><i class="fas fa-star"></i> <?php echo $exam['total_marks']; ?> নম্বর</span>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="p-4 text-center text-muted">
                                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                                            <p>কোন আসন্ন পরীক্ষা নেই।</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if ($upcoming_exams_result && $upcoming_exams_result->num_rows > 0): ?>
                            <div class="card-footer text-center">
                                <a href="manage_exams.php" class="btn btn-sm btn-outline-primary">সব পরীক্ষা দেখুন</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Recent Exams -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-history me-2"></i> সাম্প্রতিক পরীক্ষাসমূহ</h5>
                            </div>
                            <div class="card-body p-0">
                                <div class="exam-list">
                                    <?php if ($recent_exams_result && $recent_exams_result->num_rows > 0): ?>
                                        <?php while ($exam = $recent_exams_result->fetch_assoc()): ?>
                                            <div class="exam-item">
                                                <div class="exam-name"><?php echo $exam['exam_name']; ?></div>
                                                <div class="exam-date">
                                                    <i class="fas fa-calendar-alt"></i> 
                                                    <?php echo date('d F Y', strtotime($exam['exam_date'])); ?>
                                                    (<?php echo date('l', strtotime($exam['exam_date'])); ?>)
                                                </div>
                                                <div class="exam-details">
                                                    <span><i class="fas fa-graduation-cap"></i> <?php echo isset($exam['class_name']) ? $exam['class_name'] : (isset($exam['course_name']) ? $exam['course_name'] : 'N/A'); ?></span>
                                                    <span><i class="fas fa-book"></i> <?php echo $exam['course_name']; ?></span>
                                                    <span><i class="fas fa-star"></i> <?php echo $exam['total_marks']; ?> নম্বর</span>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="p-4 text-center text-muted">
                                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                                            <p>কোন সাম্প্রতিক পরীক্ষা নেই।</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if ($recent_exams_result && $recent_exams_result->num_rows > 0): ?>
                            <div class="card-footer text-center">
                                <a href="marks_entry.php" class="btn btn-sm btn-outline-primary">মার্কস এন্ট্রি করুন</a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Add hover effect to workflow cards
            $('.workflow-card').hover(function() {
                $(this).find('.action-link.primary').addClass('animate__animated animate__pulse');
            }, function() {
                $(this).find('.action-link.primary').removeClass('animate__animated animate__pulse');
            });
            
            // Add animation to stats cards
            $('.dashboard-card').each(function(index) {
                setTimeout(() => {
                    $(this).addClass('animate__animated animate__fadeInUp');
                }, 100 * index);
            });
        });
    </script>
</body>
</html> 