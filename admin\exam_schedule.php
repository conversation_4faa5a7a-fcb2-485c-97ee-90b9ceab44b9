<?php
session_start();
require_once 'includes/db_connection.php';
require_once 'includes/functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

$success_message = '';
$error_message = '';

// Check if exam_schedule table exists, if not create it
$check_table_query = "SHOW TABLES LIKE 'exam_schedule'";
$table_exists = $conn->query($check_table_query);

if ($table_exists->num_rows == 0) {
    // Create exam_schedule table
    $create_table_sql = "CREATE TABLE IF NOT EXISTS exam_schedule (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        exam_date DATE NOT NULL,
        start_time TIME NOT NULL,
        end_time TIME NOT NULL,
        room_no VARCHAR(20) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($create_table_sql)) {
        $success_message = "পরীক্ষার সময়সূচি টেবিল সফলভাবে তৈরি করা হয়েছে।";
    } else {
        $error_message = "টেবিল তৈরি করার সময় ত্রুটি: " . $conn->error;
    }
}

// Get all exams for dropdown
$exams_query = "SELECT id, exam_name FROM exams ORDER BY exam_name";
$exams_result = $conn->query($exams_query);

// Get all subjects for dropdown
$subjects_query = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);

// Handle form submission to create/update schedule
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['submit_schedule']) && $table_exists->num_rows > 0) {
    $exam_id = $_POST['exam_id'];
    $subject_id = $_POST['subject_id'];
    $exam_date = $_POST['exam_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $room_no = $_POST['room_no'];
    
    // Check if this subject is already scheduled for this exam
    $check_query = "SELECT id FROM exam_schedule WHERE exam_id = ? AND subject_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $exam_id, $subject_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows > 0) {
        // Update existing schedule
        $schedule_id = $check_result->fetch_assoc()['id'];
        $update_query = "UPDATE exam_schedule SET exam_date = ?, start_time = ?, end_time = ?, room_no = ? WHERE id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ssssi", $exam_date, $start_time, $end_time, $room_no, $schedule_id);
        
        if ($update_stmt->execute()) {
            $success_message = "পরীক্ষার সময়সূচি সফলভাবে আপডেট করা হয়েছে।";
        } else {
            $error_message = "সময়সূচি আপডেট করার সময় একটি ত্রুটি ঘটেছে: " . $conn->error;
        }
    } else {
        // Create new schedule
        $insert_query = "INSERT INTO exam_schedule (exam_id, subject_id, exam_date, start_time, end_time, room_no) VALUES (?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("iissss", $exam_id, $subject_id, $exam_date, $start_time, $end_time, $room_no);
        
        if ($insert_stmt->execute()) {
            $success_message = "পরীক্ষার সময়সূচি সফলভাবে যোগ করা হয়েছে।";
        } else {
            $error_message = "সময়সূচি যোগ করার সময় একটি ত্রুটি ঘটেছে: " . $conn->error;
        }
    }
}

// Delete schedule if requested
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id']) && $table_exists->num_rows > 0) {
    $schedule_id = $_GET['id'];
    $delete_query = "DELETE FROM exam_schedule WHERE id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("i", $schedule_id);
    
    if ($delete_stmt->execute()) {
        $success_message = "পরীক্ষার সময়সূচি সফলভাবে মুছে ফেলা হয়েছে।";
    } else {
        $error_message = "সময়সূচি মুছে ফেলার সময় একটি ত্রুটি ঘটেছে: " . $conn->error;
    }
}

// Load current schedules
$schedules_query = "SELECT es.id, e.exam_name, s.subject_name, es.exam_date, es.start_time, es.end_time, es.room_no 
                    FROM exam_schedule es
                    JOIN exams e ON es.exam_id = e.id
                    JOIN subjects s ON es.subject_id = s.id
                    ORDER BY es.exam_date, es.start_time";
$schedules_result = $table_exists->num_rows > 0 ? $conn->query($schedules_query) : null;

// Load schedules for a specific exam if requested
if (isset($_GET['exam_id']) && $table_exists->num_rows > 0) {
    $filter_exam_id = $_GET['exam_id'];
    $filtered_schedules_query = "SELECT es.id, e.exam_name, s.subject_name, es.exam_date, es.start_time, es.end_time, es.room_no 
                                FROM exam_schedule es
                                JOIN exams e ON es.exam_id = e.id
                                JOIN subjects s ON es.subject_id = s.id
                                WHERE es.exam_id = ?
                                ORDER BY es.exam_date, es.start_time";
    $filtered_stmt = $conn->prepare($filtered_schedules_query);
    $filtered_stmt->bind_param("i", $filter_exam_id);
    $filtered_stmt->execute();
    $schedules_result = $filtered_stmt->get_result();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার সময়সূচি ব্যবস্থাপনা - <?php echo SITE_NAME; ?></title>
    <?php include 'includes/header.php'; ?>
    <style>
        .form-container {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .schedule-list {
            margin-top: 30px;
        }
        .schedule-card {
            border-left: 4px solid #007bff;
            margin-bottom: 15px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .schedule-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .exam-filter {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষার সময়সূচি ব্যবস্থাপনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="manage_exams.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tasks"></i> পরীক্ষা সম্পাদনা
                            </a>
                            <a href="marks_entry.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-pen-alt"></i> নম্বর এন্ট্রি
                            </a>
                            <a href="print_exam_schedule.php<?php echo isset($_GET['exam_id']) ? '?exam_id=' . $_GET['exam_id'] : ''; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-print"></i> প্রিন্ট করুন
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($success_message)) : ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)) : ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="form-container">
                    <h3>নতুন সময়সূচি যোগ করুন</h3>
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="exam_id" class="form-label">পরীক্ষা</label>
                                <select class="form-select" id="exam_id" name="exam_id" required>
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php while ($exam = $exams_result->fetch_assoc()) : ?>
                                    <option value="<?php echo $exam['id']; ?>"><?php echo $exam['exam_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="subject_id" class="form-label">বিষয়</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <option value="">বিষয় নির্বাচন করুন</option>
                                    <?php 
                                    // Reset the pointer to the beginning
                                    $subjects_result->data_seek(0);
                                    while ($subject = $subjects_result->fetch_assoc()) : 
                                    ?>
                                    <option value="<?php echo $subject['id']; ?>"><?php echo $subject['subject_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="exam_date" class="form-label">পরীক্ষার তারিখ</label>
                                <input type="date" class="form-control" id="exam_date" name="exam_date" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="start_time" class="form-label">শুরুর সময়</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="end_time" class="form-label">শেষের সময়</label>
                                <input type="time" class="form-control" id="end_time" name="end_time" required>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="room_no" class="form-label">কক্ষ নম্বর</label>
                                <input type="text" class="form-control" id="room_no" name="room_no" required>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" name="submit_schedule" class="btn btn-primary">
                                <i class="fas fa-save"></i> সময়সূচি সংরক্ষণ করুন
                            </button>
                        </div>
                    </form>
                </div>
                
                <div class="schedule-list">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3>পরীক্ষার সময়সূচি তালিকা</h3>
                        
                        <div class="d-flex">
                            <div class="exam-filter me-2">
                                <form method="GET" class="row g-3">
                                    <div class="col-auto">
                                        <select class="form-select" name="exam_id" onchange="this.form.submit()">
                                            <option value="">সব পরীক্ষা</option>
                                            <?php 
                                            // Reset the pointer to the beginning
                                            $exams_result->data_seek(0);
                                            while ($exam = $exams_result->fetch_assoc()) : 
                                                $selected = (isset($_GET['exam_id']) && $_GET['exam_id'] == $exam['id']) ? 'selected' : '';
                                            ?>
                                            <option value="<?php echo $exam['id']; ?>" <?php echo $selected; ?>><?php echo $exam['exam_name']; ?></option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    <?php if (isset($_GET['exam_id'])) : ?>
                                    <div class="col-auto">
                                        <a href="exam_schedule.php" class="btn btn-outline-secondary">সব দেখুন</a>
                                    </div>
                                    <?php endif; ?>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <?php if ($schedules_result && $schedules_result->num_rows > 0) : ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>#</th>
                                        <th>পরীক্ষা</th>
                                        <th>বিষয়</th>
                                        <th>তারিখ</th>
                                        <th>সময়</th>
                                        <th>কক্ষ</th>
                                        <th>অ্যাকশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $counter = 1;
                                    while ($schedule = $schedules_result->fetch_assoc()) : 
                                        $date = date('d M, Y', strtotime($schedule['exam_date']));
                                        $start = date('h:i A', strtotime($schedule['start_time']));
                                        $end = date('h:i A', strtotime($schedule['end_time']));
                                    ?>
                                    <tr>
                                        <td><?php echo $counter++; ?></td>
                                        <td><?php echo $schedule['exam_name']; ?></td>
                                        <td><?php echo $schedule['subject_name']; ?></td>
                                        <td><?php echo $date; ?></td>
                                        <td><?php echo $start . ' - ' . $end; ?></td>
                                        <td><?php echo $schedule['room_no']; ?></td>
                                        <td>
                                            <a href="exam_schedule_edit.php?id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="exam_schedule.php?action=delete&id=<?php echo $schedule['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই সময়সূচি মুছে ফেলতে চান?');">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else : ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> কোন পরীক্ষার সময়সূচি পাওয়া যায়নি।
                        </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Get elements
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');
        
        // Auto calculate end time (add 3 hours to start time)
        startTimeInput.addEventListener('change', function() {
            if (this.value) {
                const startTime = new Date(`2000-01-01T${this.value}`);
                startTime.setHours(startTime.getHours() + 3);
                const hours = String(startTime.getHours()).padStart(2, '0');
                const minutes = String(startTime.getMinutes()).padStart(2, '0');
                endTimeInput.value = `${hours}:${minutes}`;
            }
        });
    });
    </script>
</body>
</html> 