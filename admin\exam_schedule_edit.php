<?php
session_start();
require_once 'includes/db_connection.php';
require_once 'includes/functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

$success_message = '';
$error_message = '';

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: exam_schedule.php");
    exit();
}

$schedule_id = $_GET['id'];

// Get all exams for dropdown
$exams_query = "SELECT id, exam_name FROM exams WHERE status='active' ORDER BY exam_name";
$exams_result = $conn->query($exams_query);

// Get all subjects for dropdown
$subjects_query = "SELECT id, subject_name FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_query);

// Get current schedule data
$schedule_query = "SELECT es.*, e.exam_name, s.subject_name 
                  FROM exam_schedule es
                  JOIN exams e ON es.exam_id = e.id
                  JOIN subjects s ON es.subject_id = s.id
                  WHERE es.id = ?";
$schedule_stmt = $conn->prepare($schedule_query);
$schedule_stmt->bind_param("i", $schedule_id);
$schedule_stmt->execute();
$schedule_result = $schedule_stmt->get_result();

if ($schedule_result->num_rows === 0) {
    header("Location: exam_schedule.php");
    exit();
}

$schedule = $schedule_result->fetch_assoc();

// Handle form submission to update schedule
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_schedule'])) {
    $exam_id = $_POST['exam_id'];
    $subject_id = $_POST['subject_id'];
    $exam_date = $_POST['exam_date'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $room_no = $_POST['room_no'];
    
    // Update schedule
    $update_query = "UPDATE exam_schedule SET 
                    exam_id = ?, 
                    subject_id = ?, 
                    exam_date = ?, 
                    start_time = ?, 
                    end_time = ?, 
                    room_no = ? 
                    WHERE id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("iissssi", $exam_id, $subject_id, $exam_date, $start_time, $end_time, $room_no, $schedule_id);
    
    if ($update_stmt->execute()) {
        $success_message = "পরীক্ষার সময়সূচি সফলভাবে আপডেট করা হয়েছে।";
        
        // Refresh schedule data
        $schedule_stmt->execute();
        $schedule_result = $schedule_stmt->get_result();
        $schedule = $schedule_result->fetch_assoc();
    } else {
        $error_message = "সময়সূচি আপডেট করার সময় একটি ত্রুটি ঘটেছে: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার সময়সূচি সম্পাদনা - <?php echo SITE_NAME; ?></title>
    <?php include 'includes/header.php'; ?>
    <style>
        .form-container {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .schedule-info {
            background-color: #e9f7fe;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'includes/sidebar.php'; ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষার সময়সূচি সম্পাদনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_schedule.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> সময়সূচি তালিকায় ফিরে যান
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($success_message)) : ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)) : ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="schedule-info">
                    <h5><i class="fas fa-info-circle"></i> বর্তমান সময়সূচি তথ্য</h5>
                    <div class="row">
                        <div class="col-md-4">
                            <p><strong>পরীক্ষা:</strong> <?php echo $schedule['exam_name']; ?></p>
                            <p><strong>বিষয়:</strong> <?php echo $schedule['subject_name']; ?></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>তারিখ:</strong> <?php echo date('d M, Y', strtotime($schedule['exam_date'])); ?></p>
                            <p><strong>সময়:</strong> <?php echo date('h:i A', strtotime($schedule['start_time'])) . ' - ' . date('h:i A', strtotime($schedule['end_time'])); ?></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>কক্ষ নম্বর:</strong> <?php echo $schedule['room_no']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="form-container">
                    <h3>সময়সূচি সম্পাদনা করুন</h3>
                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="exam_id" class="form-label">পরীক্ষা</label>
                                <select class="form-select" id="exam_id" name="exam_id" required>
                                    <?php 
                                    $exams_result->data_seek(0);
                                    while ($exam = $exams_result->fetch_assoc()) : 
                                        $selected = ($exam['id'] == $schedule['exam_id']) ? 'selected' : '';
                                    ?>
                                    <option value="<?php echo $exam['id']; ?>" <?php echo $selected; ?>><?php echo $exam['exam_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="subject_id" class="form-label">বিষয়</label>
                                <select class="form-select" id="subject_id" name="subject_id" required>
                                    <?php 
                                    $subjects_result->data_seek(0);
                                    while ($subject = $subjects_result->fetch_assoc()) : 
                                        $selected = ($subject['id'] == $schedule['subject_id']) ? 'selected' : '';
                                    ?>
                                    <option value="<?php echo $subject['id']; ?>" <?php echo $selected; ?>><?php echo $subject['subject_name']; ?></option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="exam_date" class="form-label">পরীক্ষার তারিখ</label>
                                <input type="date" class="form-control" id="exam_date" name="exam_date" value="<?php echo $schedule['exam_date']; ?>" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="start_time" class="form-label">শুরুর সময়</label>
                                <input type="time" class="form-control" id="start_time" name="start_time" value="<?php echo $schedule['start_time']; ?>" required>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="end_time" class="form-label">শেষের সময়</label>
                                <input type="time" class="form-control" id="end_time" name="end_time" value="<?php echo $schedule['end_time']; ?>" required>
                            </div>
                            
                            <div class="col-md-2 mb-3">
                                <label for="room_no" class="form-label">কক্ষ নম্বর</label>
                                <input type="text" class="form-control" id="room_no" name="room_no" value="<?php echo $schedule['room_no']; ?>" required>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <a href="exam_schedule.php" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times"></i> বাতিল করুন
                            </a>
                            <button type="submit" name="update_schedule" class="btn btn-primary">
                                <i class="fas fa-save"></i> সময়সূচি আপডেট করুন
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Get elements
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');
        
        // Auto calculate end time (add 3 hours to start time)
        startTimeInput.addEventListener('change', function() {
            if (this.value) {
                const startTime = new Date(`2000-01-01T${this.value}`);
                startTime.setHours(startTime.getHours() + 3);
                const hours = String(startTime.getHours()).padStart(2, '0');
                const minutes = String(startTime.getMinutes()).padStart(2, '0');
                endTimeInput.value = `${hours}:${minutes}`;
            }
        });
    });
    </script>
</body>
</html> 