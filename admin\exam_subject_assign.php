<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// First check if the exam_subjects table exists
$table_check = $conn->query("SHOW TABLES LIKE 'exam_subjects'");
if ($table_check->num_rows == 0) {
    // Table doesn't exist, create it with all required columns
    $create_table_sql = "CREATE TABLE IF NOT EXISTS exam_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        exam_id INT NOT NULL,
        subject_id INT NOT NULL,
        total_marks INT NOT NULL DEFAULT 100,
        cq_marks INT NOT NULL DEFAULT 60,
        mcq_marks INT NOT NULL DEFAULT 30,
        practical_marks INT NOT NULL DEFAULT 10,
        <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
        FOREIG<PERSON> KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        UNIQUE KEY (exam_id, subject_id)
    )";
    $conn->query($create_table_sql);
} else {
    // Table exists, check for required columns
    try {
        // Check if marks column exists (old version)
        $check_marks = $conn->query("SHOW COLUMNS FROM exam_subjects LIKE 'marks'");
        if ($check_marks->num_rows > 0) {
            // Rename marks to total_marks and add other columns
    $alter_sql = "ALTER TABLE exam_subjects 
                        CHANGE COLUMN marks total_marks INT NOT NULL DEFAULT 100";
    $conn->query($alter_sql);
        }
        
        // Check for total_marks column
        $check_total = $conn->query("SHOW COLUMNS FROM exam_subjects LIKE 'total_marks'");
        if ($check_total->num_rows == 0) {
            // Add total_marks column if it doesn't exist
            $conn->query("ALTER TABLE exam_subjects ADD COLUMN total_marks INT NOT NULL DEFAULT 100");
        }
        
        // Check for cq_marks column
        $check_cq = $conn->query("SHOW COLUMNS FROM exam_subjects LIKE 'cq_marks'");
        if ($check_cq->num_rows == 0) {
            // Add cq_marks column if it doesn't exist
            $conn->query("ALTER TABLE exam_subjects ADD COLUMN cq_marks INT NOT NULL DEFAULT 60");
        }
        
        // Check for mcq_marks column
        $check_mcq = $conn->query("SHOW COLUMNS FROM exam_subjects LIKE 'mcq_marks'");
        if ($check_mcq->num_rows == 0) {
            // Add mcq_marks column if it doesn't exist
            $conn->query("ALTER TABLE exam_subjects ADD COLUMN mcq_marks INT NOT NULL DEFAULT 30");
        }
        
        // Check for practical_marks column
        $check_practical = $conn->query("SHOW COLUMNS FROM exam_subjects LIKE 'practical_marks'");
        if ($check_practical->num_rows == 0) {
            // Add practical_marks column if it doesn't exist
            $conn->query("ALTER TABLE exam_subjects ADD COLUMN practical_marks INT NOT NULL DEFAULT 10");
        }
    } catch (Exception $e) {
        // If there's an error, recreate the table
        $conn->query("DROP TABLE IF EXISTS exam_subjects");
        $create_table_sql = "CREATE TABLE exam_subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            exam_id INT NOT NULL,
            subject_id INT NOT NULL,
            total_marks INT NOT NULL DEFAULT 100,
            cq_marks INT NOT NULL DEFAULT 60,
            mcq_marks INT NOT NULL DEFAULT 30,
            practical_marks INT NOT NULL DEFAULT 10,
            FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
            UNIQUE KEY (exam_id, subject_id)
        )";
        $conn->query($create_table_sql);
    }
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Assign subjects to exam
    if (isset($_POST['assign_subjects'])) {
        $exam_id = $_POST['exam_id'];
        $subject_ids = isset($_POST['subject_ids']) ? $_POST['subject_ids'] : [];
        $total_marks = isset($_POST['total_marks']) ? $_POST['total_marks'] : [];
        $cq_marks = isset($_POST['cq_marks']) ? $_POST['cq_marks'] : [];
        $mcq_marks = isset($_POST['mcq_marks']) ? $_POST['mcq_marks'] : [];
        $practical_marks = isset($_POST['practical_marks']) ? $_POST['practical_marks'] : [];
        
        // First, delete all existing subject assignments for this exam
        $delete_sql = "DELETE FROM exam_subjects WHERE exam_id = ?";
        $delete_stmt = $conn->prepare($delete_sql);
        $delete_stmt->bind_param("i", $exam_id);
        $delete_stmt->execute();
        
        // Then add the new assignments
        if (!empty($subject_ids)) {
            $insert_sql = "INSERT INTO exam_subjects (exam_id, subject_id, total_marks, cq_marks, mcq_marks, practical_marks) VALUES (?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_sql);
            
            foreach ($subject_ids as $index => $subject_id) {
                $total = isset($total_marks[$index]) ? intval($total_marks[$index]) : 100;
                
                // Ensure we have proper values for all mark types
                $cq = isset($cq_marks[$index]) ? intval($cq_marks[$index]) : $total;
                
                // Important fix: Check if the key exists in the array before accessing its value
                // Use empty string as default if key doesn't exist, then convert to integer (which will be 0)
                $mcq = isset($mcq_marks[$index]) ? intval($mcq_marks[$index]) : 0;
                $practical = isset($practical_marks[$index]) ? intval($practical_marks[$index]) : 0;
                
                // For debugging purposes
                error_log("Processing Subject ID: $subject_id, Total: $total, CQ: $cq, MCQ: $mcq, Practical: $practical");
                
                $insert_stmt->bind_param("iiiiii", $exam_id, $subject_id, $total, $cq, $mcq, $practical);
                $insert_stmt->execute();
            }
            
            $success_message = "বিষয়গুলি সফলভাবে পরীক্ষার সাথে সংযুক্ত করা হয়েছে";
        }
    }
}

// Get all exams with class join
$exams_sql = "SELECT e.* 
              FROM exams e 
              ORDER BY e.exam_date DESC";
$exams_result = $conn->query($exams_sql);

// Get all subjects
$subjects_sql = "SELECT * FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_sql);

// Convert subjects_result to an array of subjects
$subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[] = $subject;
}

// Get all assigned subjects for specific exam if exam_id is provided
$assigned_subjects = [];
if (isset($_GET['exam_id'])) {
    $exam_id = $_GET['exam_id'];
    
    $assigned_sql = "SELECT es.*, s.subject_name, s.subject_code 
                    FROM exam_subjects es 
                    JOIN subjects s ON es.subject_id = s.id 
                    WHERE es.exam_id = ?";
    $assigned_stmt = $conn->prepare($assigned_sql);
    $assigned_stmt->bind_param("i", $exam_id);
    $assigned_stmt->execute();
    $assigned_result = $assigned_stmt->get_result();
    
    while ($subject = $assigned_result->fetch_assoc()) {
        $assigned_subjects[] = $subject;
    }
    
    // Get the exam details
    $exam_details_sql = "SELECT e.* 
                        FROM exams e 
                        WHERE e.id = ?";
    $exam_details_stmt = $conn->prepare($exam_details_sql);
    $exam_details_stmt->bind_param("i", $exam_id);
    $exam_details_stmt->execute();
    $exam_details = $exam_details_stmt->get_result()->fetch_assoc();
}

// Add debug logging to see what's coming from database
foreach ($assigned_subjects as $as) {
    error_log("Subject ID: {$as['subject_id']}, Total: {$as['total_marks']}, CQ: {$as['cq_marks']}, MCQ: {$as['mcq_marks']}, Practical: {$as['practical_marks']}");
}

// Function to determine the pattern type based on marks
function get_pattern_type($cq_marks, $mcq_marks, $practical_marks) {
    if ($cq_marks > 0 && $mcq_marks > 0 && $practical_marks > 0) {
        return 'cq_mcq_practical';
    } elseif ($cq_marks > 0 && $mcq_marks > 0) {
        return 'cq_mcq';
    } elseif ($cq_marks > 0 && $practical_marks > 0) {
        return 'cq_practical';
    } elseif ($mcq_marks > 0 && $practical_marks == 0 && $cq_marks == 0) {
        return 'mcq_only';
    } else {
        return 'cq_only';
    }
}

// Function to check if field should be displayed based on pattern
function shouldShowField($fieldType, $pattern) {
    if ($fieldType === 'mcq') {
        return in_array($pattern, ['cq_mcq', 'all', 'custom']);
    } elseif ($fieldType === 'practical') {
        return in_array($pattern, ['cq_practical', 'all', 'custom']);
    }
    return true; // CQ is always shown
}

// Get exams
$exams_query = "SELECT * FROM exams ORDER BY id DESC";
$exams_result = $conn->query($exams_query);
$exams = [];
if ($exams_result->num_rows > 0) {
    while ($row = $exams_result->fetch_assoc()) {
        $exams[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষায় বিষয় যুক্ত করুন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        /* Card styles */
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.25rem;
            border-top-left-radius: 0.5rem !important;
            border-top-right-radius: 0.5rem !important;
        }
        
        .card-body {
            padding: 1.25rem;
        }
        
        /* Exam card */
        .exam-card {
            background-color: #fff;
            border-radius: 0.6rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.25rem;
            transition: all 0.3s ease;
            border-left: 3px solid #007bff;
            padding: 1.25rem;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        .exam-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-3px);
        }
        
        .exam-card h5 {
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #212529;
        }
        
        .exam-card .badge {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .exam-card .btn {
            margin-top: auto;
        }
        
        /* Subject items */
        .subject-item {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1rem;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .subject-item:hover {
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
        }
        
        .subject-item.selected {
            background-color: #e7f3ff;
            border-color: #b3d7ff;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
        }
        
        .subject-item .form-check-label {
            font-weight: 500;
            color: #212529;
        }
        
        /* Marks section */
        .marks-section {
            background-color: #f9f9f9;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 0.75rem;
            border: 1px solid #f0f0f0;
        }
        
        .marks-input {
            width: 80px;
            display: inline-block;
        }
        
        .marks-row {
            display: flex;
            gap: 0.75rem;
            margin-top: 0.75rem;
        }
        
        .marks-col {
            flex: 1;
        }
        
        .marks-label {
            font-size: 0.85rem;
            font-weight: 500;
            margin-bottom: 0.35rem;
            display: block;
        }
        
        .optional-field {
            color: #17a2b8;
            font-weight: 500;
        }
        
        /* Button styles */
        .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        
        .btn-outline-primary {
            color: #0d6efd;
            border-color: #0d6efd;
        }
        
        .btn-outline-primary:hover {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: #fff;
        }
        
        /* Custom pattern settings */
        .custom-pattern-settings {
            background-color: #f1f3f5;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 0.75rem;
            border: 1px solid #e5e5e5;
            transition: all 0.3s ease;
        }
        
        /* Range sliders */
        .form-range::-webkit-slider-thumb {
            background: #007bff;
        }
        
        .form-range::-moz-range-thumb {
            background: #007bff;
        }
        
        .form-range::-ms-thumb {
            background: #007bff;
        }
        
        .custom-cq-percent::-webkit-slider-runnable-track {
            background: linear-gradient(90deg, #007bff 0%, #007bff 100%);
            height: 4px;
        }
        
        .custom-mcq-percent::-webkit-slider-runnable-track {
            background: linear-gradient(90deg, #28a745 0%, #28a745 100%);
            height: 4px;
        }
        
        .custom-practical-percent::-webkit-slider-runnable-track {
            background: linear-gradient(90deg, #17a2b8 0%, #17a2b8 100%);
            height: 4px;
        }
        
        /* Alert styles */
        .alert {
            border-radius: 0.5rem;
            padding: 0.75rem 1.25rem;
            margin-bottom: 1.5rem;
            border: none;
        }
        
        .alert-success {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        
        .alert-info {
            background-color: #cff4fc;
            color: #055160;
        }
        
        /* Form controls */
        .form-control, .form-select {
            border-radius: 0.375rem;
            border: 1px solid #dee2e6;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        
        /* Pattern card styles */
        .pattern-card {
            background-color: white;
            border-radius: 0.375rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
            cursor: pointer;
            height: 100%;
        }
        
        .pattern-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        .pattern-card .card-body {
            padding: 1rem;
        }
        
        .pattern-card h6 {
            font-weight: 600;
            margin-bottom: 0.375rem;
        }
        
        /* Section title */
        .section-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        /* Badge styles */
        .badge {
            font-weight: 500;
            padding: 0.35em 0.65em;
        }
        
        .badge-primary {
            background-color: #0d6efd;
        }
        
        .badge-secondary {
            background-color: #6c757d;
        }
        
        /* Box shadows and transitions */
        .shadow-sm {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
        }
        
        .shadow {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        }
        
        .transition {
            transition: all 0.3s ease !important;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-4 border-bottom">
                    <h1 class="h2"><i class="fas fa-book-reader text-primary me-2"></i>পরীক্ষায় বিষয় যুক্ত করুন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-arrow-left"></i> পরীক্ষা ব্যবস্থাপনায় ফিরে যান
                        </a>
                    </div>
                </div>
                
                <?php if (isset($success_message)) : ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Left Column: Exam Selection -->
                    <?php if (!isset($_GET['exam_id'])): ?>
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>পরীক্ষা নির্বাচন করুন</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php if ($exams_result && $exams_result->num_rows > 0): ?>
                                        <?php while ($exam = $exams_result->fetch_assoc()): ?>
                                            <div class="col-md-4">
                                                <div class="exam-card">
                                                    <h5><i class="fas fa-clipboard-list text-primary me-2"></i><?php echo $exam['exam_name']; ?></h5>
                                                    <div class="mb-3">
                                                        <span class="badge bg-primary">
                                                            <i class="fas fa-calendar-alt me-1"></i> <?php echo date('d F Y', strtotime($exam['exam_date'])); ?>
                                                        </span>
                                                        <span class="badge bg-secondary">
                                                            <i class="fas fa-graduation-cap me-1"></i> <?php echo isset($exam['class_name']) ? $exam['class_name'] : (isset($exam['course_name']) ? $exam['course_name'] : 'N/A'); ?>
                                                        </span>
                                                    </div>
                                                    <a href="?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-plus-circle me-1"></i> বিষয় যুক্ত করুন
                                                    </a>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <div class="col-12">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle"></i> কোন পরীক্ষা যোগ করা হয়নি।
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php else: ?>
                    <!-- Subject Selection and Assignment -->
                    <div class="col-md-12">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>বিষয় সংযুক্ত করুন: <?php echo $exam_details['exam_name']; ?></h5>
                                <span class="badge bg-primary">
                                    <?php echo date('d F Y', strtotime($exam_details['exam_date'])); ?>
                                </span>
                                <span class="badge bg-secondary">
                                    <?php echo isset($exam_details['class_name']) ? $exam_details['class_name'] : (isset($exam_details['course_name']) ? $exam_details['course_name'] : 'N/A'); ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <?php if (isset($success_message)) : ?>
                                    <div class="alert alert-success"><?php echo $success_message; ?></div>
                                <?php endif; ?>
                                
                                <form method="post" action="" id="subjectAssignForm">
                                    <div class="form-group">
                                        <label for="exam_id">পরীক্ষা নির্বাচন করুন <span class="text-danger">*</span></label>
                                        <select class="form-control" id="exam_id" name="exam_id" required>
                                            <option value="">পরীক্ষা নির্বাচন করুন</option>
                                            <?php foreach ($exams as $exam) : ?>
                                                <option value="<?php echo $exam['id']; ?>" <?php echo (isset($_GET['exam_id']) && $_GET['exam_id'] == $exam['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $exam['exam_name']; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    
                                    <div id="subject-assignment" style="<?php echo isset($_GET['exam_id']) ? 'display:block;' : 'display:none;'; ?>">
                                        <h5 class="mb-3 mt-4">বিষয় নির্বাচন করুন এবং নম্বর বণ্টন করুন</h5>
                                        
                                        <div class="form-group">
                                    <div class="row">
                                                <div class="col-md-3">
                                                    <strong>বিষয়</strong>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>মোট নম্বর</strong>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>নম্বর বণ্টন</strong>
                                                </div>
                                                </div>
                                            </div>
                                            
                                        <!-- Apply to All Controls -->
                                        <div class="card mb-4">
                                            <div class="card-header">
                                                <h6 class="mb-0">সকল নির্বাচিত বিষয়ে প্রয়োগ করুন</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <label for="totalMarksForAll" class="form-label">মোট নম্বর:</label>
                                                        <input type="number" id="totalMarksForAll" class="form-control" value="100" min="0">
                                                    </div>
                                                    <div class="col-md-3">
                                                        <label for="patternForAll" class="form-label">প্যাটার্ন:</label>
                                                        <select id="patternForAll" class="form-control">
                                                    <option value="cq_mcq">CQ + MCQ</option>
                                                            <option value="cq">CQ Only</option>
                                                            <option value="mcq_only">MCQ Only</option>
                                                            <option value="cq_mcq_practical">CQ + MCQ + Practical</option>
                                                    <option value="cq_practical">CQ + Practical</option>
                                                            <option value="all">All Types</option>
                                                            <option value="custom">Custom</option>
                                                </select>
                                            </div>
                                                    <div class="col-md-6">
                                                        <div id="marksDistributionFields">
                                                            <div class="row">
                                                                <div class="col-md-4">
                                                                    <label for="cqMarksForAll" class="form-label">CQ নম্বর:</label>
                                                                    <input type="number" id="cqMarksForAll" class="form-control" min="0">
                                            </div>
                                                    <div class="col-md-4">
                                                                    <label for="mcqMarksForAll" class="form-label">MCQ নম্বর:</label>
                                                                    <input type="number" id="mcqMarksForAll" class="form-control" min="0">
                                                    </div>
                                                    <div class="col-md-4">
                                                                    <label for="practicalMarksForAll" class="form-label">Practical নম্বর:</label>
                                                                    <input type="number" id="practicalMarksForAll" class="form-control" min="0">
                                                            </div>
                                                        </div>
                                                    </div>
                                                            </div>
                                                        </div>
                                                <div class="mt-3">
                                                    <button type="button" id="applyMarksBtn" class="btn btn-primary">
                                                        <i class="fas fa-check-circle me-1"></i> প্রয়োগ করুন
                                                    </button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        <div id="subject-list">
                                            <?php if (isset($_GET['exam_id'])) : ?>
                                                <?php foreach ($subjects as $subject) : ?>
                                                <?php 
                                                    $is_assigned = false;
                                                    $total_marks = 100;
                                                    $cq_marks = 70;
                                                    $mcq_marks = 30;
                                                    $practical_marks = 0;
                                                    
                                                    // Check if this subject is already assigned
                                                    foreach ($assigned_subjects as $assigned) {
                                                        if ($assigned['subject_id'] == $subject['id']) {
                                                            $is_assigned = true;
                                                            $total_marks = $assigned['total_marks'];
                                                            $cq_marks = $assigned['cq_marks'];
                                                            $mcq_marks = $assigned['mcq_marks'];
                                                            $practical_marks = $assigned['practical_marks'];
                                                            break;
                                                        }
                                                    }
                                                    
                                                    $pattern_type = get_pattern_type($cq_marks, $mcq_marks, $practical_marks);
                                                    ?>
                                                    
                                                    <div class="form-group subject-item mb-4 pb-3 border-bottom">
                                                        <div class="row align-items-center">
                                                            <div class="col-md-3">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" class="custom-control-input subject-checkbox" 
                                                                           id="subject_<?php echo $subject['id']; ?>" 
                                                                           name="subject_ids[]" 
                                                                           value="<?php echo $subject['id']; ?>" 
                                                                           <?php echo $is_assigned ? 'checked' : ''; ?>>
                                                                    <label class="custom-control-label" for="subject_<?php echo $subject['id']; ?>">
                                                                        <?php echo $subject['subject_name']; ?>
                                                                </label>
                                                            </div>
                                                                </div>
                                                            
                                                            <div class="col-md-3">
                                                                <input type="number" class="form-control total-marks" 
                                                                       name="total_marks[]" 
                                                                       value="<?php echo $total_marks; ?>" 
                                                                       min="0" max="200" required
                                                                       <?php echo !$is_assigned ? 'disabled' : ''; ?>>
                                                            </div>
                                                            
                                                            <div class="col-md-6">
                                                                        <div class="row">
                                                                    <div class="col-md-4 mb-2">
                                                                        <select class="form-control pattern-select" 
                                                                                <?php echo !$is_assigned ? 'disabled' : ''; ?>>
                                                                            <option value="cq_mcq" <?php echo $pattern_type == 'cq_mcq' ? 'selected' : ''; ?>>CQ + MCQ</option>
                                                                            <option value="cq_only" <?php echo $pattern_type == 'cq_only' ? 'selected' : ''; ?>>CQ Only</option>
                                                                            <option value="mcq_only" <?php echo $pattern_type == 'mcq_only' ? 'selected' : ''; ?>>MCQ Only</option>
                                                                            <option value="cq_mcq_practical" <?php echo $pattern_type == 'cq_mcq_practical' ? 'selected' : ''; ?>>CQ + MCQ + Practical</option>
                                                                            <option value="cq_practical" <?php echo $pattern_type == 'cq_practical' ? 'selected' : ''; ?>>CQ + Practical</option>
                                                                        </select>
                                                                                </div>
                                                                    
                                                                    <div class="col-md-8">
                                                                        <div class="marks-fields">
                                                                            <div class="row mb-2 cq-field" style="<?php echo in_array($pattern_type, ['cq_only', 'cq_mcq', 'cq_mcq_practical', 'cq_practical']) ? '' : 'display: none;'; ?>">
                                                                                <div class="col-4">
                                                                                    <label class="small">CQ Marks:</label>
                                                                            </div>
                                                                                <div class="col-8">
                                                                                    <input type="number" class="form-control form-control-sm cq-marks" 
                                                                                           name="cq_marks[]" 
                                                                                           value="<?php echo $cq_marks; ?>" 
                                                                                           min="0" 
                                                                                           <?php echo !$is_assigned ? 'disabled' : ''; ?>>
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            <div class="row mb-2 mcq-field" style="<?php echo in_array($pattern_type, ['mcq_only', 'cq_mcq', 'cq_mcq_practical']) ? '' : 'display: none;'; ?>">
                                                                                <div class="col-4">
                                                                                    <label class="small">MCQ Marks:</label>
                                                                                </div>
                                                                                <div class="col-8">
                                                                                    <input type="number" class="form-control form-control-sm mcq-marks" 
                                                                                           name="mcq_marks[]" 
                                                                                           value="<?php echo $mcq_marks; ?>" 
                                                                                           min="0" 
                                                                                           <?php echo !$is_assigned ? 'disabled' : ''; ?>>
                                                                            </div>
                                                                                </div>
                                                                            
                                                                    <div class="row practical-field" style="<?php echo in_array($pattern_type, ['cq_mcq_practical', 'cq_practical']) ? '' : 'display: none;'; ?>">
                                                                        <div class="col-4">
                                                                            <label class="small">Practical:</label>
                                                                            </div>
                                                                        <div class="col-8">
                                                                        <input type="number" class="form-control form-control-sm practical-marks" 
                                                                                   name="practical_marks[]" 
                                                                                   value="<?php echo $practical_marks; ?>" 
                                                                                   min="0" 
                                                                                   <?php echo !$is_assigned ? 'disabled' : ''; ?>>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                            </div>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                    </div>
                                    
                                        <div class="form-group mt-4">
                                        <button type="submit" name="assign_subjects" class="btn btn-primary">
                                                বিষয়গুলি সংরক্ষণ করুন
                                        </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function(){
            // Show/hide marks section based on checkbox
            $('.subject-checkbox').change(function(){
                var marksSection = $(this).closest('.subject-item').find('.marks-section');
                var subjectItem = $(this).closest('.subject-item');
                
                if($(this).is(':checked')){
                    marksSection.show();
                    subjectItem.addClass('selected');
                } else {
                    marksSection.hide();
                    subjectItem.removeClass('selected');
                }
            });
            
            // Select all subjects
            $('#selectAllSubjects').click(function(){
                var isChecked = $(this).is(':checked');
                $('.subject-checkbox').prop('checked', isChecked);
                $('.subject-checkbox').trigger('change');
            });
            
            // Show/hide fields based on exam pattern
            $('.pattern-select').change(function(){
                var pattern = $(this).val();
                var item = $(this).closest('.subject-item');
                
                // Handle field visibility
                if(pattern == 'cq'){
                    item.find('.mcq-field').hide();
                    item.find('.practical-field').hide();
                    item.find('.custom-pattern-settings').hide();
                } else if(pattern == 'cq_mcq'){
                        item.find('.mcq-field').show();
                    item.find('.practical-field').hide();
                    item.find('.custom-pattern-settings').hide();
                    
                    // Set default values
                    item.find('.cq-marks').val(Math.round(item.find('.total-marks').val() * 0.7));
                    item.find('.mcq-marks').val(Math.round(item.find('.total-marks').val() * 0.3));
                    item.find('.practical-marks').val(0);
                } else if(pattern == 'cq_practical'){
                    item.find('.mcq-field').hide();
                        item.find('.practical-field').show();
                    item.find('.custom-pattern-settings').hide();
                    
                    // Set default values
                    item.find('.cq-marks').val(Math.round(item.find('.total-marks').val() * 0.75));
                    item.find('.mcq-marks').val(0);
                    item.find('.practical-marks').val(Math.round(item.find('.total-marks').val() * 0.25));
                } else if(pattern == 'all'){
                    item.find('.mcq-field').show();
                    item.find('.practical-field').show();
                    item.find('.custom-pattern-settings').hide();
                    
                    // Set default values
                    item.find('.cq-marks').val(Math.round(item.find('.total-marks').val() * 0.6));
                    item.find('.mcq-marks').val(Math.round(item.find('.total-marks').val() * 0.3));
                    item.find('.practical-marks').val(Math.round(item.find('.total-marks').val() * 0.1));
                } else if(pattern == 'custom'){
                    item.find('.mcq-field').show();
                    item.find('.practical-field').show();
                    item.find('.custom-pattern-settings').show();
                    
                    // Update slider values to match current values
                    var total = parseInt(item.find('.total-marks').val());
                    var cq = parseInt(item.find('.cq-marks').val());
                    var mcq = parseInt(item.find('.mcq-marks').val());
                    var practical = parseInt(item.find('.practical-marks').val());
                    
                    // Convert to percentages
                    var cqPercent = Math.round((cq / total) * 100);
                    var mcqPercent = Math.round((mcq / total) * 100);
                    var practicalPercent = Math.round((practical / total) * 100);
                    
                    // Set slider values
                    item.find('.custom-cq-percent').val(cqPercent);
                    item.find('.custom-mcq-percent').val(mcqPercent);
                    item.find('.custom-practical-percent').val(practicalPercent);
                    
                    // Update text
                    item.find('.custom-cq-percent-value').text(cqPercent);
                    item.find('.custom-mcq-percent-value').text(mcqPercent);
                    item.find('.custom-practical-percent-value').text(practicalPercent);
                    
                    // Check for warning
                    updateCustomTotalWarning(item);
                }
                
                // Set exam pattern
                item.find('.pattern-select').val(pattern).trigger('change');
            });
            
            // Apply marks to all selected subjects
            $('#applyMarksBtn').click(function(){
                var totalMarks = $('#totalMarksForAll').val();
                var pattern = $('#patternForAll').val();
                var cqMarks = $('#cqMarksForAll').val();
                var mcqMarks = $('#mcqMarksForAll').val();
                var practicalMarks = $('#practicalMarksForAll').val();
                
                $('.subject-checkbox:checked').each(function(){
                    var item = $(this).closest('.subject-item');
                    
                    // Set total marks
                    item.find('.total-marks').val(totalMarks);
                    
                    // Set exam pattern
                    item.find('.pattern-select').val(pattern).trigger('change');
                    
                    // Set individual marks if they were entered
                    if (cqMarks) item.find('.cq-marks').val(cqMarks);
                    if (mcqMarks) item.find('.mcq-marks').val(mcqMarks);
                    if (practicalMarks) item.find('.practical-marks').val(practicalMarks);
                });
            });
            
            // Show/hide marks distribution fields based on pattern
            $('#patternForAll').change(function(){
                var pattern = $(this).val();
                
                if(pattern == 'cq'){
                    $('#marksDistributionFields').find('input[id$="ForAll"]').not('#cqMarksForAll').closest('label').hide();
                    $('#marksDistributionFields').find('input[id$="ForAll"]').not('#cqMarksForAll').hide();
                    $('#cqMarksForAll').closest('label').show();
                    $('#cqMarksForAll').show();
                } else if(pattern == 'cq_mcq'){
                    $('#marksDistributionFields').find('input[id$="ForAll"]').closest('label').show();
                    $('#marksDistributionFields').find('input[id$="ForAll"]').show();
                    $('#practicalMarksForAll').closest('label').hide();
                    $('#practicalMarksForAll').hide();
                } else if(pattern == 'cq_practical'){
                    $('#marksDistributionFields').find('input[id$="ForAll"]').closest('label').show();
                    $('#marksDistributionFields').find('input[id$="ForAll"]').show();
                    $('#mcqMarksForAll').closest('label').hide();
                    $('#mcqMarksForAll').hide();
                } else {
                    $('#marksDistributionFields').find('input[id$="ForAll"]').closest('label').show();
                    $('#marksDistributionFields').find('input[id$="ForAll"]').show();
                }
            });
            
            // Recalculate marks when total is changed
            $('.total-marks').change(function(){
                var item = $(this).closest('.subject-item');
                var pattern = item.find('.pattern-select').val();
                var total = parseInt($(this).val());
                
                // Recalculate based on pattern
                if(pattern == 'cq'){
                    item.find('.cq-marks').val(total);
                } else if(pattern == 'cq_mcq'){
                    item.find('.cq-marks').val(Math.round(total * 0.7));
                    item.find('.mcq-marks').val(Math.round(total * 0.3));
                } else if(pattern == 'cq_practical'){
                    item.find('.cq-marks').val(Math.round(total * 0.75));
                    item.find('.practical-marks').val(Math.round(total * 0.25));
                } else if(pattern == 'all'){
                    item.find('.cq-marks').val(Math.round(total * 0.6));
                    item.find('.mcq-marks').val(Math.round(total * 0.3));
                    item.find('.practical-marks').val(Math.round(total * 0.1));
                } else if(pattern == 'custom'){
                    // Get percentages from sliders
                    var cqPercent = parseInt(item.find('.custom-cq-percent').val());
                    var mcqPercent = parseInt(item.find('.custom-mcq-percent').val());
                    var practicalPercent = parseInt(item.find('.custom-practical-percent').val());
                    
                    // Calculate marks based on percentages
                    item.find('.cq-marks').val(Math.round(total * cqPercent / 100));
                    item.find('.mcq-marks').val(Math.round(total * mcqPercent / 100));
                    item.find('.practical-marks').val(Math.round(total * practicalPercent / 100));
                }
            });
            
            // Function to check if custom percentages add up to 100%
            function updateCustomTotalWarning(item) {
                var cqPercent = parseInt(item.find('.custom-cq-percent').val());
                var mcqPercent = parseInt(item.find('.custom-mcq-percent').val());
                var practicalPercent = parseInt(item.find('.custom-practical-percent').val());
                
                var total = cqPercent + mcqPercent + practicalPercent;
                
                if(total != 100) {
                    item.find('.custom-total-warning').show();
                } else {
                    item.find('.custom-total-warning').hide();
                }
            }
            
            // Update marks when custom percentages change
            $('.custom-cq-percent, .custom-mcq-percent, .custom-practical-percent').on('input', function(){
                var item = $(this).closest('.subject-item');
                var total = parseInt(item.find('.total-marks').val());
                
                // Get percentages
                var cqPercent = parseInt(item.find('.custom-cq-percent').val());
                var mcqPercent = parseInt(item.find('.custom-mcq-percent').val());
                var practicalPercent = parseInt(item.find('.custom-practical-percent').val());
                
                // Calculate marks
                item.find('.cq-marks').val(Math.round(total * cqPercent / 100));
                item.find('.mcq-marks').val(Math.round(total * mcqPercent / 100));
                item.find('.practical-marks').val(Math.round(total * practicalPercent / 100));
                    
                    // Update text display
                item.find('.custom-cq-percent-value').text(cqPercent);
                item.find('.custom-mcq-percent-value').text(mcqPercent);
                item.find('.custom-practical-percent-value').text(practicalPercent);
                    
                    // Update warning
                    updateCustomTotalWarning(item);
            });
        });
    </script>
</body>
</html> 