<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get all exams with class join
$exams_sql = "SELECT e.id, e.exam_name, e.exam_date, e.total_marks, c.id as class_id, c.class_name 
              FROM exams e, classes c 
              WHERE e.id = c.id 
              ORDER BY e.exam_date DESC";
$exams_result = $conn->query($exams_sql);

// Get all classes for filter
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);

// Initialize filters with default values
$filter_class_id = isset($_GET['class_id']) ? $_GET['class_id'] : '';
$filter_exam_type = isset($_GET['exam_type']) ? $_GET['exam_type'] : '';
$filter_start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$filter_end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d', strtotime('+90 days'));

// Apply filters if submitted
if (isset($_GET['filter_timetable'])) {
    $where_clauses = [];
    $params = [];
    $types = "";

    if (!empty($filter_class_id)) {
        $where_clauses[] = "c.id = ?";
        $params[] = $filter_class_id;
        $types .= "i";
    }

    if (!empty($filter_exam_type)) {
        $where_clauses[] = "e.exam_type = ?";
        $params[] = $filter_exam_type;
        $types .= "s";
    }

    if (!empty($filter_start_date)) {
        $where_clauses[] = "e.exam_date >= ?";
        $params[] = $filter_start_date;
        $types .= "s";
    }

    if (!empty($filter_end_date)) {
        $where_clauses[] = "e.exam_date <= ?";
        $params[] = $filter_end_date;
        $types .= "s";
    }

    $exams_sql = "SELECT e.id, e.exam_name, e.exam_date, e.total_marks, c.id as class_id, c.class_name 
                  FROM exams e, classes c 
                  WHERE e.id = c.id";

    if (!empty($where_clauses)) {
        $exams_sql .= " AND " . implode(" AND ", $where_clauses);
    }

    $exams_sql .= " ORDER BY e.exam_date ASC";

    $stmt = $conn->prepare($exams_sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $exams_result = $stmt->get_result();
}

// Check if we need to fetch subject details for an exam
$active_exam_id = isset($_GET['exam_id']) ? $_GET['exam_id'] : null;
$exam_subjects = [];

if ($active_exam_id) {
    // Get exam details
    $exam_details_sql = "SELECT e.id, e.exam_name, e.exam_date, e.total_marks, c.id as class_id, c.class_name 
                        FROM exams e, classes c 
                        WHERE e.id = c.id 
                        AND e.id = ?";
    $exam_details_stmt = $conn->prepare($exam_details_sql);
    $exam_details_stmt->bind_param("i", $active_exam_id);
    $exam_details_stmt->execute();
    $exam_details = $exam_details_stmt->get_result()->fetch_assoc();
    
    // Get subject details
    $subjects_sql = "SELECT es.*, s.subject_name, s.subject_code 
                    FROM exam_subjects es 
                    JOIN subjects s ON es.subject_id = s.id 
                    WHERE es.exam_id = ?";
    $subjects_stmt = $conn->prepare($subjects_sql);
    $subjects_stmt->bind_param("i", $active_exam_id);
    $subjects_stmt->execute();
    $subjects_result = $subjects_stmt->get_result();
    
    while ($subject = $subjects_result->fetch_assoc()) {
        $exam_subjects[] = $subject;
    }
}

// Group exams by date
$exams_by_date = [];
if ($exams_result && $exams_result->num_rows > 0) {
    while ($exam = $exams_result->fetch_assoc()) {
        $date = $exam['exam_date'];
        if (!isset($exams_by_date[$date])) {
            $exams_by_date[$date] = [];
        }
        $exams_by_date[$date][] = $exam;
    }
}

// Array of exam types for filter dropdown
$exam_types = [
    'সামায়িক', 'অর্ধ-বার্ষিক', 'বার্ষিক', 'মডেল টেস্ট', 'নির্বাচনী', 'সাপ্তাহিক', 'মাসিক', 'অন্যান্য'
];

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার সময়সূচী - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .timetable-day {
            background-color: #e9ecef;
            border-radius: 4px;
            padding: 10px 15px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .timetable-day h4 {
            margin-bottom: 10px;
            font-weight: 500;
        }
        .exam-card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 15px;
            transition: all 0.3s ease;
            padding: 15px;
            border-left: 3px solid #28a745;
        }
        .exam-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .exam-time {
            font-weight: 500;
            color: #dc3545;
        }
        .filter-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .detail-table {
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .detail-table th {
            background-color: #f8f9fa;
        }
        .mark-distribution {
            display: flex;
            gap: 15px;
            font-size: 0.8rem;
            margin-top: 8px;
        }
        .mark-badge {
            padding: 3px 8px;
            border-radius: 12px;
            display: inline-block;
        }
        .cq-badge {
            background-color: #e7f3ff;
            color: #0d6efd;
        }
        .mcq-badge {
            background-color: #d1e7dd;
            color: #198754;
        }
        .practical-badge {
            background-color: #f8d7da;
            color: #dc3545;
        }
        .badge-day {
            background-color: #6f42c1;
            color: white;
            font-size: 0.8rem;
            padding: 3px 6px;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container-fluid, .row, main {
                width: 100% !important;
                padding: 0 !important;
                margin: 0 !important;
            }
            body {
                background-color: white !important;
            }
            .exam-card {
                break-inside: avoid;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষার সময়সূচী</h1>
                    <div class="btn-toolbar mb-2 mb-md-0 no-print">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-list"></i> পরীক্ষা তালিকা
                        </a>
                        <button onclick="window.print()" class="btn btn-sm btn-success">
                            <i class="fas fa-print"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <!-- Filter Card -->
                <div class="filter-card no-print">
                    <h5><i class="fas fa-filter me-2"></i>ফিল্টার অপশন</h5>
                    <form method="get" action="" class="row g-3">
                        <div class="col-md-3">
                            <label for="class_id" class="form-label">শ্রেণী:</label>
                            <select class="form-select" id="class_id" name="class_id">
                                <option value="">সব শ্রেণী</option>
                                <?php 
                                $classes_result->data_seek(0);
                                while ($class = $classes_result->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo $class['id']; ?>" <?php echo ($filter_class_id == $class['id']) ? 'selected' : ''; ?>>
                                        <?php echo $class['class_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="exam_type" class="form-label">পরীক্ষার ধরন:</label>
                            <select class="form-select" id="exam_type" name="exam_type">
                                <option value="">সব ধরন</option>
                                <?php foreach ($exam_types as $type): ?>
                                    <option value="<?php echo $type; ?>" <?php echo ($filter_exam_type == $type) ? 'selected' : ''; ?>>
                                        <?php echo $type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">শুরুর তারিখ:</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $filter_start_date; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">শেষের তারিখ:</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $filter_end_date; ?>">
                        </div>
                        <div class="col-12">
                            <button type="submit" name="filter_timetable" class="btn btn-primary">
                                <i class="fas fa-filter"></i> ফিল্টার করুন
                            </button>
                            <a href="exam_timetable.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-sync"></i> রিসেট করুন
                            </a>
                        </div>
                    </form>
                </div>
                
                <?php if ($active_exam_id && isset($exam_details)): ?>
                <!-- Single Exam Details View -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4><?php echo $exam_details['exam_name']; ?> - বিস্তারিত তথ্য</h4>
                        <?php if (isset($exam_details['exam_type']) && !empty($exam_details['exam_type'])): ?>
                            <span class="badge bg-warning text-dark"><?php echo $exam_details['exam_type']; ?></span>
                        <?php endif; ?>
                        <span class="badge bg-info"><?php echo $exam_details['class_name']; ?></span>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>তারিখ:</strong> <?php echo date('d F Y', strtotime($exam_details['exam_date'])); ?></p>
                                <?php if (isset($exam_details['start_time']) && isset($exam_details['end_time'])): ?>
                                <p><strong>সময়:</strong> <?php echo $exam_details['start_time'] ? date('h:i A', strtotime($exam_details['start_time'])) : ''; ?> - 
                                   <?php echo $exam_details['end_time'] ? date('h:i A', strtotime($exam_details['end_time'])) : ''; ?></p>
                                <?php endif; ?>
                                <p><strong>মোট নম্বর:</strong> <?php echo $exam_details['total_marks']; ?></p>
                                <?php if (isset($exam_details['passing_marks'])): ?>
                                <p><strong>পাস নম্বর:</strong> <?php echo $exam_details['passing_marks']; ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if (isset($exam_details['status'])): ?>
                                <p><strong>স্ট্যাটাস:</strong> 
                                    <span class="badge <?php echo $exam_details['status'] == 'active' ? 'bg-success' : 'bg-danger'; ?>">
                                        <?php echo $exam_details['status'] == 'active' ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                    </span>
                                </p>
                                <?php endif; ?>
                                <?php if (isset($exam_details['description']) && !empty($exam_details['description'])): ?>
                                    <p><strong>বিবরণ:</strong> <?php echo $exam_details['description']; ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if (!empty($exam_subjects)): ?>
                            <h5 class="mt-4">পরীক্ষার বিষয়সমূহ</h5>
                            <div class="table-responsive detail-table">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>বিষয়</th>
                                            <th>কোড</th>
                                            <th>মোট নম্বর</th>
                                            <th>নম্বর বণ্টন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($exam_subjects as $subject): ?>
                                            <tr>
                                                <td><?php echo $subject['subject_name']; ?></td>
                                                <td><?php echo $subject['subject_code']; ?></td>
                                                <td><?php echo $subject['total_marks']; ?></td>
                                                <td>
                                                    <div class="mark-distribution">
                                                        <span class="mark-badge cq-badge">CQ: <?php echo $subject['cq_marks']; ?></span>
                                                        <span class="mark-badge mcq-badge">MCQ: <?php echo $subject['mcq_marks']; ?></span>
                                                        <span class="mark-badge practical-badge">Practical: <?php echo $subject['practical_marks']; ?></span>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-info-circle"></i> এই পরীক্ষার জন্য কোন বিষয় যুক্ত করা হয়নি।
                                <a href="exam_subject_assign.php?exam_id=<?php echo $active_exam_id; ?>" class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="fas fa-plus"></i> বিষয় যুক্ত করুন
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <div class="mt-3 no-print">
                            <a href="exam_timetable.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> সময়সূচীতে ফিরে যান
                            </a>
                            <a href="exam_subject_assign.php?exam_id=<?php echo $active_exam_id; ?>" class="btn btn-primary ms-2">
                                <i class="fas fa-edit"></i> বিষয় পরিবর্তন করুন
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php else: ?>
                <!-- Timetable View -->
                <div id="timetable">
                    <?php if (empty($exams_by_date)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> নির্বাচিত সময়কালে কোন পরীক্ষা নেই।
                        </div>
                    <?php else: ?>
                        <div class="text-center mb-4">
                            <h3>পরীক্ষার সময়সূচী</h3>
                            <p class="text-muted">
                                <?php echo date('d F Y', strtotime($filter_start_date)); ?> থেকে 
                                <?php echo date('d F Y', strtotime($filter_end_date)); ?> পর্যন্ত
                            </p>
                            <?php if (!empty($filter_class_id)): 
                                $class_name = "";
                                $classes_result->data_seek(0);
                                while ($class = $classes_result->fetch_assoc()) {
                                    if ($class['id'] == $filter_class_id) {
                                        $class_name = $class['class_name'];
                                        break;
                                    }
                                }
                            ?>
                                <p><strong>শ্রেণী:</strong> <?php echo $class_name; ?></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($filter_exam_type)): ?>
                                <p><strong>পরীক্ষার ধরন:</strong> <?php echo $filter_exam_type; ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <?php foreach ($exams_by_date as $date => $exams): ?>
                            <div class="timetable-day">
                                <h4>
                                    <?php echo date('d F Y', strtotime($date)); ?>
                                    <span class="badge-day"><?php echo date('l', strtotime($date)); ?></span>
                                </h4>
                                
                                <div class="row">
                                    <?php foreach ($exams as $exam): ?>
                                        <div class="col-md-6 col-lg-4">
                                            <div class="exam-card">
                                                <h5><?php echo $exam['exam_name']; ?></h5>
                                                <div class="mb-2">
                                                    <span class="badge bg-secondary"><?php echo $exam['class_name']; ?></span>
                                                    <?php if (isset($exam['exam_type']) && !empty($exam['exam_type'])): ?>
                                                        <span class="badge bg-info"><?php echo $exam['exam_type']; ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div>
                                                    <?php if (isset($exam['start_time']) && !empty($exam['start_time']) && isset($exam['end_time']) && !empty($exam['end_time'])): ?>
                                                        <p class="mb-1 exam-time">
                                                            <i class="far fa-clock"></i> 
                                                            <?php echo date('h:i A', strtotime($exam['start_time'])); ?> - 
                                                            <?php echo date('h:i A', strtotime($exam['end_time'])); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                    
                                                    <p class="mb-1"><strong>মোট নম্বর:</strong> <?php echo $exam['total_marks']; ?></p>
                                                    <?php if (isset($exam['passing_marks'])): ?>
                                                    <p class="mb-0"><strong>পাস নম্বর:</strong> <?php echo $exam['passing_marks']; ?></p>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <div class="mt-2 no-print">
                                                    <a href="?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i> বিস্তারিত দেখুন
                                                    </a>
                                                    <a href="exam_subject_assign.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-outline-success">
                                                        <i class="fas fa-edit"></i> বিষয় সংযোজন
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Date filter validation
            $('#end_date').change(function() {
                const startDate = new Date($('#start_date').val());
                const endDate = new Date($(this).val());
                
                if (endDate < startDate) {
                    alert('শেষের তারিখ শুরুর তারিখের আগে হতে পারে না!');
                    $(this).val($('#start_date').val());
                }
            });
        });
    </script>
</body>
</html> 