<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get all classes for dropdown
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);

// Get statistics
$exams_count = $conn->query("SELECT COUNT(*) as count FROM exams")->fetch_assoc()['count'];
$subjects_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch_assoc()['count'];
$assigned_subjects_count = $conn->query("SELECT COUNT(*) as count FROM exam_subjects")->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষা কার্যপ্রণালী - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .dashboard-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            height: 100%;
            border-top: 3px solid #007bff;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .dashboard-card .icon {
            font-size: 36px;
            color: #007bff;
            margin-bottom: 15px;
        }
        .dashboard-card h3 {
            font-size: 18px;
            margin-bottom: 10px;
            color: #343a40;
        }
        .dashboard-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
        .dashboard-card .count {
            font-size: 24px;
            font-weight: bold;
            color: #343a40;
            margin-bottom: 5px;
        }
        .workflow-step {
            position: relative;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }
        .workflow-step h4 {
            color: #007bff;
            font-size: 18px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .workflow-step h4 .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            margin-right: 10px;
            font-size: 16px;
        }
        .workflow-step p {
            color: #6c757d;
            margin-bottom: 15px;
        }
        .workflow-step .action-buttons {
            margin-top: 15px;
        }
        .workflow-step:after {
            content: '';
            position: absolute;
            bottom: -20px;
            left: 40px;
            height: 20px;
            width: 2px;
            background-color: #dee2e6;
            z-index: 0;
        }
        .workflow-step:last-child:after {
            display: none;
        }
        .workflow-step.completed {
            border-left-color: #28a745;
        }
        .workflow-step.completed h4 {
            color: #28a745;
        }
        .workflow-step.completed h4 .step-number {
            background-color: #28a745;
        }
        .workflow-step.ongoing {
            border-left-color: #fd7e14;
        }
        .workflow-step.ongoing h4 {
            color: #fd7e14;
        }
        .workflow-step.ongoing h4 .step-number {
            background-color: #fd7e14;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষা কার্যপ্রণালী</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_management.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-arrow-left"></i> পরীক্ষা ব্যবস্থাপনায় ফিরে যান
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> প্রিন্ট করুন
                        </button>
                    </div>
                </div>
                
                <!-- Overview Cards -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-file-alt"></i></div>
                            <div class="count"><?php echo $exams_count; ?></div>
                            <h3>মোট পরীক্ষা</h3>
                            <p>সিস্টেমে যোগ করা সকল পরীক্ষার সংখ্যা</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-book"></i></div>
                            <div class="count"><?php echo $subjects_count; ?></div>
                            <h3>মোট বিষয়</h3>
                            <p>বিদ্যালয়ে পাঠদানকৃত বিষয়সমূহ</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="dashboard-card">
                            <div class="icon"><i class="fas fa-link"></i></div>
                            <div class="count"><?php echo $assigned_subjects_count; ?></div>
                            <h3>সংযুক্ত বিষয়</h3>
                            <p>পরীক্ষার সাথে সংযুক্ত বিষয়সমূহ</p>
                        </div>
                    </div>
                </div>
                
                <!-- Workflow Process -->
                <h3 class="mb-4">পরীক্ষা পরিচালনার ধারাবাহিক প্রক্রিয়া</h3>
                
                <div class="workflow-container">
                    <!-- Step 1: Create Exam -->
                    <div class="workflow-step completed">
                        <h4><span class="step-number">1</span> পরীক্ষা তৈরি করুন</h4>
                        <p>নতুন পরীক্ষা যোগ করুন এবং এর বিবরণ সেট করুন। প্রতিটি পরীক্ষার জন্য তারিখ, শ্রেণী, মোট নম্বর এবং অন্যান্য প্রয়োজনীয় তথ্য প্রদান করুন।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        পরীক্ষার নাম
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        পরীক্ষার তারিখ 
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        শ্রেণী নির্বাচন
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="exam_management.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-plus-circle"></i> পরীক্ষা তৈরি করুন
                            </a>
                        </div>
                    </div>
                    
                    <!-- Step 2: Assign Subjects -->
                    <div class="workflow-step ongoing">
                        <h4><span class="step-number">2</span> বিষয় সংযুক্ত করুন</h4>
                        <p>প্রতিটি পরীক্ষার জন্য প্রয়োজনীয় বিষয়সমূহ যুক্ত করুন। প্রতিটি বিষয়ের জন্য নম্বর বণ্টন, পূর্ণমান এবং পাশ মার্কস নির্ধারণ করুন।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        বিষয় নির্বাচন
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        প্রতি বিষয়ের নম্বর নির্ধারণ
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        একাধিক বিষয় একসাথে যোগ করা
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="exam_subject_assign.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-book"></i> বিষয় সংযুক্ত করুন
                            </a>
                        </div>
                    </div>
                    
                    <!-- Step 3: Generate Exam Schedule -->
                    <div class="workflow-step">
                        <h4><span class="step-number">3</span> পরীক্ষার রুটিন তৈরি করুন</h4>
                        <p>পরীক্ষার সময়সূচী তৈরি করুন। তারিখ অনুযায়ী বিষয়ভিত্তিক রুটিন প্রস্তুত করুন, যা ছাত্র-ছাত্রী, শিক্ষকদের জন্য প্রিন্ট বা শেয়ার করা যাবে।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        তারিখ ভিত্তিক রুটিন
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        শ্রেণী অনুযায়ী ফিল্টার
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        বিষয় অনুযায়ী ফিল্টার
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="exam_timetable.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-calendar-alt"></i> রুটিন দেখুন
                            </a>
                            <a href="generate_schedule.php" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-print"></i> প্রিন্ট করুন
                            </a>
                        </div>
                    </div>
                    
                    <!-- Step 4: Marks Entry -->
                    <div class="workflow-step">
                        <h4><span class="step-number">4</span> মার্কস এন্ট্রি করুন</h4>
                        <p>ছাত্র-ছাত্রীদের পরীক্ষার ফলাফল এন্ট্রি করুন। প্রতিটি বিষয়ের জন্য প্রাপ্ত নম্বর রেকর্ড করুন, শ্রেণী এবং বিভাগ অনুযায়ী।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        প্রাপ্ত নম্বর এন্ট্রি
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        শ্রেণী অনুযায়ী গ্রুপ করুন
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ছাত্র অনুযায়ী ফিল্টার
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="marks_entry.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-edit"></i> মার্কস এন্ট্রি
                            </a>
                            <a href="detailed_marks_entry.php" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-th-list"></i> বিস্তারিত মার্কস এন্ট্রি
                            </a>
                        </div>
                    </div>
                    
                    <!-- Step 5: Generate Result -->
                    <div class="workflow-step">
                        <h4><span class="step-number">5</span> ফলাফল প্রস্তুত করুন</h4>
                        <p>সকল ছাত্র-ছাত্রীদের পরীক্ষার ফলাফল প্রস্তুত করুন। মোট নম্বর, শতাংশ, গ্রেড এবং মেধা তালিকা তৈরি করুন।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ফলাফল সংকলন
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        মেধা তালিকা তৈরি
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        গ্রেড নির্ধারণ
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="result_management.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-clipboard-list"></i> ফলাফল তৈরি করুন
                            </a>
                        </div>
                    </div>
                    
                    <!-- Step 6: Publish Result -->
                    <div class="workflow-step">
                        <h4><span class="step-number">6</span> ফলাফল প্রকাশ করুন</h4>
                        <p>ফলাফল প্রকাশ করুন এবং ছাত্র-ছাত্রীদের জন্য অ্যাকসেসযোগ্য করুন। মার্কশিট তৈরি করুন এবং প্রিন্ট করুন। বিদ্যালয়ের ওয়েবসাইটে ফলাফল প্রকাশ করতে পারেন।</p>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        ফলাফল প্রকাশ
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        মার্কশিট প্রিন্ট
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        অভিভাবক অ্যাকসেস
                                        <span class="badge bg-primary rounded-pill"><i class="fas fa-check"></i></span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="action-buttons">
                            <a href="results.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-chart-bar"></i> ফলাফল দেখুন
                            </a>
                            <a href="generate_marksheet.php" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-print"></i> মার্কশিট তৈরি করুন
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Add scroll animation
            $('.btn-primary').on('click', function() {
                $(this).addClass('animate__animated animate__pulse');
                setTimeout(() => {
                    $(this).removeClass('animate__animated animate__pulse');
                }, 1000);
            });
        });
    </script>
</body>
</html> 