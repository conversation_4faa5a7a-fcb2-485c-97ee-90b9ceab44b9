<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Function to remove duplicate fees
function removeDuplicateFees($conn) {
    try {
        // Find duplicate fees (same student_id and fee_type)
        $findDuplicatesQuery = "
            SELECT student_id, fee_type, MIN(id) as keep_id, COUNT(*) as count
            FROM fees 
            GROUP BY student_id, fee_type
            HAVING COUNT(*) > 1
        ";
        
        $duplicateResult = $conn->query($findDuplicatesQuery);
        
        if ($duplicateResult && $duplicateResult->num_rows > 0) {
            // Start a transaction
            $conn->begin_transaction();
            
            $duplicatesRemoved = 0;
            
            while ($row = $duplicateResult->fetch_assoc()) {
                $studentId = $row['student_id'];
                $feeType = $row['fee_type'];
                $keepId = $row['keep_id'];
                
                // Delete duplicate fees (keep the one with the lowest ID)
                $deleteDuplicatesQuery = "
                    DELETE FROM fees 
                    WHERE student_id = ? AND fee_type = ? AND id != ?
                ";
                
                $stmt = $conn->prepare($deleteDuplicatesQuery);
                $stmt->bind_param("isi", $studentId, $feeType, $keepId);
                $stmt->execute();
                
                $duplicatesRemoved += $stmt->affected_rows;
            }
            
            // Commit the transaction
            $conn->commit();
            
            if ($duplicatesRemoved > 0) {
                return $duplicatesRemoved;
            }
        }
        
        return 0;
    } catch (Exception $e) {
        // If an error occurs, roll back the transaction
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        error_log("Error removing duplicate fees: " . $e->getMessage());
        return 0;
    }
}

// Remove duplicate fees when the page loads
$duplicatesRemoved = removeDuplicateFees($conn);

// Ensure necessary tables exist
$feesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_status ENUM('due', 'partial', 'paid', 'overpaid') DEFAULT 'due',
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feesTableQuery);

// Ensure fee_payments table exists with notes column
$feePaymentsTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATETIME NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
)";
$conn->query($feePaymentsTableQuery);

// Check if notes column exists in fees table
$columnCheckQuery = "SHOW COLUMNS FROM fees LIKE 'notes'";
$columnResult = $conn->query($columnCheckQuery);
$notesColumnExists = $columnResult->num_rows > 0;

// Add notes column if it doesn't exist
if (!$notesColumnExists) {
    $addColumnQuery = "ALTER TABLE fees ADD COLUMN notes TEXT DEFAULT NULL";
    $conn->query($addColumnQuery);
}

// Check if notes column exists in fee_payments table
$columnCheckQuery = "SHOW COLUMNS FROM fee_payments LIKE 'notes'";
$columnResult = $conn->query($columnCheckQuery);
$paymentNotesColumnExists = $columnResult->num_rows > 0;

// Add notes column if it doesn't exist
if (!$paymentNotesColumnExists) {
    $addColumnQuery = "ALTER TABLE fee_payments ADD COLUMN notes TEXT DEFAULT NULL";
    $conn->query($addColumnQuery);
}

// Get fee types
$feeTypesQuery = "SHOW COLUMNS FROM fee_types LIKE 'amount'";
$amountColumnExists = $conn->query($feeTypesQuery)->num_rows > 0;

if (!$amountColumnExists) {
    // If amount column doesn't exist, add it
    $addColumnQuery = "ALTER TABLE fee_types ADD COLUMN amount DECIMAL(10,2) DEFAULT NULL";
    $conn->query($addColumnQuery);
}

$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Check if payment_status enum includes 'overpaid'
$checkEnumQuery = "SHOW COLUMNS FROM fees LIKE 'payment_status'";
$enumResult = $conn->query($checkEnumQuery);
$enumColumn = $enumResult->fetch_assoc();

// Check if the enum has 'overpaid' in it
if ($enumColumn && !strpos($enumColumn['Type'], 'overpaid')) {
    // Add 'overpaid' to the enum values
    $modifyEnumQuery = "ALTER TABLE fees MODIFY COLUMN payment_status ENUM('due', 'partial', 'paid', 'overpaid') DEFAULT 'due'";
    $conn->query($modifyEnumQuery);
}

// Handle fee assignment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Debug data
    $errorMessage = "POST data received:\n";
    foreach ($_POST as $key => $value) {
        $errorMessage .= "$key: ";
        if (is_array($value)) {
            $errorMessage .= implode(', ', $value);
        } else {
            $errorMessage .= $value;
        }
        $errorMessage .= "\n";
    }
    
    if (isset($_POST['assign_fee'])) {
        // Get form data and validate
        $errors = [];
        
        // Determine student selection type
        $studentSelectionType = $_POST['student_selection_type'] ?? '';
        
        // Get session ID and class ID
        $sessionId = $_POST['session_id'] ?? 0;
        $classId = $_POST['class_id'] ?? 0;
        
        // Validate session and class
        if (empty($sessionId)) {
            $errors[] = "দয়া করে একটি সেশন নির্বাচন করুন।";
        }
        
        if (empty($classId)) {
            $errors[] = "দয়া করে একটি ক্লাস নির্বাচন করুন।";
        }
        
        // Handle different student selection types
        $selectedStudents = [];
        
        if ($studentSelectionType === 'single') {
            // Single student
            $studentId = $_POST['student_id'] ?? 0;
            if (empty($studentId)) {
                $errors[] = "দয়া করে একজন শিক্ষার্থী নির্বাচন করুন।";
            } else {
                $selectedStudents[] = $studentId;
            }
        } elseif ($studentSelectionType === 'multiple') {
            // Multiple students
            if (!isset($_POST['selected_students']) || empty($_POST['selected_students'])) {
                $errors[] = "দয়া করে কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন।";
            } else {
                $selectedStudents = $_POST['selected_students'];
            }
        } elseif ($studentSelectionType === 'all') {
            // All students in the class
            $allStudentsQuery = "SELECT id FROM students WHERE class_id = ? AND session_id = ?";
            $stmt = $conn->prepare($allStudentsQuery);
            $stmt->bind_param("ii", $classId, $sessionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            while ($row = $result->fetch_assoc()) {
                $selectedStudents[] = $row['id'];
            }
            
            if (empty($selectedStudents)) {
                $errors[] = "এই ক্লাসে কোন শিক্ষার্থী নেই।";
            }
        } else {
            $errors[] = "দয়া করে শিক্ষার্থী নির্বাচন ধরন নির্বাচন করুন।";
        }
        
        // Get fee data
        $feeType = $_POST['fee_type'] ?? '';
        $amount = $_POST['amount'] ?? 0;
        $dueDate = $_POST['due_date'] ?? '';
        $notes = $_POST['notes'] ?? '';
        $paid = $_POST['paid'] ?? 0;
        
        // Ensure notes is not null
        $notes = $notes ?? '';
        
        // Validation for fee data
        if (empty($feeType)) {
            $errors[] = "দয়া করে ফি টাইপ নির্বাচন করুন।";
        }
        
        if (empty($amount) || !is_numeric($amount) || $amount <= 0) {
            $errors[] = "দয়া করে সঠিক পরিমাণ প্রদান করুন।";
        }
        
        if (!empty($paid) && (!is_numeric($paid) || $paid < 0)) {
            $errors[] = "পরিশোধিত পরিমাণ সঠিক নয়।";
        }
        
        if (empty($dueDate)) {
            $errors[] = "দয়া করে শেষ তারিখ নির্বাচন করুন।";
        }
        
        // Process if no errors
        if (empty($errors)) {
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($selectedStudents as $studentId) {
                // Calculate due amount
                $dueAmount = $amount - $paid;
                
                // Determine payment status
                $paymentStatus = 'due';
                if ($dueAmount < 0) {
                    $paymentStatus = 'overpaid';
                } elseif ($dueAmount == 0) {
                    $paymentStatus = 'paid';
                } elseif ($paid > 0) {
                    $paymentStatus = 'partial';
                }
                
                // Check if fee already exists for this student and type (enhance the duplicate check)
                $checkQuery = "SELECT f.id, f.amount, f.fee_type, CONCAT(s.first_name, ' ', s.last_name) as name, 
                               f.due_date, f.payment_status, f.paid
                               FROM fees f 
                               JOIN students s ON f.student_id = s.id 
                               WHERE f.student_id = ? AND f.fee_type = ?";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bind_param("is", $studentId, $feeType);
                $checkStmt->execute();
                $checkResult = $checkStmt->get_result();
                
                // Skip if this exact fee type already exists for this student
                if ($checkResult->num_rows > 0) {
                    $duplicateFee = $checkResult->fetch_assoc();
                    $dueDateFormatted = date('d-m-Y', strtotime($duplicateFee['due_date']));
                    
                    // Log detailed information about the duplicate fee
                    error_log("Duplicate fee skipped: Student ID: $studentId, Name: {$duplicateFee['name']}, Fee Type: {$duplicateFee['fee_type']}, " .
                             "Existing Fee ID: {$duplicateFee['id']}, Amount: {$duplicateFee['amount']}, Due Date: $dueDateFormatted, " . 
                             "Payment Status: {$duplicateFee['payment_status']}, Paid Amount: {$duplicateFee['paid']}");
                    $errorCount++;
                    continue; // Skip this student
                }
                
                // Insert new fee
                if ($notesColumnExists) {
                    $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status, notes) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    
                    // Debug
                    $errorMessage = "Parameters: studentId=$studentId, feeType=$feeType, amount=$amount, paid=$paid, dueDate=$dueDate, paymentStatus=$paymentStatus, notes=$notes";
                    
                    try {
                        $insertStmt = $conn->prepare($insertQuery);
                        if (!$insertStmt) {
                            throw new Exception("Error preparing statement: " . $conn->error);
                        }
                        
                        // Make sure notes is not null
                        if ($notes === null) {
                            $notes = '';
                        }
                        
                        $bindResult = $insertStmt->bind_param("isddsss", $studentId, $feeType, $amount, $paid, $dueDate, $paymentStatus, $notes);
                        if (!$bindResult) {
                            throw new Exception("Error binding parameters: " . $insertStmt->error);
                        }
                        
                        if ($insertStmt->execute()) {
                            $feeId = $conn->insert_id;
                            $successCount++;
                            
                            // If there's initial payment, record it
                            if (!empty($paid) && $paid > 0) {
                                $paymentQuery = "";
                                $initialPaymentNote = "Initial payment";
                                
                                if ($paymentNotesColumnExists) {
                                    $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, notes) 
                                                  VALUES (?, ?, NOW(), 'cash', ?)";
                                    $paymentStmt = $conn->prepare($paymentQuery);
                                    $paymentStmt->bind_param("ids", $feeId, $paid, $initialPaymentNote);
                                } else {
                                    $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method) 
                                                  VALUES (?, ?, NOW(), 'cash')";
                                    $paymentStmt = $conn->prepare($paymentQuery);
                                    $paymentStmt->bind_param("id", $feeId, $paid);
                                }
                                
                                $paymentStmt->execute();
                            }
                        } else {
                            $errorCount++;
                        }
                    } catch (Exception $e) {
                        // Log error and continue with next student
                        error_log("Error assigning fee: " . $e->getMessage());
                        $errorCount++;
                        continue;
                    }
                } else {
                    $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status) 
                                    VALUES (?, ?, ?, ?, ?, ?)";
                    
                    try {
                        $insertStmt = $conn->prepare($insertQuery);
                        if (!$insertStmt) {
                            throw new Exception("Error preparing statement: " . $conn->error);
                        }
                        
                        $bindResult = $insertStmt->bind_param("isddss", $studentId, $feeType, $amount, $paid, $dueDate, $paymentStatus);
                        if (!$bindResult) {
                            throw new Exception("Error binding parameters: " . $insertStmt->error);
                        }
                        
                        if ($insertStmt->execute()) {
                            $feeId = $conn->insert_id;
                            $successCount++;
                            
                            // If there's initial payment, record it
                            if (!empty($paid) && $paid > 0) {
                                $paymentQuery = "";
                                $initialPaymentNote = "Initial payment";
                                
                                if ($paymentNotesColumnExists) {
                                    $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, notes) 
                                                 VALUES (?, ?, NOW(), 'cash', ?)";
                                    $paymentStmt = $conn->prepare($paymentQuery);
                                    $paymentStmt->bind_param("ids", $feeId, $paid, $initialPaymentNote);
                                } else {
                                    $paymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method) 
                                                 VALUES (?, ?, NOW(), 'cash')";
                                    $paymentStmt = $conn->prepare($paymentQuery);
                                    $paymentStmt->bind_param("id", $feeId, $paid);
                                }
                                
                                $paymentStmt->execute();
                            }
                        } else {
                            $errorCount++;
                        }
                    } catch (Exception $e) {
                        // Log error and continue with next student
                        error_log("Error assigning fee: " . $e->getMessage());
                        $errorCount++;
                        continue;
                    }
                }
            }
            
            if ($successCount > 0) {
                $successMessage = "সফলভাবে $successCount জন শিক্ষার্থীর ফি এসাইন করা হয়েছে!";
                if ($errorCount > 0) {
                    $successMessage .= " $errorCount জন শিক্ষার্থীর ফি এসাইন করা যায়নি (একই ধরনের ফি ইতিমধ্যে এসাইন করা আছে)। বিস্তারিত জানতে লগ ফাইল দেখুন।";
                }
            } else {
                $errorMessage = "ফি এসাইন করতে সমস্যা হয়েছে। দয়া করে আবার চেষ্টা করুন। সম্ভবত সবগুলি শিক্ষার্থীর জন্য এই ফি ইতিমধ্যে এসাইন করা আছে। বিস্তারিত জানতে লগ ফাইল দেখুন।";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি এসাইন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .sidebar .nav-link {
            color: #f8f9fa;
            font-weight: 500;
        }
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .sidebar ul ul .nav-link {
            padding: 0.3rem 1rem;
            font-size: 0.9rem;
        }
        .sidebar ul ul .nav-link.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                        <ul class="nav flex-column ms-3">
                            <li class="nav-item">
                                <a class="nav-link active" href="fee_assign.php">
                                    <i class="fas fa-angle-right me-1"></i> ফি এসাইন
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="fee_collect.php">
                                    <i class="fas fa-angle-right me-1"></i> ফি কালেকশন
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="container-fluid px-4">
                    <h1 class="mt-4">ফি এসাইন</h1>
                    <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                        <li class="breadcrumb-item"><a href="fees.php">ফি ম্যানেজমেন্ট</a></li>
                        <li class="breadcrumb-item active">ফি এসাইন</li>
                    </ol>
                    
                    <div class="d-flex justify-content-end mb-4">
                        <a href="fee_collect.php" class="btn btn-success">
                            <i class="fas fa-money-bill me-1"></i> ফি কালেকশন
                        </a>
                    </div>
                    
                    <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success"><?= $successMessage ?></div>
                    <?php endif; ?>
                    
                    <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger"><?= $errorMessage ?></div>
                    <?php endif; ?>
                    
                    <?php if (isset($duplicatesRemoved) && $duplicatesRemoved > 0): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> সিস্টেম <?= $duplicatesRemoved ?> টি ডুপ্লিকেট ফি রেকর্ড অপসারণ করেছে।
                    </div>
                    <?php endif; ?>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            ফি এসাইন করুন
                            <div class="float-end">
                                <button type="button" id="resetFeeAssignBtn" class="btn btn-warning btn-sm">
                                    <i class="fas fa-redo-alt me-1"></i> ফরম রিসেট করুন
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <form method="post" action="" id="fee-assign-form">
                                <input type="hidden" name="assign_fee" value="1">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="session_id" class="form-label">সেশন নির্বাচন করুন</label>
                                        <select class="form-select" id="session_id" name="session_id" required>
                                            <option value="">সেশন নির্বাচন করুন</option>
                                            <?php 
                                            $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                                            $sessions = $conn->query($sessionsQuery);
                                            if ($sessions && $sessions->num_rows > 0):
                                                while ($session = $sessions->fetch_assoc()):
                                            ?>
                                                <option value="<?= $session['id'] ?>"><?= htmlspecialchars($session['session_name']) ?></option>
                                            <?php 
                                                endwhile;
                                            endif;
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                        <select class="form-select" id="class_id" name="class_id" required>
                                            <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="student_selection_type" class="form-label">শিক্ষার্থী নির্বাচন ধরন</label>
                                        <select class="form-select" id="student_selection_type" name="student_selection_type" required>
                                            <option value="single">একজন শিক্ষার্থী</option>
                                            <option value="multiple">একাধিক শিক্ষার্থী</option>
                                            <option value="all">সমস্ত শিক্ষার্থী</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Single Student Selection -->
                                <div class="row mb-3" id="single_student_section">
                                    <div class="col-md-12">
                                        <label for="student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন</label>
                                        <select class="form-select" id="student_id" name="student_id">
                                            <option value="">প্রথমে ক্লাস নির্বাচন করুন</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Multiple Students Selection -->
                                <div class="row mb-3 d-none" id="multiple_students_section">
                                    <div class="col-md-12">
                                        <label class="form-label">শিক্ষার্থী নির্বাচন করুন</label>
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="select_all_students">
                                                        <label class="form-check-label" for="select_all_students">
                                                            সকল শিক্ষার্থী নির্বাচন করুন
                                                        </label>
                                                    </div>
                                                    <div class="w-25">
                                                        <input type="text" class="form-control form-control-sm" id="student_search" placeholder="শিক্ষার্থী খুঁজুন...">
                                                    </div>
                                                </div>
                                                <hr>
                                                <div id="student_list_container" style="max-height: 400px; overflow-y: auto;">
                                                    <p class="text-muted">প্রথমে ক্লাস নির্বাচন করুন</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- All Students Message -->
                                <div class="row mb-3 d-none" id="all_students_section">
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            নির্বাচিত ক্লাসের সকল শিক্ষার্থীদের জন্য ফি এসাইন করা হবে।
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="fee_type" class="form-label">ফি-এর ধরন</label>
                                        <select class="form-select" id="fee_type" name="fee_type" required>
                                            <option value="">ফি-এর ধরন নির্বাচন করুন</option>
                                            <?php 
                                            if ($feeTypes && $feeTypes->num_rows > 0):
                                                $feeTypes->data_seek(0);
                                                while ($feeType = $feeTypes->fetch_assoc()):
                                                    $amount = isset($feeType['amount']) && !empty($feeType['amount']) ? $feeType['amount'] : null;
                                                    $amountDisplay = $amount ? ' - ৳' . number_format($amount, 2) : '';
                                            ?>
                                                <option value="<?= htmlspecialchars($feeType['name']) ?>" data-amount="<?= htmlspecialchars($amount ?? '') ?>"><?= htmlspecialchars($feeType['name']) . $amountDisplay ?></option>
                                            <?php 
                                                endwhile;
                                            endif;
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="amount" class="form-label">পরিমাণ (৳)</label>
                                        <input type="number" class="form-control" id="amount" name="amount" min="0" step="0.01" required>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <label for="due_date" class="form-label">বকেয়ার তারিখ</label>
                                        <input type="date" class="form-control" id="due_date" name="due_date" required>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="paid" class="form-label">ইতিমধ্যে পরিশোধিত (৳)</label>
                                        <input type="number" class="form-control" id="paid" name="paid" min="0" step="0.01" value="0">
                                        <div class="form-text">যদি শিক্ষার্থী আগে থেকেই কিছু টাকা পরিশোধ করে থাকে</div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="notes" class="form-label">নোট (ঐচ্ছিক)</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                    </div>
                                </div>
                                
                                <!-- Monthly Fee Selection -->
                                <div class="row mb-3 d-none" id="monthly_fee_section">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-header bg-light">
                                                <h5 class="card-title mb-0">মাসিক বেতন নির্বাচন করুন</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12 mb-3">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <div class="form-check">
                                                                <input class="form-check-input" type="checkbox" id="select_all_months">
                                                                <label class="form-check-label" for="select_all_months">
                                                                    সব মাস নির্বাচন করুন
                                                                </label>
                                                            </div>
                                                            <div>
                                                                <span class="badge bg-primary" id="month_count">নির্বাচিত: 0 মাস</span>
                                                                <span class="badge bg-success ms-2" id="total_amount">মোট: ৳0</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php 
                                                    $months = [
                                                        'জানুয়ারি', 'ফেব্রুয়ারি', 'মার্চ', 'এপ্রিল', 
                                                        'মে', 'জুন', 'জুলাই', 'আগস্ট', 
                                                        'সেপ্টেম্বর', 'অক্টোবর', 'নভেম্বর', 'ডিসেম্বর'
                                                    ];
                                                    foreach ($months as $index => $month): 
                                                    ?>
                                                    <div class="col-md-3 col-6 mb-2">
                                                        <div class="form-check">
                                                            <input class="form-check-input month-checkbox" type="checkbox" value="<?= $month ?>" id="month_<?= $index ?>" name="selected_months[]">
                                                            <label class="form-check-label" for="month_<?= $index ?>">
                                                                <?= $month ?>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <a href="fee_collect.php" class="btn btn-success">
                                        <i class="fas fa-money-check-alt me-1"></i> নতুন ফি কালেকশন
                                    </a>
                                    <div>
                                        <a href="reset_fee_assign.php" class="btn btn-danger me-2">
                                            <i class="fas fa-undo-alt me-1"></i> ফি রিসেট করুন
                                        </a>
                                        <button type="submit" class="btn btn-primary" id="assignFeeBtn" name="assign_fee" value="1">
                                            <i class="fas fa-plus-circle me-1"></i> ফি এসাইন করুন
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            ফি কালেকশন সম্পর্কে
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <h5 class="card-title">ফি কালেকশন প্রক্রিয়া</h5>
                                    <p>শিক্ষার্থীদের ফি পরিশোধ নিম্নলিখিত পদ্ধতিতে সংগ্রহ করা যাবে:</p>
                                    <ul>
                                        <li>শিক্ষার্থী অনুযায়ী বকেয়া ফি দেখা</li>
                                        <li>পরিশোধের পরিমাণ, পদ্ধতি এবং তারিখ নির্ধারণ</li>
                                        <li>পেমেন্টের রশিদ প্রদান</li>
                                        <li>বকেয়া স্ট্যাটাস আপডেট করা</li>
                                    </ul>
                                    <p>বিস্তারিত জানতে "ফি কালেকশন" বাটনে ক্লিক করুন।</p>
                                </div>
                                <div class="col-md-4 text-center d-flex flex-column justify-content-center">
                                    <i class="fas fa-hand-holding-usd fa-5x text-success mb-3"></i>
                                    <a href="fee_collect.php" class="btn btn-lg btn-success">
                                        <i class="fas fa-money-bill me-1"></i> ফি কালেকশন পৃষ্ঠায় যান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-4">
                        <a href="fee_collect.php" class="btn btn-success">
                            <i class="fas fa-money-bill me-1"></i> ফি কালেকশন করুন
                        </a>
                        <a href="fees.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Set today's date as default due date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10);
            $('#due_date').val(formattedDate);
            
            // Handle session selection
            $('#session_id').change(function() {
                const sessionId = $(this).val();
                
                // Enable/disable class dropdown
                if(sessionId) {
                    $('#class_id').html('<option value="">লোড হচ্ছে...</option>');
                    $('#class_id').prop('disabled', true);
                    
                    // AJAX call to get classes for this session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_classes_by_session',
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let options = '<option value="">ক্লাস নির্বাচন করুন</option>';
                                response.classes.forEach(function(cls) {
                                    options += `<option value="${cls.id}">${cls.class_name}</option>`;
                                });
                                $('#class_id').html(options);
                                $('#class_id').prop('disabled', false);
                            } else {
                                $('#class_id').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                            }
                        },
                        error: function() {
                            $('#class_id').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                        }
                    });
                } else {
                    // Reset class dropdown
                    $('#class_id').html('<option value="">প্রথমে সেশন নির্বাচন করুন</option>');
                    $('#class_id').prop('disabled', true);
                    
                    // Reset student selection
                    $('#student_selection_type').prop('disabled', true);
                    $('#student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#student_id').prop('disabled', true);
                    $('#student_list_container').html('<p class="text-muted">প্রথমে ক্লাস নির্বাচন করুন</p>');
                }
            });
            
            // Handle class selection
            $('#class_id').change(function() {
                const classId = $(this).val();
                const sessionId = $('#session_id').val();
                
                if(classId) {
                    // Enable student selection type
                    $('#student_selection_type').prop('disabled', false);
                    
                    // Load students for this class
                    loadStudents(classId, sessionId);
                } else {
                    // Reset student selection
                    $('#student_selection_type').prop('disabled', false);
                    $('#student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#student_id').prop('disabled', false);
                    $('#student_list_container').html('<p class="text-muted">প্রথমে ক্লাস নির্বাচন করুন</p>');
                }
            });
            
            // Function to load students
            function loadStudents(classId, sessionId) {
                // Show loading
                $('#student_id').html('<option value="">লোড হচ্ছে...</option>');
                $('#student_list_container').html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">লোড হচ্ছে...</span></div><p class="mt-2">শিক্ষার্থী তালিকা লোড হচ্ছে...</p></div>');
                
                // AJAX call to get students
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'get_students_by_class',
                        class_id: classId,
                        session_id: sessionId
                    },
                    dataType: 'json',
                    success: function(response) {
                        console.log('Student response:', response);
                        if(response.success && response.students && response.students.length > 0) {
                            // Filter out duplicates by student ID AND database ID
                            const uniqueStudents = [];
                            const studentIds = new Set();
                            const studentDatabaseIds = new Set();
                            let duplicateCount = 0;
                            
                            response.students.forEach(student => {
                                // Check for both student_id (visible ID) and id (database primary key)
                                const uniqueKey = `${student.student_id}_${student.id}`;
                                if (!studentIds.has(student.student_id) && !studentDatabaseIds.has(student.id)) {
                                    // This is a new unique student
                                    studentIds.add(student.student_id);
                                    studentDatabaseIds.add(student.id);
                                    uniqueStudents.push(student);
                                } else {
                                    // This is a duplicate
                                    console.log(`Duplicate student found: ID=${student.student_id}, DB_ID=${student.id}, Name=${student.first_name} ${student.last_name}`);
                                    duplicateCount++;
                                }
                            });
                            
                            console.log(`Filtered out ${duplicateCount} duplicate students`);
                            
                            // Create a map to identify students with same names (but different IDs)
                            const studentMap = new Map();
                            uniqueStudents.forEach(student => {
                                const fullName = `${student.first_name} ${student.last_name}`;
                                
                                if (!studentMap.has(fullName)) {
                                    studentMap.set(fullName, []);
                                }
                                
                                studentMap.get(fullName).push(student);
                            });
                            
                            // Update single student dropdown
                            let options = '<option value="">শিক্ষার্থী নির্বাচন করুন</option>';
                            uniqueStudents.forEach(function(student) {
                                const fullName = `${student.first_name} ${student.last_name}`;
                                const displayName = studentMap.get(fullName).length > 1 
                                    ? `${student.student_id} - ${fullName} (রোল: ${student.roll_no || 'N/A'})` 
                                    : `${student.student_id} - ${fullName}`;
                                
                                options += `<option value="${student.id}">${displayName}</option>`;
                            });
                            $('#student_id').html(options);
                            $('#student_id').prop('disabled', false);
                            
                            // Update multiple students list with table
                            let studentListHtml = `
                                <div class="table-responsive">
                                    <table class="table table-hover table-sm student-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th width="50" class="text-center">
                                                    <i class="fas fa-check-square"></i>
                                                </th>
                                                <th>শিক্ষার্থী আইডি</th>
                                                <th>নাম</th>
                                                <th>রোল</th>
                                                <th>ফোন</th>
                                                <th>বিভাগ</th>
                                            </tr>
                                        </thead>
                                        <tbody>`;
                                        
                            uniqueStudents.forEach(function(student) {
                                const fullName = `${student.first_name} ${student.last_name}`;
                                const hasDuplicate = studentMap.get(fullName).length > 1;
                                
                                studentListHtml += `
                                    <tr data-id="${student.id}" data-name="${student.first_name} ${student.last_name}" 
                                        data-student-id="${student.student_id}" data-email="${student.email || ''}" data-phone="${student.phone || ''}">
                                        <td class="text-center">
                                            <div class="form-check d-flex justify-content-center">
                                                <input class="form-check-input student-checkbox" type="checkbox" 
                                                       name="selected_students[]" value="${student.id}" 
                                                       id="student_${student.id}">
                                            </div>
                                        </td>
                                        <td>${student.student_id}</td>
                                        <td>${hasDuplicate ? `<strong>${fullName}</strong>` : fullName}</td>
                                        <td>${student.roll_no || 'N/A'}</td>
                                        <td>${student.phone || 'N/A'}</td>
                                        <td>${student.department_name || 'N/A'}</td>
                                    </tr>
                                `;
                            });
                            
                            studentListHtml += `
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3 d-flex justify-content-between align-items-center">
                                    <span class="text-muted">মোট <strong>${response.students.length}</strong> জন শিক্ষার্থী</span>
                                    <span class="text-muted" id="selected_count">নির্বাচিত: <strong>0</strong> জন</span>
                                </div>
                            `;
                            
                            $('#student_list_container').html(studentListHtml);
                            
                            // Add highlighting to rows when selected
                            $('.student-checkbox').change(function() {
                                $(this).closest('tr').toggleClass('table-primary', this.checked);
                                updateSelectedCount();
                            });
                            
                            // Make entire row clickable for checkbox
                            $('.student-table tbody tr').click(function(e) {
                                if (e.target.type !== 'checkbox') {
                                    const checkbox = $(this).find('.student-checkbox');
                                    checkbox.prop('checked', !checkbox.prop('checked'));
                                    checkbox.change();
                                }
                            });
                        } else {
                            // No students found
                            $('#student_id').html('<option value="">কোন শিক্ষার্থী পাওয়া যায়নি</option>');
                            $('#student_id').prop('disabled', true);
                            $('#student_list_container').html('<div class="alert alert-info">কোন শিক্ষার্থী পাওয়া যায়নি। অন্য ক্লাস বা সেশন নির্বাচন করুন।</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        console.log("Response Text:", xhr.responseText);
                        // Error loading students
                        $('#student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                        $('#student_id').prop('disabled', true);
                        $('#student_list_container').html('<div class="alert alert-danger"><i class="fas fa-exclamation-circle me-2"></i>শিক্ষার্থী লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।</div>');
                    }
                });
            }
            
            // Function to update selected count
            function updateSelectedCount() {
                const selectedCount = $('.student-checkbox:checked').length;
                $('#selected_count').html(`নির্বাচিত: <strong>${selectedCount}</strong> জন`);
            }
            
            // Handle student selection type change
            $('#student_selection_type').change(function() {
                const selectedType = $(this).val();
                
                // Hide all sections
                $('#single_student_section, #multiple_students_section, #all_students_section').addClass('d-none');
                
                // Show the appropriate section
                if(selectedType === 'single') {
                    $('#single_student_section').removeClass('d-none');
                } else if(selectedType === 'multiple') {
                    $('#multiple_students_section').removeClass('d-none');
                } else if(selectedType === 'all') {
                    $('#all_students_section').removeClass('d-none');
                }
            });
            
            // Handle select all checkbox
            $('#select_all_students').change(function() {
                $('.student-checkbox').prop('checked', $(this).is(':checked'));
                // Highlight/unhighlight all rows
                $('.student-checkbox').closest('tr').toggleClass('table-primary', $(this).is(':checked'));
                updateSelectedCount();
            });
            
            // Auto-populate amount when fee type is selected
            $('#fee_type').change(function() {
                const selectedType = $(this).val();
                const selectedOption = $(this).find('option:selected');
                const amount = selectedOption.data('amount');
                
                console.log('Selected fee type:', selectedType);
                console.log('Selected option:', selectedOption.text());
                console.log('Amount from data attribute:', amount);
                
                // Handle monthly fee type
                if(selectedType && selectedType.toLowerCase().includes('মাসিক বেতন')) {
                    $('#monthly_fee_section').removeClass('d-none');
                    
                    // Set base amount for one month (150 Taka)
                    const baseMonthlyAmount = 150;
                    $('#amount').val(baseMonthlyAmount);
                    $('#amount').prop('readonly', true);
                    
                    // Uncheck all month checkboxes initially
                    $('.month-checkbox').prop('checked', false);
                    $('#select_all_months').prop('checked', false);
                    updateMonthlyFeeAmount();
                } else {
                    // Hide monthly fee section
                    $('#monthly_fee_section').addClass('d-none');
                    $('#amount').prop('readonly', false);
                    
                    // Set amount from data attribute if available
                    if(amount && !isNaN(parseFloat(amount))) {
                        $('#amount').val(parseFloat(amount));
                    } else {
                        $('#amount').val('');
                    }
                }
            });
            
            // Handle month selection
            $(document).on('change', '.month-checkbox', function() {
                updateMonthlyFeeAmount();
            });
            
            // Handle select all months
            $('#select_all_months').change(function() {
                $('.month-checkbox').prop('checked', $(this).is(':checked'));
                updateMonthlyFeeAmount();
            });
            
            // Update fee amount based on selected months
            function updateMonthlyFeeAmount() {
                const selectedMonths = $('.month-checkbox:checked').length;
                const baseMonthlyAmount = 150;
                const totalAmount = selectedMonths * baseMonthlyAmount;
                
                $('#month_count').text(`নির্বাচিত: ${selectedMonths} মাস`);
                $('#total_amount').text(`মোট: ৳${totalAmount}`);
                $('#amount').val(totalAmount);
                
                // Update fee_type as well to include selected months
                if(selectedMonths > 0) {
                    const months = $('.month-checkbox:checked').map(function() {
                        return $(this).val();
                    }).get().join(', ');
                    
                    // Create a fee type name with selected months
                    const feeTypeName = selectedMonths === 12 ? 
                        'মাসিক বেতন (সব মাস)' : 
                        `মাসিক বেতন (${months})`;
                    
                    // Store the custom fee type name in a hidden field
                    if($('#custom_fee_type').length === 0) {
                        $('<input>').attr({
                            type: 'hidden',
                            id: 'custom_fee_type',
                            name: 'custom_fee_type',
                            value: feeTypeName
                        }).appendTo('#fee-assign-form');
                    } else {
                        $('#custom_fee_type').val(feeTypeName);
                    }
                }
            }
            
            // Handle reset button click
            $('#resetFeeAssignBtn').click(function() {
                // Reset the form completely without any conditions
                $('#fee-assign-form')[0].reset();
                
                // Hide any student selection sections that might be visible
                $('#monthly_fee_section').addClass('d-none');
                $('#amount').prop('readonly', false);
                
                // Reset the due date to today
                const today = new Date();
                const formattedDate = today.toISOString().substr(0, 10);
                $('#due_date').val(formattedDate);
            });

            // Validate form before submitting
            $('#fee-assign-form').submit(function(e) {
                const studentSelectionType = $('#student_selection_type').val();
                
                // Validation based on student selection type
                if (studentSelectionType === 'single') {
                    if (!$('#student_id').val()) {
                        e.preventDefault();
                        alert('দয়া করে একজন শিক্ষার্থী নির্বাচন করুন।');
                        return false;
                    }
                } else if (studentSelectionType === 'multiple') {
                    if ($('.student-checkbox:checked').length === 0) {
                        e.preventDefault();
                        alert('দয়া করে কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন।');
                        return false;
                    }
                }
                
                // Ensure fee type is selected
                if (!$('#fee_type').val()) {
                    e.preventDefault();
                    alert('দয়া করে ফি টাইপ নির্বাচন করুন।');
                    return false;
                }
                
                // Ensure amount is valid
                const amount = $('#amount').val();
                if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
                    e.preventDefault();
                    alert('দয়া করে সঠিক ফি পরিমাণ উল্লেখ করুন।');
                    return false;
                }
                
                // Ensure due date is selected
                if (!$('#due_date').val()) {
                    e.preventDefault();
                    alert('দয়া করে বকেয়ার তারিখ নির্বাচন করুন।');
                    return false;
                }
                
                return true;
            });
        });
    </script>
</body>
</html>