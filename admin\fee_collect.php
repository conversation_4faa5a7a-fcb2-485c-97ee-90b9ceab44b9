<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Ensure necessary tables exist
$feesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_status ENUM('due', 'partial', 'paid') DEFAULT 'due',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feesTableQuery);

// Create fee_payments table if it doesn't exist
$paymentTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_id INT(11) NOT NULL,
    receipt_id INT(11) NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    receipt_no VARCHAR(50) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
)";
$conn->query($paymentTableQuery);

// Check if payment_date column exists in fee_payments
$checkColumnQuery = "SHOW COLUMNS FROM fee_payments LIKE 'payment_date'";
$columnResult = $conn->query($checkColumnQuery);
if ($columnResult && $columnResult->num_rows === 0) {
    // Add payment_date column if it doesn't exist
    $alterQuery = "ALTER TABLE fee_payments ADD COLUMN payment_date DATE NOT NULL AFTER amount";
    $conn->query($alterQuery);
}

// Create payment_receipts table if it doesn't exist
$receiptTableQuery = "CREATE TABLE IF NOT EXISTS payment_receipts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    total_amount DECIMAL(10,2) NOT NULL,
    receipt_no VARCHAR(50) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($receiptTableQuery);

// Handle fee payment collection
if (isset($_POST['collect_payment'])) {
    $feeId = $_POST['fee_id'];
    $amount = $_POST['payment_amount'];
    $paymentDate = $_POST['payment_date'];
    $paymentMethod = $_POST['payment_method'];
    $receiptNo = $_POST['receipt_no'] ?? '';
    $notes = $_POST['notes'] ?? '';
    
    if (empty($feeId) || empty($amount) || empty($paymentDate)) {
        $errorMessage = "সব তথ্য দিন!";
    } else {
        // First, get current fee information
        $feeQuery = "SELECT * FROM fees WHERE id = ?";
        $stmt = $conn->prepare($feeQuery);
        $stmt->bind_param("i", $feeId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $fee = $result->fetch_assoc();
            $totalPaid = $fee['paid'] + $amount;
            $totalAmount = $fee['amount'];
            
            // Calculate new payment status
            $paymentStatus = 'due';
            if ($totalPaid >= $totalAmount) {
                $paymentStatus = 'paid';
                // Ensure paid amount doesn't exceed total amount
                $totalPaid = $totalAmount;
            } elseif ($totalPaid > 0) {
                $paymentStatus = 'partial';
            }
            
            // Begin transaction
            $conn->begin_transaction();
            
            try {
                // Insert payment record
                $insertPaymentQuery = "INSERT INTO fee_payments (fee_id, amount, payment_date, payment_method, receipt_no, notes) 
                                      VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($insertPaymentQuery);
                $stmt->bind_param("idssss", $feeId, $amount, $paymentDate, $paymentMethod, $receiptNo, $notes);
                $stmt->execute();
                
                // Update fee record
                $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                $stmt = $conn->prepare($updateFeeQuery);
                $stmt->bind_param("dsi", $totalPaid, $paymentStatus, $feeId);
                $stmt->execute();
                
                // Commit the transaction
                $conn->commit();
                
                $successMessage = "পেমেন্ট সফলভাবে সংগ্রহ করা হয়েছে!";
            } catch (Exception $e) {
                // Roll back the transaction if something failed
                $conn->rollback();
                $errorMessage = "পেমেন্ট সংগ্রহ করতে সমস্যা হয়েছে: " . $e->getMessage();
            }
        } else {
            $errorMessage = "নির্বাচিত ফি খুঁজে পাওয়া যায়নি!";
        }
    }
}

// Get students with unpaid/partially paid fees
$studentsWithDuesQuery = "SELECT DISTINCT s.id, s.student_id as roll, s.first_name, s.last_name, c.class_name, d.department_name, ss.session_name
                         FROM students s
                         JOIN fees f ON s.id = f.student_id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         WHERE f.payment_status IN ('due', 'partial')
                         ORDER BY s.student_id";
$studentsWithDues = $conn->query($studentsWithDuesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি কালেকশন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="container-fluid px-4">
                    <h1 class="mt-4">ফি কালেকশন</h1>
                    <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                        <li class="breadcrumb-item"><a href="fees.php">ফি ম্যানেজমেন্ট</a></li>
                        <li class="breadcrumb-item active">ফি কালেকশন</li>
                    </ol>
                    
                    <!-- Payment Summary Dashboard -->
                    <div class="row mb-4">
                        <?php 
                        // Get payment statistics
                        $todayDate = date('Y-m-d');
                        $thisMonth = date('Y-m');
                        
                        // Get today's collections
                        $todayQuery = "SELECT SUM(amount) as today_amount FROM fee_payments WHERE DATE(payment_date) = ?";
                        $stmt = $conn->prepare($todayQuery);
                        $stmt->bind_param("s", $todayDate);
                        $stmt->execute();
                        $todayResult = $stmt->get_result();
                        $todayAmount = $todayResult->fetch_assoc()['today_amount'] ?? 0;
                        
                        // Get this month's collections
                        $monthQuery = "SELECT SUM(amount) as month_amount FROM fee_payments WHERE payment_date LIKE ?";
                        $monthParam = $thisMonth . '%';
                        $stmt = $conn->prepare($monthQuery);
                        $stmt->bind_param("s", $monthParam);
                        $stmt->execute();
                        $monthResult = $stmt->get_result();
                        $monthAmount = $monthResult->fetch_assoc()['month_amount'] ?? 0;
                        
                        // Get total due amount
                        $dueQuery = "SELECT SUM(amount) - SUM(paid) as total_due FROM fees WHERE payment_status != 'paid'";
                        $dueResult = $conn->query($dueQuery);
                        $totalDue = $dueResult->fetch_assoc()['total_due'] ?? 0;
                        
                        // Get due count
                        $dueCountQuery = "SELECT COUNT(*) as due_count FROM fees WHERE payment_status != 'paid'";
                        $dueCountResult = $conn->query($dueCountQuery);
                        $dueCount = $dueCountResult->fetch_assoc()['due_count'] ?? 0;
                        ?>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-uppercase mb-1">আজকের সংগ্রহ</h6>
                                            <h4 class="mb-0">৳ <?= number_format($todayAmount, 2) ?></h4>
                                        </div>
                                        <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <span><?= date('d F, Y') ?></span>
                                    <a href="fee_reports.php?period=today" class="text-white"><i class="fas fa-arrow-circle-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-uppercase mb-1">এই মাসের সংগ্রহ</h6>
                                            <h4 class="mb-0">৳ <?= number_format($monthAmount, 2) ?></h4>
                                        </div>
                                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <span><?= date('F Y') ?></span>
                                    <a href="fee_reports.php?period=month" class="text-white"><i class="fas fa-arrow-circle-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-uppercase mb-1">মোট বকেয়া</h6>
                                            <h4 class="mb-0">৳ <?= number_format($totalDue, 2) ?></h4>
                                        </div>
                                        <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <span>সকল বকেয়া ফি</span>
                                    <a href="fees.php?status=due" class="text-white"><i class="fas fa-arrow-circle-right"></i></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="text-uppercase mb-1">বকেয়া ফি সংখ্যা</h6>
                                            <h4 class="mb-0"><?= number_format($dueCount) ?></h4>
                                        </div>
                                        <i class="fas fa-list-alt fa-2x opacity-75"></i>
                                    </div>
                                </div>
                                <div class="card-footer d-flex align-items-center justify-content-between">
                                    <span>মোট ফি রেকর্ড</span>
                                    <a href="fee_reports.php" class="text-white"><i class="fas fa-arrow-circle-right"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success"><?= $successMessage ?></div>
                    <?php endif; ?>
                    
                    <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger"><?= $errorMessage ?></div>
                    <?php endif; ?>
                    
                    <div class="row mb-4">
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী নির্বাচন করুন</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Advanced Search -->
                                    <div class="card mb-3">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><i class="fas fa-search me-2"></i> উন্নত অনুসন্ধান</h6>
                                                <button class="btn btn-sm btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSearchCollapse">
                                                    <i class="fas fa-caret-down"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="collapse" id="advancedSearchCollapse">
                                            <div class="card-body">
                                                <div class="row g-3">
                                                    <div class="col-md-4">
                                                        <label for="studentSearch" class="form-label">শিক্ষার্থী খুঁজুন</label>
                                                        <input type="text" class="form-control" id="studentSearch" placeholder="নাম / আইডি">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="classFilter" class="form-label">ক্লাস</label>
                                                        <select class="form-select" id="classFilter">
                                                            <option value="">সব ক্লাস</option>
                                                            <?php 
                                                            $classesQuery = "SELECT DISTINCT c.id, c.class_name 
                                                                            FROM classes c 
                                                                            JOIN students s ON c.id = s.class_id 
                                                                            JOIN fees f ON s.id = f.student_id 
                                                                            WHERE f.payment_status IN ('due', 'partial')
                                                                            ORDER BY c.class_name";
                                                            $classesResult = $conn->query($classesQuery);
                                                            if ($classesResult && $classesResult->num_rows > 0) {
                                                                while ($classRow = $classesResult->fetch_assoc()) {
                                                                    echo "<option value='" . $classRow['class_name'] . "'>" . $classRow['class_name'] . "</option>";
                                                                }
                                                            }
                                                            ?>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="departmentFilter" class="form-label">বিভাগ</label>
                                                        <select class="form-select" id="departmentFilter">
                                                            <option value="">সব বিভাগ</option>
                                                            <?php 
                                                            $deptQuery = "SELECT DISTINCT d.id, d.department_name 
                                                                        FROM departments d 
                                                                        JOIN students s ON d.id = s.department_id 
                                                                        JOIN fees f ON s.id = f.student_id 
                                                                        WHERE f.payment_status IN ('due', 'partial')
                                                                        ORDER BY d.department_name";
                                                            $deptResult = $conn->query($deptQuery);
                                                            if ($deptResult && $deptResult->num_rows > 0) {
                                                                while ($deptRow = $deptResult->fetch_assoc()) {
                                                                    echo "<option value='" . $deptRow['department_name'] . "'>" . $deptRow['department_name'] . "</option>";
                                                                }
                                                            }
                                                            ?>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="dueAmountMin" class="form-label">সর্বনিম্ন বকেয়া</label>
                                                        <input type="number" class="form-control" id="dueAmountMin" min="0" placeholder="সর্বনিম্ন পরিমাণ">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="dueAmountMax" class="form-label">সর্বোচ্চ বকেয়া</label>
                                                        <input type="number" class="form-control" id="dueAmountMax" min="0" placeholder="সর্বোচ্চ পরিমাণ">
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label for="dueDateFilter" class="form-label">বকেয়া শেষ তারিখ</label>
                                                        <input type="date" class="form-control" id="dueDateFilter">
                                                    </div>
                                                    <div class="col-12 text-end">
                                                        <button type="button" class="btn btn-secondary" id="resetFilterBtn">
                                                            <i class="fas fa-redo me-1"></i> রিসেট
                                                        </button>
                                                        <button type="button" class="btn btn-primary ms-2" id="applyFilterBtn">
                                                            <i class="fas fa-filter me-1"></i> ফিল্টার প্রয়োগ করুন
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                                        <table class="table table-hover" id="studentTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>রোল নং</th>
                                                    <th>নাম</th>
                                                    <th>ক্লাস</th>
                                                    <th>বিভাগ</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if ($studentsWithDues && $studentsWithDues->num_rows > 0): ?>
                                                    <?php while ($student = $studentsWithDues->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?= $student['roll'] ?></td>
                                                        <td><?= $student['first_name'] . ' ' . $student['last_name'] ?></td>
                                                        <td><?= $student['class_name'] ?? 'N/A' ?></td>
                                                        <td><?= $student['department_name'] ?? 'N/A' ?></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-primary load-fees-btn" data-student-id="<?= $student['id'] ?>">
                                                                <i class="fas fa-arrow-right"></i> বকেয়া দেখুন
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    <?php endwhile; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="5" class="text-center">কোন শিক্ষার্থীর বকেয়া ফি পাওয়া যায়নি</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-money-bill-wave me-2"></i> বকেয়া ফি</h5>
                                </div>
                                <div class="card-body" id="feeDetails">
                                    <div class="text-center py-5">
                                        <i class="fas fa-hand-point-left fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">বাম পাশ থেকে একজন শিক্ষার্থী নির্বাচন করুন</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Payment Form (initially hidden) -->
                    <div class="row mb-4 d-none" id="paymentFormSection">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-credit-card me-2"></i> পেমেন্ট সংগ্রহ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="" id="paymentForm">
                                        <input type="hidden" id="fee_id" name="fee_id">
                                        
                                        <div class="row">
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="payment_amount" class="form-label">পরিমাণ (৳)</label>
                                                    <input type="number" class="form-control" id="payment_amount" name="payment_amount" step="0.01" required>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                                                    <input type="date" class="form-control" id="payment_date" name="payment_date" required>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="payment_method" class="form-label">পেমেন্ট মাধ্যম</label>
                                                    <select class="form-select" id="payment_method" name="payment_method" required>
                                                        <option value="cash">নগদ</option>
                                                        <option value="bkash">বিকাশ</option>
                                                        <option value="nagad">নগদ (ডিজিটাল)</option>
                                                        <option value="rocket">রকেট</option>
                                                        <option value="bank">ব্যাংক ট্রান্সফার</option>
                                                        <option value="check">চেক</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="mb-3">
                                                    <label for="receipt_no" class="form-label">রসিদ নং (যদি থাকে)</label>
                                                    <input type="text" class="form-control" id="receipt_no" name="receipt_no">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">নোট (ঐচ্ছিক)</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                        </div>
                                        
                                        <input type="hidden" name="action" value="collect_fee_payment">
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="badge bg-info p-2" id="payment_summary">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    পেমেন্ট সারসংক্ষেপ লোড হচ্ছে...
                                                </span>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-secondary me-2" id="cancelPaymentBtn">
                                                    <i class="fas fa-times me-1"></i> বাতিল করুন
                                                </button>
                                                <button type="submit" class="btn btn-success" id="submitPaymentBtn">
                                                    <i class="fas fa-check-circle me-1"></i> পেমেন্ট গ্রহণ করুন
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Receipt Section (initially hidden) -->
                    <div class="row mb-4 d-none" id="receiptSection">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="card-title mb-0"><i class="fas fa-receipt me-2"></i> পেমেন্ট রসিদ</h5>
                                        <button class="btn btn-sm btn-light" id="printReceiptBtn">
                                            <i class="fas fa-print me-1"></i> রসিদ প্রিন্ট করুন
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body" id="receiptContent">
                                    <div id="printableReceipt">
                                        <div class="text-center mb-4">
                                            <h4>কলেজ ম্যানেজমেন্ট সিস্টেম</h4>
                                            <h5>ফি পেমেন্ট রসিদ</h5>
                                            <p class="mb-0">মতিঝিল, ঢাকা</p>
                                            <p class="mb-0">ফোন: +880 12345678</p>
                                        </div>
                                        
                                        <div class="row mb-3">
                                            <div class="col-6">
                                                <p class="mb-1"><strong>রসিদ নং:</strong> <span id="receipt_receipt_no">-</span></p>
                                                <p class="mb-1"><strong>তারিখ:</strong> <span id="receipt_date">-</span></p>
                                            </div>
                                            <div class="col-6 text-end">
                                                <p class="mb-1"><strong>পেমেন্ট মাধ্যম:</strong> <span id="receipt_payment_method">-</span></p>
                                                <p class="mb-1"><strong>স্ট্যাটাস:</strong> <span class="badge bg-success" id="receipt_status">পরিশোধিত</span></p>
                                            </div>
                                        </div>
                                        
                                        <div class="row mb-3 pt-3 border-top">
                                            <div class="col-6">
                                                <p class="mb-1"><strong>শিক্ষার্থী আইডি:</strong> <span id="receipt_student_id">-</span></p>
                                                <p class="mb-1"><strong>শিক্ষার্থী নাম:</strong> <span id="receipt_student_name">-</span></p>
                                            </div>
                                            <div class="col-6">
                                                <p class="mb-1"><strong>ক্লাস:</strong> <span id="receipt_class">-</span></p>
                                                <p class="mb-1"><strong>বিভাগ:</strong> <span id="receipt_department">-</span></p>
                                            </div>
                                        </div>
                                        
                                        <div class="table-responsive mb-3 pt-3 border-top">
                                            <table class="table table-bordered">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>ফি টাইপ</th>
                                                        <th>বিবরণ</th>
                                                        <th class="text-end">পরিমাণ</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td id="receipt_fee_type">-</td>
                                                        <td id="receipt_description">-</td>
                                                        <td class="text-end">৳ <span id="receipt_amount">0.00</span></td>
                                                    </tr>
                                                </tbody>
                                                <tfoot class="table-light">
                                                    <tr>
                                                        <th colspan="2" class="text-end">মোট</th>
                                                        <th class="text-end">৳ <span id="receipt_total">0.00</span></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        
                                        <div class="row pt-3 border-top">
                                            <div class="col-12">
                                                <p class="mb-1"><strong>নোট:</strong> <span id="receipt_notes">-</span></p>
                                            </div>
                                        </div>
                                        
                                        <div class="row mt-5 pt-5">
                                            <div class="col-6 text-center">
                                                <div class="border-top pt-2">গ্রহণকারীর স্বাক্ষর</div>
                                            </div>
                                            <div class="col-6 text-center">
                                                <div class="border-top pt-2">অ্যাডমিনের স্বাক্ষর</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="text-end">
                                        <button class="btn btn-info" id="newPaymentBtn">
                                            <i class="fas fa-plus me-1"></i> নতুন পেমেন্ট করুন
                                        </button>
                                        <a href="fee_collect.php" class="btn btn-secondary ms-2">
                                            <i class="fas fa-redo me-1"></i> রিফ্রেশ করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-end mb-4">
                        <a href="fee_assign.php" class="btn btn-primary">
                            <i class="fas fa-tag me-1"></i> ফি এসাইন করুন
                        </a>
                        <a href="fee_collect_multiple.php" class="btn btn-success">
                            <i class="fas fa-money-bill-alt me-1"></i> একাধিক ফি সংগ্রহ
                        </a>
                    </div>
                    
                    <!-- Recent Payment History Section -->
                    <div class="card" id="paymentHistoryCard">
                        <div class="card-header bg-gradient-info text-white d-flex justify-content-between align-items-center">
                            <span>সাম্প্রতিক পেমেন্ট ইতিহাস</span>
                            <div>
                                <button type="button" id="refreshPaymentHistory" class="btn btn-sm btn-light">
                                    <i class="fas fa-sync-alt"></i> রিফ্রেশ
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="historyDateFrom" class="form-label">তারিখ হতে</label>
                                    <input type="date" class="form-control" id="historyDateFrom" value="<?= date('Y-m-d', strtotime('-7 days')) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="historyDateTo" class="form-label">তারিখ পর্যন্ত</label>
                                    <input type="date" class="form-control" id="historyDateTo" value="<?= date('Y-m-d') ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="historyPaymentMethod" class="form-label">পেমেন্ট মাধ্যম</label>
                                    <select class="form-select" id="historyPaymentMethod">
                                        <option value="">সকল মাধ্যম</option>
                                        <option value="cash">নগদ</option>
                                        <option value="bkash">বিকাশ</option>
                                        <option value="nagad">নগদ (ডিজিটাল)</option>
                                        <option value="rocket">রকেট</option>
                                        <option value="bank">ব্যাংক ট্রান্সফার</option>
                                        <option value="check">চেক</option>
                                    </select>
                                </div>
                                <div class="col-md-3 align-self-end">
                                    <button type="button" class="btn btn-primary w-100" id="filterPaymentHistoryBtn">
                                        <i class="fas fa-filter me-1"></i> ফিল্টার
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Payment History Table -->
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="paymentHistoryTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th>তারিখ</th>
                                            <th>রসিদ নং</th>
                                            <th>শিক্ষার্থী আইডি</th>
                                            <th>নাম</th>
                                            <th>ফি টাইপ</th>
                                            <th>পরিমাণ</th>
                                            <th>পেমেন্ট মাধ্যম</th>
                                            <th>অ্যাকশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Will be populated via AJAX -->
                                        <tr id="paymentHistoryLoading">
                                            <td colspan="8" class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                                                পেমেন্ট ইতিহাস লোড হচ্ছে...
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- No Payment History Message -->
                            <div id="noPaymentHistory" class="alert alert-info text-center d-none">
                                <i class="fas fa-info-circle me-2"></i> নির্বাচিত সময়কালে কোন পেমেন্ট ইতিহাস পাওয়া যায়নি
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Set today's date as default payment date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10);
            $('#payment_date').val(formattedDate);
            
            // Initialize date range for payment history
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            $('#historyDateFrom').val(weekAgo.toISOString().substr(0, 10));
            $('#historyDateTo').val(formattedDate);
            
            // Call the new function with cache busting directly
            loadPaymentHistoryWithCacheBusting();
            
            // Student search functionality
            $('#studentSearch').on('keyup', function() {
                const value = $(this).val().toLowerCase();
                $('#studentTable tbody tr').filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });
            
            // Load student fees
            $('.load-fees-btn').click(function() {
                const studentId = $(this).data('student-id');
                
                // Highlight selected student
                $('#studentTable tbody tr').removeClass('table-primary');
                $(this).closest('tr').addClass('table-primary');
                
                // Show loading
                $('#feeDetails').html('<div class="text-center py-5"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">ফি তথ্য লোড হচ্ছে...</p></div>');
                
                // Hide payment form section
                $('#paymentFormSection').addClass('d-none');
                
                // AJAX call to get student fees
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'get_student_fees',
                        student_id: studentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if(response.status === 'success') {
                            // Display student information
                            $('#studentInfo').html(`
                                <div class="card mb-4">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>${response.student.first_name} ${response.student.last_name}</h5>
                                    </div>
                                    <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                                <p><strong>রোল/আইডি:</strong> ${response.student.student_id || '-'}</p>
                                                <p><strong>শ্রেণি:</strong> ${response.student.class_name || '-'}</p>
                                                <p><strong>বিভাগ:</strong> ${response.student.department_name || '-'}</p>
                                        </div>
                                        <div class="col-md-6">
                                                <p><strong>সেশন:</strong> ${response.student.session_name || '-'}</p>
                                                <p><strong>ফোন:</strong> ${response.student.phone || '-'}</p>
                                                <p><strong>ইমেইল:</strong> ${response.student.email || '-'}</p>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            `);
                            
                            // Display fee information
                            if(response.fees.length > 0) {
                                let feesHtml = `
                                    <div class="table-responsive">
                                        <table class="table table-bordered" id="feesTable">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>ফি প্রকার</th>
                                                    <th>মোট অঙ্ক</th>
                                                    <th>পরিশোধিত</th>
                                                    <th>বাকি</th>
                                                    <th>স্ট্যাটাস</th>
                                                    <th>দেয় তারিখ</th>
                                                    <th width="150">অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                `;
                                
                                response.fees.forEach(function(fee) {
                                    const due = fee.amount - fee.paid;
                                    let statusClass = '';
                                    let statusText = '';
                                    
                                    switch(fee.payment_status) {
                                        case 'paid':
                                            statusClass = 'success';
                                            statusText = 'পরিশোধিত';
                                            break;
                                        case 'partial':
                                            statusClass = 'warning';
                                            statusText = 'আংশিক';
                                            break;
                                        default:
                                            statusClass = 'danger';
                                            statusText = 'বকেয়া';
                                            break;
                                    }
                                    
                                    feesHtml += `
                                            <tr>
                                                <td>${fee.fee_type}</td>
                                            <td>৳${fee.amount}</td>
                                            <td>৳${fee.paid}</td>
                                                <td>৳${due.toFixed(2)}</td>
                                            <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                                                <td>${fee.due_date}</td>
                                                <td>
                                                <button class="btn btn-sm btn-primary pay-fee-btn" data-fee-id="${fee.id}" data-fee-type="${fee.fee_type}" data-amount="${fee.amount}" data-paid="${fee.paid}" data-due="${due.toFixed(2)}" ${fee.payment_status === 'paid' ? 'disabled' : ''}>
                                                    <i class="fas fa-money-bill-wave"></i> পেমেন্ট
                                                </button>
                                                <button class="btn btn-sm btn-danger delete-fee-btn" data-id="${fee.id}" title="মুছে ফেলুন">
                                                    <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        `;
                                });
                                
                                feesHtml += `
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-dark">
                                                    <th colspan="3">মোট বকেয়া</th>
                                                    <th colspan="4">৳${response.total_due.toFixed(2)}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                `;
                                
                                $('#feeDetails').html(feesHtml);
                            } else {
                                $('#feeDetails').html(`
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>এই শিক্ষার্থীর জন্য কোন ফি রেকর্ড নেই।
                                    </div>
                                `);
                            }
                        } else {
                            // Show error message
                            $('#feeDetails').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-triangle me-2"></i>${response.message}
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#feeDetails').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                ফি তথ্য লোড করতে সমস্যা হয়েছে। সার্ভারে সমস্যা।
                                <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="window.location.reload();">আবার চেষ্টা করুন</button>
                            </div>
                        `);
                        console.error("AJAX Error:", xhr, status, error);
                    }
                });
            });
            
            // Collect payment button click
            $(document).on('click', '.pay-fee-btn', function() {
                const feeId = $(this).data('fee-id');
                const feeType = $(this).data('fee-type');
                const totalAmount = parseFloat($(this).data('amount'));
                const paidAmount = parseFloat($(this).data('paid'));
                const dueAmount = parseFloat($(this).data('due'));
                
                // Fill payment form
                $('#fee_id').val(feeId);
                $('#payment_amount').val(dueAmount.toFixed(2));
                $('#payment_amount').attr('max', dueAmount.toFixed(2));
                
                // Generate unique receipt number
                generateReceiptNumber();
                
                // Show payment summary
                $('#payment_summary').html(`
                    <i class="fas fa-info-circle me-1"></i>
                    ফি: ${feeType} | মোট: ৳${totalAmount.toFixed(2)} | পরিশোধিত: ৳${paidAmount.toFixed(2)} | বাকি: ৳${dueAmount.toFixed(2)}
                `);
                
                // Show payment form section
                $('#paymentFormSection').removeClass('d-none');
                
                // Scroll to payment form
                $('html, body').animate({
                    scrollTop: $("#paymentFormSection").offset().top - 20
                }, 500);
            });
            
            // Cancel payment button
            $('#cancelPaymentBtn').click(function() {
                $('#paymentFormSection').addClass('d-none');
            });
            
            // Validate payment amount
            $('#payment_amount').on('input', function() {
                const max = parseFloat($(this).attr('max'));
                const val = parseFloat($(this).val());
                
                if (val > max) {
                    $(this).val(max.toFixed(2));
                }
            });
            
            // ENHANCED: Handle the payment form submission
            $('#paymentForm').on('submit', function(e) {
                e.preventDefault();
                
                // Validate form fields
                const feeId = $('#fee_id').val();
                const amount = $('#payment_amount').val();
                const paymentDate = $('#payment_date').val();
                const paymentMethod = $('#payment_method').val();
                
                if (!feeId || !amount || !paymentDate || !paymentMethod) {
                    alert('অনুগ্রহ করে সকল প্রয়োজনীয় ফিল্ড পূরণ করুন!');
                    return;
                }
                
                // Make sure amount is a valid number greater than zero
                if (isNaN(amount) || parseFloat(amount) <= 0) {
                    alert('সঠিক পরিমাণ প্রদান করুন!');
                    return;
                }
                
                // Show loading
                $('#submitPaymentBtn').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> প্রক্রিয়াকরণ হচ্ছে...');
                $('#submitPaymentBtn').prop('disabled', true);
                
                // Get form data
                const formData = {
                    action: 'collect_fee_payment',
                    fee_id: feeId,
                    payment_amount: amount,
                    payment_date: paymentDate,
                    payment_method: paymentMethod,
                    receipt_no: $('#receipt_no').val(),
                    notes: $('#notes').val()
                };
                
                // Debug data
                console.log('Sending payment data:', formData);
                
                // Submit the form via AJAX
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        if(response.status === 'success') {
                            // Show success message
                            $('#paymentFormSection').addClass('d-none');
                            $('#receiptSection').removeClass('d-none');
                            
                            // Populate receipt with payment details
                            // Debug information
                            console.log('Receipt data:', response.data);
                            
                            // Update all receipt related fields
                            $('#receipt_student_name').text(response.data.student_name);
                            $('#receipt_student_id').text(response.data.student_id);
                            $('#receipt_class').text(response.data.class_name);
                            $('#receipt_fee_type').text(response.data.fee_type);
                            $('#receipt_description').text(response.data.fee_type + ' পেমেন্ট');
                            $('#receipt_amount').text(parseFloat(response.data.amount).toFixed(2));
                            $('#receipt_total').text(parseFloat(response.data.amount).toFixed(2));
                            $('#receipt_date').text(response.data.payment_date);
                            $('#receipt_payment_method').text(getPaymentMethodLabel(response.data.payment_method));
                            $('#receipt_receipt_no').text(response.data.receipt_no);
                            $('#receipt_notes').text(response.data.notes || '-');
                            $('#receipt_status').text(response.data.payment_status === 'paid' ? 'পরিশোধিত' : 'আংশিক পরিশোধিত');
                            
                            // Refresh student table to show updated fee status
                            refreshStudentTable();
                            
                            // Also refresh payment history
                            loadPaymentHistory();
                        } else {
                            // Show error in a modal or alert
                            const errorMsg = response.message || 'পেমেন্ট প্রক্রিয়াকরণে সমস্যা হয়েছে।';
                            
                            // Create alert with error details and retry button
                            const alertHtml = `
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i> ${errorMsg}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>`;
                            
                            // Show error message at the top of payment form
                            $('#paymentForm').prepend(alertHtml);
                            
                            // Scroll to error message
                            $('html, body').animate({
                                scrollTop: $("#paymentForm").offset().top - 20
                            }, 500);
                        }
                        
                        // Reset button
                        $('#submitPaymentBtn').html('<i class="fas fa-check-circle me-1"></i> পেমেন্ট সম্পন্ন করুন');
                        $('#submitPaymentBtn').prop('disabled', false);
                    },
                    error: function(xhr, status, error) {
                        // Log error details for debugging
                        console.error("AJAX Error:", status, error, xhr.responseText);
                        
                        try {
                            // Try to parse the response to get error message
                            let errorMessage = 'পেমেন্ট প্রক্রিয়াকরণে সমস্যা! সার্ভারে সমস্যা।';
                            if (xhr.responseText) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    if (response.message) {
                                        errorMessage = response.message;
                                    }
                                } catch(e) {
                                    console.log("Error parsing JSON response:", e);
                                }
                            }
                            
                            // Create alert with error details
                            const alertHtml = `
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i> ${errorMessage}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>`;
                            
                            // Show error message at the top of payment form
                            $('#paymentForm').prepend(alertHtml);
                            
                        } catch (e) {
                        alert('পেমেন্ট প্রক্রিয়াকরণে সমস্যা! সার্ভারে সমস্যা।');
                        }
                        
                        // Reset button
                        $('#submitPaymentBtn').html('<i class="fas fa-check-circle me-1"></i> পেমেন্ট সম্পন্ন করুন');
                        $('#submitPaymentBtn').prop('disabled', false);
                    }
                });
            });
            
            // Print receipt functionality
            $('#printReceiptBtn').click(function() {
                // Get values from the receipt display
                const receiptNo = $('#receipt_receipt_no').text() || '-';
                const receiptDate = $('#receipt_date').text() || '-';
                const studentName = $('#receipt_student_name').text() || '-';
                const studentId = $('#receipt_student_id').text() || '-';
                const className = $('#receipt_class').text() || '-';
                const feeType = $('#receipt_fee_type').text() || '-';
                const amount = $('#receipt_amount').text() || '-';
                const paymentMethod = $('#receipt_payment_method').text() || '-';
                const notes = $('#receipt_notes').text() || '-';
                
                console.log('Printing receipt:', {
                    receiptNo,
                    receiptDate,
                    studentName,
                    studentId,
                    className,
                    feeType,
                    amount,
                    paymentMethod,
                    notes
                });
                
                const printWindow = window.open('', '_blank', 'width=800,height=600');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html lang="bn">
                    <head>
                        <meta charset="UTF-8">
                        <title>Payment Receipt</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: Arial, sans-serif; }
                            .receipt-header { text-align: center; margin-bottom: 20px; }
                            .receipt-title { font-size: 24px; font-weight: bold; margin-top: 10px; }
                            .receipt-subtitle { font-size: 18px; margin-top: 5px; }
                            .receipt-body { margin: 20px 0; }
                            .receipt-table { width: 100%; border-collapse: collapse; }
                            .receipt-table th, .receipt-table td { padding: 8px; border-bottom: 1px solid #ddd; }
                            .receipt-footer { margin-top: 50px; display: flex; justify-content: space-between; }
                            .signature-line { border-top: 1px solid #000; width: 150px; text-align: center; padding-top: 5px; }
                            @media print {
                                .no-print { display: none; }
                                body { margin: 0; padding: 15px; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container my-4">
                            <div class="row">
                                <div class="col-12 receipt-header">
                                    <h1 class="receipt-title">কলেজ ম্যানেজমেন্ট সিস্টেম</h1>
                                    <p class="receipt-subtitle">পেমেন্ট রশিদ</p>
                                    <p><strong>রশিদ নং:</strong> ${receiptNo}</p>
                                    <p><strong>তারিখ:</strong> ${receiptDate}</p>
                                </div>
                            </div>
                            
                            <div class="row receipt-body">
                                <div class="col-12">
                                    <table class="receipt-table">
                                        <tr>
                                            <th width="40%">শিক্ষার্থীর নাম:</th>
                                            <td>${studentName}</td>
                                        </tr>
                                        <tr>
                                            <th>শিক্ষার্থী আইডি:</th>
                                            <td>${studentId}</td>
                                        </tr>
                                        <tr>
                                            <th>ক্লাস:</th>
                                            <td>${className}</td>
                                        </tr>
                                        <tr>
                                            <th>ফি-এর ধরন:</th>
                                            <td>${feeType}</td>
                                        </tr>
                                        <tr>
                                            <th>পরিমাণ:</th>
                                            <td><strong>৳ ${amount}</strong></td>
                                        </tr>
                                        <tr>
                                            <th>পেমেন্ট পদ্ধতি:</th>
                                            <td>${paymentMethod}</td>
                                        </tr>
                                        <tr>
                                            <th>মন্তব্য:</th>
                                            <td>${notes}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <div class="row receipt-footer">
                                <div class="col-4">
                                    <div class="signature-line">শিক্ষার্থীর স্বাক্ষর</div>
                                </div>
                                <div class="col-4 text-center">
                                    <img src="../assets/img/paid-stamp.png" alt="Paid" width="100">
                                </div>
                                <div class="col-4 text-end">
                                    <div class="signature-line">অ্যাকাউন্টস অফিসারের স্বাক্ষর</div>
                                </div>
                            </div>
                            
                            <div class="row mt-5">
                                <div class="col-12 text-center">
                                    <button class="btn btn-primary no-print" onclick="window.print()">প্রিন্ট করুন</button>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                `);
                printWindow.document.close();
            });
            
            // Handle new payment button
            $('#newPaymentBtn').click(function() {
                $('#receiptSection').addClass('d-none');
                // Reset form fields if needed
                $('#payment_amount').val('');
                $('#receipt_no').val('');
                $('#notes').val('');
            });
            
            // Advanced student search functionality
            function filterStudents() {
                const search = $('#studentSearch').val().toLowerCase();
                const classFilter = $('#classFilter').val().toLowerCase();
                const deptFilter = $('#departmentFilter').val().toLowerCase();
                const minDue = parseFloat($('#dueAmountMin').val()) || 0;
                const maxDue = parseFloat($('#dueAmountMax').val()) || Infinity;
                const dueDate = $('#dueDateFilter').val();
                
                $('#studentTable tbody tr').each(function() {
                    const $row = $(this);
                    const rowData = $row.text().toLowerCase();
                    const classText = $row.find('td:eq(2)').text().toLowerCase();
                    const deptText = $row.find('td:eq(3)').text().toLowerCase();
                    
                    // Skip placeholder rows
                    if ($row.find('td[colspan]').length > 0) {
                        return true;
                    }
                    
                    let show = true;
                    
                    // Check text search
                    if (search && rowData.indexOf(search) === -1) {
                        show = false;
                    }
                    
                    // Check class filter
                    if (classFilter && classText.indexOf(classFilter) === -1) {
                        show = false;
                    }
                    
                    // Check department filter
                    if (deptFilter && deptText.indexOf(deptFilter) === -1) {
                        show = false;
                    }
                    
                    // We can't filter by amount until we select a student and see their dues
                    // Due date would require additional AJAX calls to check
                    
                    $row.toggle(show);
                });
                
                // Check if any rows are visible
                const visibleRows = $('#studentTable tbody tr:visible').length;
                if (visibleRows === 0) {
                    // If no rows match filters, show a message
                    if ($('#studentTable tbody tr.no-results').length === 0) {
                        $('#studentTable tbody').append('<tr class="no-results"><td colspan="7" class="text-center">কোন শিক্ষার্থী পাওয়া যায়নি</td></tr>');
                    } else {
                        $('#studentTable tbody tr.no-results').show();
                    }
                } else {
                    // Hide the no results message if it exists
                    $('#studentTable tbody tr.no-results').hide();
                }
            }
            
            // Handle filter button click
            $('#applyFilterBtn').click(filterStudents);
            
            // Handle reset filter button click
            $('#resetFilterBtn').click(function() {
                $('#studentSearch').val('');
                $('#classFilter').val('');
                $('#departmentFilter').val('');
                $('#dueAmountMin').val('');
                $('#dueAmountMax').val('');
                $('#dueDateFilter').val('');
                filterStudents();
            });
            
            // Real-time search with delay
            let searchTimeout;
            $('#studentSearch').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(filterStudents, 300);
            });
            
            // Change events for dropdown filters
            $('#classFilter, #departmentFilter').on('change', filterStudents);
            
            // Load payment history
            function loadPaymentHistory() {
                loadPaymentHistoryWithCacheBusting();
            }
            
            // Function to load payment history with cache busting
            function loadPaymentHistoryWithCacheBusting() {
                const dateFrom = $('#historyDateFrom').val();
                const dateTo = $('#historyDateTo').val();
                const paymentMethod = $('#historyPaymentMethod').val();
                
                // Show loading
                $('#paymentHistoryTable tbody').html(`
                    <tr id="paymentHistoryLoading">
                        <td colspan="8" class="text-center py-3">
                            <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                            পেমেন্ট ইতিহাস লোড হচ্ছে...
                        </td>
                    </tr>
                `);
                $('#noPaymentHistory').addClass('d-none');
                
                // Add cache busting parameter
                const cacheBuster = new Date().getTime();
                
                // AJAX call to get payment history
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'get_recent_payments',
                        date_from: dateFrom,
                        date_to: dateTo,
                        payment_method: paymentMethod,
                        cache_buster: cacheBuster  // Add cache busting parameter
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.status === 'success') {
                            // Reset card header to normal
                            $('#paymentHistoryCard .card-header').removeClass('bg-danger').addClass('bg-gradient-info');
                        
                            const payments = response.payments || [];
                        
                            if (payments.length > 0) {
                                // Populate table with payment records
                            let html = '';
                                payments.forEach(function(payment) {
                                html += `
                                    <tr>
                                            <td>${payment.payment_date || '-'}</td>
                                        <td>${payment.receipt_no || '-'}</td>
                                        <td>${payment.student_roll || '-'}</td>
                                            <td class="student-name">${payment.student_name || 'N/A'}</td>
                                        <td>${payment.fee_type || '-'}</td>
                                            <td>৳${parseFloat(payment.amount).toFixed(2)}</td>
                                            <td>${getPaymentMethodLabel(payment.payment_method) || '-'}</td>
                                        <td>
                                                <button type="button" class="btn btn-sm btn-outline-info view-receipt-btn" data-id="${payment.id}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-payment-btn" data-id="${payment.id}" 
                                                    data-amount="${payment.amount}" data-date="${payment.payment_date_raw || ''}" 
                                                    data-method="${payment.payment_method}" data-receipt="${payment.receipt_no}" 
                                                    data-fee-id="${payment.fee_id}" data-fee-type="${payment.fee_type}">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-payment-btn" data-id="${payment.id}">
                                                    <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `;
                            });
                                
                            $('#paymentHistoryTable tbody').html(html);
                        } else {
                                // Show no records message
                                $('#paymentHistoryLoading').remove();
                            $('#noPaymentHistory').removeClass('d-none');
                            }
                        } else {
                            // Show error message in table
                            $('#paymentHistoryTable tbody').html(`
                                <tr>
                                    <td colspan="8" class="text-center text-danger py-4">
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            ${response.message || 'পেমেন্ট ইতিহাস লোড করতে সমস্যা হয়েছে।'}
                                        </div>
                                        <button type="button" class="btn btn-danger" onclick="loadPaymentHistoryWithCacheBusting();">
                                            <i class="fas fa-sync-alt me-2"></i> আবার চেষ্টা করুন
                                        </button>
                                    </td>
                                </tr>
                            `);
                            
                            // Also indicate error in card header
                            $('#paymentHistoryCard .card-header').removeClass('bg-gradient-info').addClass('bg-danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", xhr, status, error);
                        
                        // Show error message
                        $('#paymentHistoryTable tbody').html(`
                            <tr>
                                <td colspan="8" class="text-center text-danger py-4">
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        পেমেন্ট ইতিহাস লোড করতে সমস্যা হয়েছে।
                                    </div>
                                    <button type="button" class="btn btn-danger" onclick="loadPaymentHistoryWithCacheBusting();">
                                        <i class="fas fa-sync-alt me-2"></i> আবার চেষ্টা করুন
                                    </button>
                                </td>
                            </tr>
                        `);
                        
                        // Also indicate error in card header
                        $('#paymentHistoryCard .card-header').removeClass('bg-gradient-info').addClass('bg-danger');
                    }
                });
            }
            
            // Format date helper function
            function formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('bn-BD', { year: 'numeric', month: 'long', day: 'numeric' });
            }
            
            // Generate unique receipt number
            function generateReceiptNumber() {
                const today = new Date();
                const dateStr = today.getFullYear() + 
                               ('0' + (today.getMonth() + 1)).slice(-2) + 
                               ('0' + today.getDate()).slice(-2);
                const randomNum = Math.floor(1000 + Math.random() * 9000); // 4-digit random number
                const receiptNo = `REC-${dateStr}-${randomNum}`;
                $('#receipt_no').val(receiptNo);
            }
            
            // Function to translate payment method values to Bengali labels
            function getPaymentMethodLabel(method) {
                if (!method) return '-';
                
                switch(method.toLowerCase()) {
                    case 'cash': 
                        return 'নগদ';
                    case 'check':
                    case 'cheque':
                        return 'চেক';
                    case 'bank':
                    case 'bank_transfer':
                        return 'ব্যাংক ট্রান্সফার';
                    case 'online':
                        return 'অনলাইন';
                    case 'card':
                        return 'কার্ড';
                    case 'bkash':
                        return 'বিকাশ';
                    case 'nagad':
                        return 'নগদ (ডিজিটাল)';
                    case 'rocket':
                        return 'রকেট';
                    case 'mobile_banking':
                        return 'মোবাইল ব্যাংকিং';
                    default:
                        return method;
                }
            }
            
            // Refresh student table after payment
            function refreshStudentTable() {
                // Reload the student list with updated payment status
                $.ajax({
                    url: 'fee_collect.php',
                    type: 'GET',
                    success: function(data) {
                        const newStudentTable = $(data).find('#studentTable tbody').html();
                        $('#studentTable tbody').html(newStudentTable);
                        
                        // Re-attach event handlers to new elements
                        $('.load-fees-btn').click(function() {
                            const studentId = $(this).data('student-id');
                            // ... existing student load functionality
                            // This is a shortcut - in production, you would copy the entire handler
                            
                            // Just call the existing code
                            $(this).trigger('click');
                        });
                    },
                    error: function() {
                        console.error('Failed to refresh student table');
                    }
                });
            }
            
            // Filter payment history button
            $('#filterPaymentHistoryBtn').click(function() {
                loadPaymentHistory();
            });
            
            // Refresh payment history button
            $('#refreshPaymentHistory').click(function() {
                // Reset date filters to default (last 7 days)
                const today = new Date();
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                
                $('#historyDateFrom').val(weekAgo.toISOString().substr(0, 10));
                $('#historyDateTo').val(today.toISOString().substr(0, 10));
                $('#historyPaymentMethod').val('');
                
                // Reload payment history with cache busting
                loadPaymentHistoryWithCacheBusting();
            });
            
            // Load payment history on page load
            loadPaymentHistory();
            
            // Handle view receipt button clicks (delegated event for dynamically created buttons)
            $(document).on('click', '.view-receipt-btn', function() {
                const paymentId = $(this).data('id');
                const receiptRow = $(this).closest('tr');
                const studentName = receiptRow.find('.student-name').text();
                const paymentDate = receiptRow.find('td:first').text();
                const receiptNo = receiptRow.find('td:eq(1)').text();
                const feeType = receiptRow.find('td:eq(4)').text();
                const amount = receiptRow.find('td:eq(5)').text();
                const paymentMethod = receiptRow.find('td:eq(6)').text();
                
                // Log for debugging
                console.log('Opening receipt modal for payment ID:', paymentId);
                
                // Populate receipt modal with data
                $('#receiptModal .receipt-number').text(receiptNo);
                $('#receiptModal .receipt-date').text(paymentDate);
                $('#receiptModal .student-name').text(studentName);
                $('#receiptModal .fee-type').text(feeType);
                $('#receiptModal .payment-amount').text(amount);
                $('#receiptModal .payment-method').text(paymentMethod);
                
                // Show the modal
                $('#receiptModal').modal('show');
            });

            // Handle edit payment button clicks
            $(document).on('click', '.edit-payment-btn', function() {
                const paymentId = $(this).data('id');
                const feeId = $(this).data('fee-id');
                const feeType = $(this).data('fee-type');
                const amount = $(this).data('amount');
                const paymentDate = $(this).data('date');
                const paymentMethod = $(this).data('method');
                const receiptNo = $(this).data('receipt');
                
                // Log for debugging
                console.log('Opening edit modal for payment ID:', paymentId, 'with data:', {
                    feeId, feeType, amount, paymentDate, paymentMethod, receiptNo
                });
                
                // Populate edit modal with data
                $('#edit_payment_id').val(paymentId);
                $('#edit_fee_id').val(feeId);
                $('#edit_fee_type').val(feeType);
                $('#edit_payment_amount').val(amount);
                $('#edit_payment_date').val(paymentDate);
                $('#edit_payment_method').val(paymentMethod);
                $('#edit_receipt_no').val(receiptNo);
                
                // Show the modal
                $('#editPaymentModal').modal('show');
            });
            
            // Handle delete payment button clicks
            $(document).on('click', '.delete-payment-btn', function() {
                const paymentId = $(this).data('id');
                
                // Set the payment ID in the delete confirmation modal
                $('#delete_payment_id').val(paymentId);
                
                // Show the delete confirmation modal
                $('#deletePaymentModal').modal('show');
            });
            
            // Handle save edit payment button click
            $('#saveEditPaymentBtn').click(function() {
                // Get form data
                const paymentId = $('#edit_payment_id').val();
                const feeId = $('#edit_fee_id').val();
                const amount = $('#edit_payment_amount').val();
                const paymentDate = $('#edit_payment_date').val();
                const paymentMethod = $('#edit_payment_method').val();
                const receiptNo = $('#edit_receipt_no').val();
                
                // Validate inputs
                if (!amount || amount <= 0 || !paymentDate || !paymentMethod) {
                    alert('অনুগ্রহ করে সকল প্রয়োজনীয় ফিল্ড পূরণ করুন!');
                    return;
                }
                
                // Disable the save button and show loading
                const saveBtn = $(this);
                saveBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> প্রক্রিয়াকরণ হচ্ছে...');
                saveBtn.prop('disabled', true);
                
                // Send AJAX request to update the payment
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'update_payment',
                        payment_id: paymentId,
                        fee_id: feeId,
                        payment_amount: amount,
                        payment_date: paymentDate,
                        payment_method: paymentMethod,
                        receipt_no: receiptNo
                    },
                    dataType: 'json',
                    success: function(response) {
                        // Enable the save button
                        saveBtn.html('সংরক্ষণ করুন');
                        saveBtn.prop('disabled', false);
                        
                        if (response.status === 'success') {
                            // Close the modal
                            $('#editPaymentModal').modal('hide');
                            
                            // Show success message
                            alert('পেমেন্ট সফলভাবে আপডেট করা হয়েছে!');
                            
                            // Refresh payment history
                            loadPaymentHistoryWithCacheBusting();
                        } else {
                            // Show error message
                            alert(response.message || 'পেমেন্ট আপডেট করতে সমস্যা হয়েছে!');
                        }
                    },
                    error: function(xhr, status, error) {
                        // Enable the save button
                        saveBtn.html('সংরক্ষণ করুন');
                        saveBtn.prop('disabled', false);
                        
                        // Show error message
                        alert('পেমেন্ট আপডেট করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        console.error('AJAX Error:', xhr, status, error);
                    }
                });
            });
            
            // Handle confirm delete payment button click
            $('#confirmDeletePaymentBtn').click(function() {
                const paymentId = $('#delete_payment_id').val();
                
                // Disable the delete button and show loading
                const deleteBtn = $(this);
                deleteBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> প্রক্রিয়াকরণ হচ্ছে...');
                deleteBtn.prop('disabled', true);
                
                // Send AJAX request to delete the payment
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'delete_payment',
                        payment_id: paymentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        // Enable the delete button
                        deleteBtn.html('মুছে ফেলুন');
                        deleteBtn.prop('disabled', false);
                        
                        if (response.status === 'success') {
                            // Close the modal
                            $('#deletePaymentModal').modal('hide');
                            
                            // Show success message
                            alert('পেমেন্ট সফলভাবে মুছে ফেলা হয়েছে!');
                            
                            // Refresh payment history
                            loadPaymentHistoryWithCacheBusting();
                        } else {
                            // Show error message
                            alert(response.message || 'পেমেন্ট মুছে ফেলতে সমস্যা হয়েছে!');
                        }
                    },
                    error: function(xhr, status, error) {
                        // Enable the delete button
                        deleteBtn.html('মুছে ফেলুন');
                        deleteBtn.prop('disabled', false);
                        
                        // Show error message
                        alert('পেমেন্ট মুছে ফেলতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        console.error('AJAX Error:', xhr, status, error);
                    }
                });
            });
            
            // Handle print receipt button click
            $('.print-receipt-btn').click(function() {
                const receiptContent = $('#receiptModal .modal-body').html();
                const originalContent = $('body').html();
                
                // Create a new window with just the receipt content
                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>পেমেন্ট রসিদ</title>
                        <link href="../assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: Arial, sans-serif; }
                            .print-area { max-width: 800px; margin: 0 auto; padding: 20px; }
                            @media print {
                                .no-print { display: none; }
                                button { display: none; }
                                a { text-decoration: none; color: black; }
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container mt-4">
                            <div class="no-print mb-4 text-center">
                                <button class="btn btn-primary" onclick="window.print();">
                                    <i class="fas fa-print"></i> প্রিন্ট করুন
                                </button>
                                <button class="btn btn-secondary" onclick="window.close();">
                                    বন্ধ করুন
                                </button>
                            </div>
                            <div class="print-area">
                                ${receiptContent}
                            </div>
                        </div>
                    </body>
                    </html>
                `);
                printWindow.document.close();
            });
            
            // Add a direct event handler for the delete button that appears in the payment table
            $(document).on('click', '.delete-payment-btn', function() {
                const paymentId = $(this).data('id');
                
                // Set the payment ID in the delete confirmation modal
                $('#delete_payment_id').val(paymentId);
                
                // Show the delete confirmation modal
                $('#deletePaymentModal').modal('show');
            });
            
            // Add a direct deletion function for fee IDs (from the main fee table)
            function deleteFeeRecord(feeId) {
                if (!feeId) return;
                
                if (!confirm('আপনি কি নিশ্চিত যে আপনি এই ফি রেকর্ডটি মুছে ফেলতে চান? এই পদক্ষেপটি পূর্বাবস্থায় ফেরানো যাবে না।')) {
                    return;
                }
                
                // Show loading
                const loadingHtml = `<div class="spinner-border spinner-border-sm text-danger" role="status"></div> মুছে ফেলা হচ্ছে...`;
                
                // Send AJAX request to delete the fee
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'delete_fee',
                        fee_id: feeId
                    },
                    dataType: 'json',
                    beforeSend: function() {
                        $('button[data-id="' + feeId + '"]').closest('td').html(loadingHtml);
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            // Remove the row from the table
                            $('button[data-id="' + feeId + '"]').closest('tr').fadeOut(300, function() {
                                $(this).remove();
                            });
                            
                            // Show success message
                            alert('ফি রেকর্ড সফলভাবে মুছে ফেলা হয়েছে!');
                        } else {
                            // Show error message
                            alert(response.message || 'ফি রেকর্ড মুছে ফেলতে সমস্যা হয়েছে!');
                        }
                    },
                    error: function(xhr, status, error) {
                        // Show error message
                        alert('ফি রেকর্ড মুছে ফেলতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        console.error('AJAX Error:', xhr, status, error);
                    }
                });
            }

            // Handle direct delete link in the table
            $(document).on('click', '.delete-fee-btn', function(e) {
                e.preventDefault();
                const feeId = $(this).data('id');
                deleteFeeRecord(feeId);
            });

            // Add reset buttons in the payment form section
            $('#paymentFormSection .card-header').append(`
                <div class="float-end">
                    <button type="button" id="resetPaymentFormBtn" class="btn btn-warning btn-sm ms-2">
                        <i class="fas fa-redo-alt me-1"></i> রিসেট করুন
                    </button>
                </div>
            `);

            // Add reset functionality for the payment form
            $(document).on('click', '#resetPaymentFormBtn', function() {
                // Reset the payment form fields
                $('#paymentForm')[0].reset();
                // Set today's date as default payment date
                const today = new Date();
                const formattedDate = today.toISOString().substr(0, 10);
                $('#payment_date').val(formattedDate);
                // Generate new receipt number
                generateReceiptNumber();
                // Remove any error messages
                $('#paymentForm .alert').remove();
            });

            // Add a reset button for fee assignment section if it exists
            if ($('#feeAssignmentForm').length) {
                $('#feeAssignmentForm').closest('.card').find('.card-header').append(`
                    <div class="float-end">
                        <button type="button" id="resetFeeAssignmentBtn" class="btn btn-warning btn-sm">
                            <i class="fas fa-redo-alt me-1"></i> রিসেট করুন
                        </button>
                    </div>
                `);

                // Add reset functionality for fee assignment form
                $(document).on('click', '#resetFeeAssignmentBtn', function() {
                    // Reset the fee assignment form fields
                    $('#feeAssignmentForm')[0].reset();
                    // Remove any error messages
                    $('#feeAssignmentForm .alert').remove();
                });
            }
        });
    </script>

    <!-- Receipt Modal -->
    <div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary text-white">
                    <h5 class="modal-title" id="receiptModalLabel">পেমেন্ট রসিদ</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="card">
                            <div class="card-body p-4">
                                <div class="row mb-4">
                                    <div class="col-12 text-center mb-4">
                                        <h3 class="mb-0">পেমেন্ট রসিদ</h3>
                                        <p class="text-muted">জেকাত ফাউন্ডেশন একাডেমিক উইং</p>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>রসিদ নং:</strong> <span class="receipt-number"></span>
                                    </div>
                                    <div class="col-md-6 text-md-end">
                                        <strong>তারিখ:</strong> <span class="receipt-date"></span>
                                    </div>
                                </div>
                                
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <strong>শিক্ষার্থীর নাম:</strong> <span class="student-name"></span>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>ফি প্রকার:</strong> <span class="fee-type"></span>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>বিবরণ</th>
                                                <th width="25%">পরিমাণ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><span class="fee-type"></span></td>
                                                <td><span class="payment-amount"></span></td>
                                            </tr>
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th>মোট</th>
                                                <th><span class="payment-amount"></span></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                
                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <strong>পেমেন্ট পদ্ধতি:</strong> <span class="payment-method"></span>
                                    </div>
                                </div>
                                
                                <div class="row mt-5">
                                    <div class="col-md-6 text-center">
                                        <div class="border-top border-dark pt-2">গ্রহণকারীর স্বাক্ষর</div>
                                    </div>
                                    <div class="col-md-6 text-center">
                                        <div class="border-top border-dark pt-2">অফিসিয়াল সীল</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                    <button type="button" class="btn btn-primary print-receipt-btn">
                        <i class="fas fa-print me-1"></i> প্রিন্ট করুন
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Payment Modal -->
    <div class="modal fade" id="editPaymentModal" tabindex="-1" aria-labelledby="editPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editPaymentModalLabel">পেমেন্ট সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editPaymentForm">
                        <input type="hidden" id="edit_payment_id" name="payment_id">
                        <input type="hidden" id="edit_fee_id" name="fee_id">
                        
                        <div class="mb-3">
                            <label for="edit_fee_type" class="form-label">ফি প্রকার</label>
                            <input type="text" class="form-control" id="edit_fee_type" disabled>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_payment_amount" class="form-label">পরিমাণ (৳)</label>
                            <input type="number" class="form-control" id="edit_payment_amount" name="payment_amount" step="0.01" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_payment_date" class="form-label">পেমেন্ট তারিখ</label>
                            <input type="date" class="form-control" id="edit_payment_date" name="payment_date" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_payment_method" class="form-label">পেমেন্ট মাধ্যম</label>
                            <select class="form-select" id="edit_payment_method" name="payment_method" required>
                                <option value="cash">নগদ</option>
                                <option value="bkash">বিকাশ</option>
                                <option value="nagad">নগদ (ডিজিটাল)</option>
                                <option value="rocket">রকেট</option>
                                <option value="bank">ব্যাংক ট্রান্সফার</option>
                                <option value="check">চেক</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_receipt_no" class="form-label">রসিদ নং</label>
                            <input type="text" class="form-control" id="edit_receipt_no" name="receipt_no">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-primary" id="saveEditPaymentBtn">সংরক্ষণ করুন</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deletePaymentModal" tabindex="-1" aria-labelledby="deletePaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deletePaymentModalLabel">পেমেন্ট মুছে ফেলার নিশ্চিতকরণ</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি এই পেমেন্ট রেকর্ডটি মুছে ফেলতে চান?</p>
                    <p class="text-danger"><strong>সতর্কতা:</strong> এই পদক্ষেপটি পূর্বাবস্থায় ফেরানো যাবে না।</p>
                    <input type="hidden" id="delete_payment_id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="button" class="btn btn-danger" id="confirmDeletePaymentBtn">মুছে ফেলুন</button>
                </div>
            </div>
        </div>
    </div>

<?php
// Include footer file safely with error suppression
@include_once('../includes/footer.php');
?>

<!-- Footer Fallback - Used if the footer.php file cannot be loaded -->
<script>
    // Check if the footer has been included
    if (!document.querySelector('footer.sticky-footer')) {
        document.write(`
            <footer class="sticky-footer bg-white mt-4">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>কপিরাইট &copy; কলেজ ম্যানেজমেন্ট সিস্টেম ${new Date().getFullYear()}</span>
                    </div>
                </div>
            </footer>
        `);
    }
    </script>
</body>
</html> 