<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Check if payment was successful
if (isset($_GET['payment_success']) && $_GET['payment_success'] == '1') {
    $successMessage = "পেমেন্ট সফলভাবে সংগ্রহ করা হয়েছে!";
}

// Check if the receipt_no column exists in the fee_payments table
$columnCheckQuery = "SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND TABLE_NAME = 'fee_payments' 
                    AND COLUMN_NAME = 'receipt_no'";
$columnResult = $conn->query($columnCheckQuery);

// If the column doesn't exist, add it
if ($columnResult && $columnResult->num_rows === 0) {
    $addColumnQuery = "ALTER TABLE fee_payments ADD COLUMN receipt_no VARCHAR(50) NULL AFTER payment_method";
    $conn->query($addColumnQuery);
}

// Ensure necessary tables exist
$feesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_status ENUM('due', 'partial', 'paid') DEFAULT 'due',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feesTableQuery);

// Create fee_payments table if it doesn't exist
$paymentTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_id INT(11) NOT NULL,
    receipt_id INT(11) NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    receipt_no VARCHAR(50) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
)";
$conn->query($paymentTableQuery);

// Create payment_receipts table if it doesn't exist
$receiptTableQuery = "CREATE TABLE IF NOT EXISTS payment_receipts (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50) DEFAULT 'cash',
    total_amount DECIMAL(10,2) NOT NULL,
    receipt_no VARCHAR(50) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($receiptTableQuery);

$student = null;
$fees = [];
$totalDue = 0;

// If student ID is provided, fetch their data and fee records
if (isset($_GET['student_id']) && !empty($_GET['student_id'])) {
    $studentId = intval($_GET['student_id']);
    
    // Get student information
    $studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                   FROM students s
                   LEFT JOIN classes c ON s.class_id = c.id
                   LEFT JOIN departments d ON s.department_id = d.id
                   LEFT JOIN sessions ss ON s.session_id = ss.id
                   WHERE s.id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("i", $studentId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $student = $result->fetch_assoc();
        
        // Get all unpaid/partially paid fees for this student
        $feesQuery = "SELECT * FROM fees 
                     WHERE student_id = ? AND payment_status IN ('due', 'partial')
                     ORDER BY due_date ASC";
        $stmt = $conn->prepare($feesQuery);
        $stmt->bind_param("i", $studentId);
        $stmt->execute();
        $feesResult = $stmt->get_result();
        
        // Array to track duplicates
        $feeTypes = [];
        $hasDuplicates = false;
        
        while ($fee = $feesResult->fetch_assoc()) {
            $fees[] = $fee;
            $totalDue += ($fee['amount'] - $fee['paid']);
            
            // Check for duplicates
            if (isset($feeTypes[$fee['fee_type']])) {
                $hasDuplicates = true;
                $feeTypes[$fee['fee_type']][] = $fee['id'];
            } else {
                $feeTypes[$fee['fee_type']] = [$fee['id']];
            }
        }
    }
}

// Process multiple fee payments
if (isset($_POST['collect_multiple_payments'])) {
    $studentId = $_POST['student_id'];
    $paymentDate = $_POST['payment_date'];
    $paymentMethod = $_POST['payment_method'];
    $receiptNo = $_POST['receipt_no'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $feeIds = $_POST['fee_ids'] ?? [];
    $amounts = $_POST['amounts'] ?? [];
    
    if (empty($feeIds) || empty($amounts)) {
        $errorMessage = "কোন ফি নির্বাচন করা হয়নি!";
    } else {
        // Begin transaction
        $conn->begin_transaction();
        
        try {
            $totalAmount = 0;
            
            // Create a receipt record
            $receiptQuery = "INSERT INTO payment_receipts 
                            (student_id, payment_date, payment_method, total_amount, receipt_no, notes) 
                            VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($receiptQuery);
            
            // Calculate total first
            foreach ($feeIds as $index => $feeId) {
                if (isset($amounts[$index]) && $amounts[$index] > 0) {
                    $totalAmount += $amounts[$index];
                }
            }
            
            $stmt->bind_param("issdss", $studentId, $paymentDate, $paymentMethod, $totalAmount, $receiptNo, $notes);
            $stmt->execute();
            $receiptId = $conn->insert_id;
            
            // Process each payment
            foreach ($feeIds as $index => $feeId) {
                $amount = $amounts[$index];
                
                if ($amount > 0) {
                    // Get current fee information
                    $feeQuery = "SELECT * FROM fees WHERE id = ?";
                    $stmt = $conn->prepare($feeQuery);
                    $stmt->bind_param("i", $feeId);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    $fee = $result->fetch_assoc();
                    
                    $totalPaid = $fee['paid'] + $amount;
                    $totalAmount = $fee['amount'];
                    
                    // Calculate new payment status
                    $paymentStatus = 'due';
                    if ($totalPaid >= $totalAmount) {
                        $paymentStatus = 'paid';
                        // Ensure paid amount doesn't exceed total amount
                        $totalPaid = $totalAmount;
                    } elseif ($totalPaid > 0) {
                        $paymentStatus = 'partial';
                    }
                    
                    // Insert payment record
                    $insertPaymentQuery = "INSERT INTO fee_payments 
                                         (fee_id, receipt_id, amount, payment_date, payment_method, receipt_no, notes) 
                                         VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($insertPaymentQuery);
                    $stmt->bind_param("iidssss", $feeId, $receiptId, $amount, $paymentDate, $paymentMethod, $receiptNo, $notes);
                    $stmt->execute();
                    
                    // Update fee record
                    $updateFeeQuery = "UPDATE fees SET paid = ?, payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
                    $stmt = $conn->prepare($updateFeeQuery);
                    $stmt->bind_param("dsi", $totalPaid, $paymentStatus, $feeId);
                    $stmt->execute();
                }
            }
            
            // Commit the transaction
            $conn->commit();
            
            $successMessage = "পেমেন্ট সফলভাবে সংগ্রহ করা হয়েছে!";
            
            // Reload the current page with success message
            $studentId = $student['id'];
            header("Location: fee_collect_multiple.php?student_id=$studentId&payment_success=1");
            exit();
        } catch (Exception $e) {
            // Roll back the transaction if something failed
            $conn->rollback();
            $errorMessage = "পেমেন্ট সংগ্রহ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
    }
}

// Get all students with unpaid fees for student selection dropdown
$studentsWithDuesQuery = "SELECT DISTINCT s.id, s.student_id as roll, s.first_name, s.last_name, c.class_name, d.department_name, ss.session_name
                         FROM students s
                         JOIN fees f ON s.id = f.student_id
                         LEFT JOIN classes c ON s.class_id = c.id
                         LEFT JOIN departments d ON s.department_id = d.id
                         LEFT JOIN sessions ss ON s.session_id = ss.id
                         WHERE f.payment_status IN ('due', 'partial')
                         ORDER BY s.student_id";
$studentsWithDues = $conn->query($studentsWithDuesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>একাধিক ফি সংগ্রহ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .card-header {
            border-radius: 10px 10px 0 0;
            font-weight: 600;
        }
        .card-body {
            padding: 1.5rem;
        }
        .student-card {
            background: linear-gradient(to right, #f9f9f9, #ffffff);
            transition: all 0.3s ease;
        }
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-due {
            background-color: #ffe5e5;
            color: #e74c3c;
        }
        .status-partial {
            background-color: #fff5e5;
            color: #f39c12;
        }
        .status-paid {
            background-color: #e5f9e0;
            color: #27ae60;
        }
        .fee-item {
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }
        .fee-item:hover {
            background-color: #f8f9fa;
        }
        .fee-item.selected {
            background-color: #e3f2fd;
            border: 1px solid #90caf9;
        }
        .payment-summary {
            background-color: #f1f8e9;
            border-radius: 8px;
            padding: 15px;
        }
        .form-control, .form-select {
            border-radius: 8px;
            padding: 10px 15px;
        }
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
        }
        .btn-primary {
            background: linear-gradient(to right, #2563eb, #3b82f6);
            border: none;
        }
        .btn-success {
            background: linear-gradient(to right, #16a34a, #22c55e);
            border: none;
        }
        .payment-method-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .payment-method-card {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px;
            width: calc(33.33% - 10px);
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .payment-method-card:hover {
            border-color: #90caf9;
            background-color: #f0f7ff;
        }
        .payment-method-card.selected {
            border-color: #2563eb;
            background-color: #e3f2fd;
        }
        .payment-method-card i {
            font-size: 24px;
            margin-bottom: 5px;
            color: #2563eb;
        }
        .payment-method-card span {
            display: block;
            font-size: 14px;
        }
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .quick-actions button {
            flex: 1;
            padding: 8px;
            font-size: 14px;
        }
        /* Enhanced search styles */
        .accordion-button:not(.collapsed) {
            background-color: #f8f9fa;
            color: #0d6efd;
            box-shadow: none;
        }
        .accordion-button:focus {
            box-shadow: none;
            border-color: #dee2e6;
        }
        .dropdown-item.active, .dropdown-item:active {
            background-color: #0d6efd;
        }
        .form-select-sm {
            font-size: 0.875rem;
            padding: 0.4rem 0.8rem;
        }
        .badge {
            font-weight: normal;
            font-size: 85%;
        }
        .search-highlight {
            background-color: #fff3cd;
            padding: 0 2px;
            border-radius: 2px;
        }
        .search-animation {
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
        .no-results-message {
            text-align: center;
            padding: 30px 0;
            color: #6c757d;
        }
        .no-results-message i {
            font-size: 3rem;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        .search-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        .search-loading .spinner-border {
            width: 3rem;
            height: 3rem;
        }
        .student-card {
            position: relative;
            overflow: hidden;
        }
        .student-card-badge {
            position: absolute;
            top: 0;
            right: 0;
            padding: 4px 8px;
            font-size: 10px;
            border-radius: 0 5px 0 5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_collect.php">
                            <i class="fas fa-hand-holding-usd me-2"></i> ফি কালেকশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_types.php">
                            <i class="fas fa-tags me-2"></i> ফি টাইপ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_reports.php">
                            <i class="fas fa-chart-bar me-2"></i> ফি রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="container-fluid px-4 py-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="fs-2 fw-bold">
                            <i class="fas fa-money-bill-wave me-2 text-primary"></i> একাধিক ফি সংগ্রহ
                        </h1>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                                <li class="breadcrumb-item"><a href="fees.php">ফি ম্যানেজমেন্ট</a></li>
                                <li class="breadcrumb-item active">একাধিক ফি সংগ্রহ</li>
                            </ol>
                        </nav>
                    </div>
                    
                    <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i> <?= $errorMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i> <?= $successMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="row">
                        <!-- Student Selection Column -->
                        <div class="col-lg-5">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী নির্বাচন করুন</h5>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Search Box -->
                                    <div class="mb-4">
                                        <div class="accordion" id="searchAccordion">
                                            <div class="accordion-item border-0">
                                                <h2 class="accordion-header" id="searchHeading">
                                                    <button class="accordion-button p-2 bg-light" type="button" data-bs-toggle="collapse" data-bs-target="#searchCollapse" aria-expanded="true" aria-controls="searchCollapse">
                                                        <i class="fas fa-filter me-2"></i> উন্নত অনুসন্ধান ফিল্টার
                                                    </button>
                                                </h2>
                                                <div id="searchCollapse" class="accordion-collapse collapse show" aria-labelledby="searchHeading">
                                                    <div class="accordion-body p-2">
                                                        <div class="row g-2">
                                                            <div class="col-12 mb-2">
                                                                <div class="input-group">
                                                                    <span class="input-group-text bg-light">
                                                                        <i class="fas fa-search text-muted"></i>
                                                                    </span>
                                                                    <input type="text" id="studentSearch" class="form-control" placeholder="শিক্ষার্থী রোল/আইডি/নাম লিখুন...">
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <select id="classFilter" class="form-select form-select-sm">
                                                                    <option value="">সকল ক্লাস</option>
                                                                    <?php
                                                                    $classQuery = "SELECT DISTINCT c.class_name 
                                                                                  FROM classes c 
                                                                                  JOIN students s ON c.id = s.class_id 
                                                                                  JOIN fees f ON s.id = f.student_id 
                                                                                  WHERE f.payment_status IN ('due', 'partial')
                                                                                  ORDER BY c.class_name";
                                                                    $classResult = $conn->query($classQuery);
                                                                    if ($classResult && $classResult->num_rows > 0) {
                                                                        while ($row = $classResult->fetch_assoc()) {
                                                                            echo '<option value="' . $row['class_name'] . '">' . $row['class_name'] . '</option>';
                                                                        }
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <select id="deptFilter" class="form-select form-select-sm">
                                                                    <option value="">সকল বিভাগ</option>
                                                                    <?php
                                                                    $deptQuery = "SELECT DISTINCT d.department_name 
                                                                                FROM departments d 
                                                                                JOIN students s ON d.id = s.department_id 
                                                                                JOIN fees f ON s.id = f.student_id 
                                                                                WHERE f.payment_status IN ('due', 'partial')
                                                                                ORDER BY d.department_name";
                                                                    $deptResult = $conn->query($deptQuery);
                                                                    if ($deptResult && $deptResult->num_rows > 0) {
                                                                        while ($row = $deptResult->fetch_assoc()) {
                                                                            echo '<option value="' . $row['department_name'] . '">' . $row['department_name'] . '</option>';
                                                                        }
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <select id="statusFilter" class="form-select form-select-sm">
                                                                    <option value="">সকল স্ট্যাটাস</option>
                                                                    <option value="due">শুধু বকেয়া</option>
                                                                    <option value="partial">আংশিক পরিশোধিত</option>
                                                                    <option value="high">উচ্চ বকেয়া প্রথমে</option>
                                                                </select>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="btn-group w-100">
                                                                    <button type="button" id="resetFilterBtn" class="btn btn-sm btn-outline-secondary">
                                                                        <i class="fas fa-redo-alt me-1"></i> রিসেট
                                                                    </button>
                                                                    <button type="button" id="applyFilterBtn" class="btn btn-sm btn-primary">
                                                                        <i class="fas fa-filter me-1"></i> ফিল্টার
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Search Stats -->
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <p class="mb-0 text-muted small"><span id="studentCount">0</span> জন শিক্ষার্থী পাওয়া গেছে</p>
                                        <div class="d-flex gap-2">
                                            <!-- View Toggle Icons -->
                                            <div class="btn-group" role="group" aria-label="View Options">
                                                <button type="button" class="btn btn-sm btn-outline-primary view-option" data-view="grid">
                                                    <i class="fas fa-th-large"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-primary active view-option" data-view="list">
                                                    <i class="fas fa-list"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-primary view-option" data-view="detail">
                                                    <i class="fas fa-th-list"></i>
                                                </button>
                                            </div>
                                            
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-sort me-1"></i> সাজানো
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                                                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-asc">নাম (A-Z)</a></li>
                                                    <li><a class="dropdown-item sort-option" href="#" data-sort="name-desc">নাম (Z-A)</a></li>
                                                    <li><a class="dropdown-item sort-option" href="#" data-sort="roll-asc">রোল (ছোট থেকে বড়)</a></li>
                                                    <li><a class="dropdown-item sort-option" href="#" data-sort="roll-desc">রোল (বড় থেকে ছোট)</a></li>
                                                    <li><a class="dropdown-item sort-option" href="#" data-sort="due-desc">বকেয়া (বেশি থেকে কম)</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- No Results Message -->
                                    <div id="noResultsMessage" class="col-12 no-results-message" style="display: none;">
                                        <i class="fas fa-search"></i>
                                        <h4>কোন ছাত্র/ছাত্রী পাওয়া যায়নি</h4>
                                        <p>আপনার অনুসন্ধান মানদণ্ড পরিবর্তন করে আবার চেষ্টা করুন</p>
                                        <button class="btn btn-outline-primary mt-2" id="resetSearchBtn">
                                            <i class="fas fa-redo me-1"></i> ফিল্টার রিসেট করুন
                                        </button>
                                    </div>

                                    <!-- Student Cards Container -->
                                    <div class="row" id="studentCardsContainer">
                                        <!-- Student Cards will be loaded here -->
                                        <?php if ($studentsWithDues && $studentsWithDues->num_rows > 0): ?>
                                            <?php while ($student_item = $studentsWithDues->fetch_assoc()): ?>
                                                <a href="fee_collect_multiple.php?student_id=<?= $student_item['id'] ?>" class="text-decoration-none">
                                                    <div class="card student-card mb-3 <?= (isset($student) && $student['id'] == $student_item['id']) ? 'border-primary' : '' ?>"
                                                         data-student-id="<?= $student_item['id'] ?>"
                                                         data-roll="<?= $student_item['roll'] ?>"
                                                         data-name="<?= $student_item['first_name'] . ' ' . $student_item['last_name'] ?>"
                                                         data-class="<?= $student_item['class_name'] ?? '' ?>"
                                                         data-department="<?= $student_item['department_name'] ?? '' ?>"
                                                         data-session="<?= $student_item['session_name'] ?? '' ?>"
                                                         data-status="<?php 
                                                            // Get payment status for this student
                                                            $statusQuery = "SELECT 
                                                                             CASE 
                                                                                WHEN COUNT(*) = SUM(CASE WHEN payment_status = 'due' THEN 1 ELSE 0 END) THEN 'due'
                                                                                WHEN SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) > 0 THEN 'partial'
                                                                                ELSE 'due'
                                                                             END as overall_status,
                                                                             SUM(amount - paid) as total_due
                                                                           FROM fees 
                                                                           WHERE student_id = ? AND payment_status IN ('due', 'partial')";
                                                            $stmt = $conn->prepare($statusQuery);
                                                            $stmt->bind_param("i", $student_item['id']);
                                                            $stmt->execute();
                                                            $statusResult = $stmt->get_result();
                                                            $statusData = $statusResult->fetch_assoc();
                                                            echo $statusData['overall_status'] ?? 'due';
                                                         ?>"
                                                         data-due-amount="<?= $statusData['total_due'] ?? 0 ?>">
                                                        <div class="card-body py-3">
                                                            <div class="d-flex justify-content-between align-items-center">
                                                                <div>
                                                                    <h6 class="mb-1 text-dark"><?= $student_item['first_name'] . ' ' . $student_item['last_name'] ?></h6>
                                                                    <div class="text-muted small">
                                                                        <span class="me-2"><i class="fas fa-id-card me-1"></i> <?= $student_item['roll'] ?></span>
                                                                        <?php if (!empty($student_item['class_name'])): ?>
                                                                        <span class="me-2"><i class="fas fa-graduation-cap me-1"></i> <?= $student_item['class_name'] ?></span>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                </div>
                                                                <div class="d-flex align-items-center">
                                                                    <span class="badge bg-<?= $statusData['overall_status'] == 'due' ? 'danger' : 'warning' ?> me-2">
                                                                        ৳ <?= number_format($statusData['total_due'] ?? 0, 0) ?>
                                                                    </span>
                                                                    <i class="fas fa-chevron-right text-muted"></i>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </a>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <div class="text-center py-5">
                                                <i class="fas fa-info-circle text-muted fa-3x mb-3"></i>
                                                <p class="text-muted">কোন শিক্ষার্থীর বকেয়া ফি নেই।</p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fee Collection Column -->
                        <div class="col-lg-7">
                            <?php if ($student): ?>
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0"><i class="fas fa-user me-2"></i> শিক্ষার্থী তথ্য</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>নাম:</strong> <?= $student['first_name'] . ' ' . $student['last_name'] ?></p>
                                            <p class="mb-1"><strong>আইডি/রোল:</strong> <?= $student['student_id'] ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>ক্লাস:</strong> <?= $student['class_name'] ?? 'N/A' ?></p>
                                            <p class="mb-1"><strong>বিভাগ:</strong> <?= $student['department_name'] ?? 'N/A' ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (count($fees) > 0): ?>
                            <?php if(isset($hasDuplicates) && $hasDuplicates): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i> <strong>সতর্কতা:</strong> এই শিক্ষার্থীর একই ধরনের একাধিক ফি পাওয়া গেছে। 
                                <a href="fix_duplicate_fees.php" class="alert-link">দ্রুত ফিক্স করতে এখানে ক্লিক করুন</a> অথবা 
                                <a href="remove_duplicate_fees.php" class="alert-link">ডুপ্লিকেট ফি ম্যানেজ করতে এখানে ক্লিক করুন</a>।
                            </div>
                            <?php endif; ?>
                            <form method="post" action="" id="multiplePaymentForm">
                                <input type="hidden" name="student_id" value="<?= $student['id'] ?>">
                                
                                <div class="card mb-4">
                                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> বকেয়া ফি সমূহ</h5>
                                        <div>
                                            <span class="badge bg-light text-dark">মোট বকেয়া: ৳ <?= number_format($totalDue, 2) ?></span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <!-- Unpaid Fees List -->
                                        <div class="fees-list">
                                            <?php foreach ($fees as $index => $fee): ?>
                                                <?php $dueAmount = $fee['amount'] - $fee['paid']; ?>
                                                <div class="fee-item border">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-1">
                                                            <div class="form-check">
                                                                <input class="form-check-input fee-checkbox" type="checkbox" 
                                                                    id="fee_check_<?= $fee['id'] ?>" 
                                                                    data-fee-id="<?= $fee['id'] ?>"
                                                                    data-due-amount="<?= $dueAmount ?>">
                                                                <label class="form-check-label" for="fee_check_<?= $fee['id'] ?>"></label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <h6 class="mb-1"><?= $fee['fee_type'] ?></h6>
                                                            <small class="text-muted">দেয় তারিখ: <?= date('d/m/Y', strtotime($fee['due_date'])) ?></small>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="d-flex justify-content-between">
                                                                <div>
                                                                    <small class="d-block">মোট: ৳ <?= number_format($fee['amount'], 2) ?></small>
                                                                    <small class="d-block">পরিশোধিত: ৳ <?= number_format($fee['paid'], 2) ?></small>
                                                                </div>
                                                                <div>
                                                                    <span class="status-badge <?= 
                                                                        $fee['payment_status'] == 'due' ? 'status-due' : 
                                                                        ($fee['payment_status'] == 'partial' ? 'status-partial' : 'status-paid') ?>">
                                                                        <?= 
                                                                        $fee['payment_status'] == 'due' ? 'বকেয়া' : 
                                                                        ($fee['payment_status'] == 'partial' ? 'আংশিক' : 'পরিশোধিত') ?>
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="input-group">
                                                                <span class="input-group-text">৳</span>
                                                                <input type="number" 
                                                                    class="form-control fee-amount" 
                                                                    name="amounts[]" 
                                                                    value="<?= number_format($dueAmount, 2, '.', '') ?>" 
                                                                    min="0" 
                                                                    max="<?= $dueAmount ?>" 
                                                                    step="0.01" 
                                                                    disabled
                                                                    required>
                                                                <input type="hidden" name="fee_ids[]" value="<?= $fee['id'] ?>" disabled>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        
                                        <div class="quick-actions mb-3">
                                            <button type="button" id="selectAllBtn" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-check-square me-1"></i> সব নির্বাচন করুন
                                            </button>
                                            <button type="button" id="deselectAllBtn" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-square me-1"></i> সব বাদ দিন
                                            </button>
                                            <button type="button" id="payFullBtn" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-money-bill-wave me-1"></i> পুরো টাকা
                                            </button>
                                            <button type="button" id="payHalfBtn" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-money-bill me-1"></i> অর্ধেক টাকা
                                            </button>
                                        </div>
                                        
                                        <!-- Payment Summary -->
                                        <div class="payment-summary mb-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0"><i class="fas fa-calculator me-2"></i> পেমেন্ট সারসংক্ষেপ</h6>
                                                <h6 class="mb-0">মোট: <span id="totalPaymentAmount">৳ 0.00</span></h6>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card mb-4">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i> পেমেন্ট তথ্য</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                                                <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="receipt_no" class="form-label">রসিদ নং (ঐচ্ছিক)</label>
                                                <input type="text" class="form-control" id="receipt_no" name="receipt_no" value="REC-<?= date('Ymd') ?>-<?= rand(1000, 9999) ?>">
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">পেমেন্ট মাধ্যম</label>
                                            <input type="hidden" id="payment_method" name="payment_method" value="cash">
                                            <div class="payment-method-cards">
                                                <div class="payment-method-card selected" data-method="cash">
                                                    <i class="fas fa-money-bill-wave"></i>
                                                    <span>নগদ</span>
                                                </div>
                                                <div class="payment-method-card" data-method="bkash">
                                                    <i class="fas fa-mobile-alt"></i>
                                                    <span>বিকাশ</span>
                                                </div>
                                                <div class="payment-method-card" data-method="nagad">
                                                    <i class="fas fa-wallet"></i>
                                                    <span>নগদ (ডিজিটাল)</span>
                                                </div>
                                                <div class="payment-method-card" data-method="rocket">
                                                    <i class="fas fa-rocket"></i>
                                                    <span>রকেট</span>
                                                </div>
                                                <div class="payment-method-card" data-method="bank">
                                                    <i class="fas fa-university"></i>
                                                    <span>ব্যাংক ট্রান্সফার</span>
                                                </div>
                                                <div class="payment-method-card" data-method="check">
                                                    <i class="fas fa-money-check"></i>
                                                    <span>চেক</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="notes" class="form-label">মন্তব্য (ঐচ্ছিক)</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                        </div>
                                        
                                        <div class="d-grid gap-2">
                                            <button type="submit" name="collect_multiple_payments" class="btn btn-success" id="submitPaymentBtn" disabled>
                                                <i class="fas fa-check-circle me-1"></i> পেমেন্ট সম্পন্ন করুন
                                            </button>
                                            <a href="fees.php" class="btn btn-outline-secondary">
                                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <?php else: ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-check-circle text-success fa-4x mb-3"></i>
                                    <h4>এই শিক্ষার্থীর কোন বকেয়া ফি নেই</h4>
                                    <p class="text-muted">সকল ফি পরিশোধ করা হয়েছে</p>
                                    <div class="mt-4">
                                        <a href="fees.php" class="btn btn-primary">
                                            <i class="fas fa-arrow-left me-1"></i> ফি পেজে ফিরে যান
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php else: ?>
                            <div class="card">
                                <div class="card-body text-center py-5">
                                    <i class="fas fa-hand-point-left fa-4x text-muted mb-3"></i>
                                    <h4>একজন শিক্ষার্থী নির্বাচন করুন</h4>
                                    <p class="text-muted">ফি পেমেন্ট তৈরি করতে বাম পাশ থেকে শিক্ষার্থী নির্বাচন করুন</p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // View options functionality
            const viewOptions = $(".view-option");
            let currentView = "list"; // Default view
            
            // Handle view option clicks
            viewOptions.click(function() {
                const viewType = $(this).data('view');
                
                // Update active button
                viewOptions.removeClass('active');
                $(this).addClass('active');
                
                // Handle view change
                changeView(viewType);
            });
            
            // Function to change view
            function changeView(viewType) {
                if (viewType === currentView) return;
                
                currentView = viewType;
                
                // For now, just show an alert since actual view changes would require more extensive UI changes
                if (viewType === 'grid') {
                    alert('গ্রিড ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                } else if (viewType === 'detail') {
                    alert('বিস্তারিত ভিউ বৈশিষ্ট্য বর্তমানে বিকাশের অধীনে আছে।');
                }
                
                // Default back to list view for now
                if (viewType !== 'list') {
                    setTimeout(() => {
                        viewOptions.removeClass('active');
                        $(".view-option[data-view='list']").addClass('active');
                        currentView = 'list';
                    }, 300);
                }
            }
            
            // Set today's date as default payment date
            const today = new Date();
            const formattedDate = today.toISOString().substr(0, 10);
            $('#payment_date').val(formattedDate);
            
            // Update student count
            updateStudentCount();
            
            // Advanced search functionality
            let searchTimeout;
            let allStudents = [];
            
            // Initialize - store all visible students
            $('#studentCardsContainer .student-card').each(function() {
                allStudents.push({
                    element: $(this),
                    id: $(this).data('student-id'),
                    name: $(this).data('name').toLowerCase(),
                    roll: $(this).data('roll').toLowerCase(),
                    class: $(this).data('class').toLowerCase(),
                    department: $(this).data('department').toLowerCase(),
                    due: parseFloat($(this).data('due-amount') || 0)
                });
            });
            
            // Search function
            $('#studentSearch').on('keyup', function() {
                const searchValue = $(this).val().toLowerCase().trim();
                
                clearTimeout(searchTimeout);
                
                searchTimeout = setTimeout(function() {
                    // Hide all student cards initially
                    $('.student-card').hide();
                    
                    // Filter and show matching student cards
                    if (searchValue) {
                        $('.student-card').each(function() {
                            const studentRoll = $(this).data('roll') ? $(this).data('roll').toString().toLowerCase() : '';
                            const studentName = $(this).data('name') ? $(this).data('name').toString().toLowerCase() : '';
                            
                            if (studentRoll.includes(searchValue) || studentName.includes(searchValue)) {
                                $(this).show();
                            }
                        });
                    } else {
                        // If search is empty, show all cards
                        $('.student-card').show();
                    }
                    
                    // Update student count after filtering
                    updateStudentCount();
                    
                    // Show/hide no results message
                    if ($('.student-card:visible').length === 0) {
                        $('#noResultsMessage').show();
                    } else {
                        $('#noResultsMessage').hide();
                    }
                }, 300);
            });
            
            // Class filter
            $('#classFilter').on('change', function() {
                applyAllFilters();
            });
            
            // Department filter
            $('#deptFilter').on('change', function() {
                applyAllFilters();
            });
            
            // Status filter
            $('#statusFilter').on('change', function() {
                applyAllFilters();
            });
            
            // Apply all filters function
            function applyAllFilters() {
                const classValue = $('#classFilter').val().toLowerCase();
                const deptValue = $('#deptFilter').val().toLowerCase();
                const statusValue = $('#statusFilter').val();
                const searchValue = $('#studentSearch').val().toLowerCase();
                
                // Hide all student cards initially
                $('.student-card').hide();
                
                // Apply combined filters
                $('.student-card').each(function() {
                    const studentClass = $(this).data('class') ? $(this).data('class').toString().toLowerCase() : '';
                    const studentDept = $(this).data('department') ? $(this).data('department').toString().toLowerCase() : '';
                    const studentStatus = $(this).data('status') ? $(this).data('status').toString().toLowerCase() : '';
                    const studentRoll = $(this).data('roll') ? $(this).data('roll').toString().toLowerCase() : '';
                    const studentName = $(this).data('name') ? $(this).data('name').toString().toLowerCase() : '';
                    const dueAmount = parseFloat($(this).data('due-amount') || 0);
                    
                    let matchesClass = true;
                    let matchesDept = true;
                    let matchesStatus = true;
                    let matchesSearch = true;
                    
                    // Check class filter
                    if (classValue) {
                        matchesClass = studentClass === classValue;
                    }
                    
                    // Check department filter
                    if (deptValue) {
                        matchesDept = studentDept === deptValue;
                    }
                    
                    // Check status filter
                    if (statusValue) {
                        if (statusValue === 'high') {
                            matchesStatus = true; // We'll sort by due amount later
                        } else {
                            matchesStatus = studentStatus === statusValue;
                        }
                    }
                    
                    // Check search filter
                    if (searchValue) {
                        matchesSearch = studentRoll.includes(searchValue) || 
                                      studentName.includes(searchValue);
                    }
                    
                    // Show card if it matches all filters
                    if (matchesClass && matchesDept && matchesStatus && matchesSearch) {
                        $(this).show();
                    }
                });
                
                // Sort by due amount if 'high' status is selected
                if (statusValue === 'high') {
                    sortStudentsByDueAmount();
                }
                
                // Update student count after filtering
                updateStudentCount();
                
                // Show/hide no results message
                if ($('.student-card:visible').length === 0) {
                    $('#noResultsMessage').show();
                } else {
                    $('#noResultsMessage').hide();
                }
            }
            
            // Sort students by due amount function
            function sortStudentsByDueAmount() {
                const container = $('#studentCardsContainer');
                const students = container.children('.student-card').get();
                
                students.sort(function(a, b) {
                    const amountA = parseFloat($(a).data('due-amount') || 0);
                    const amountB = parseFloat($(b).data('due-amount') || 0);
                    return amountB - amountA; // Descending order (high to low)
                });
                
                $.each(students, function(idx, student) {
                    container.append(student);
                });
            }
            
            // Reset filter button
            $('#resetFilterBtn, #resetSearchBtn').on('click', function() {
                $('#studentSearch').val('');
                $('#classFilter').val('');
                $('#deptFilter').val('');
                $('#statusFilter').val('');
                
                // Show all cards
                $('.student-card').show();
                
                // Hide no results message
                $('#noResultsMessage').hide();
                
                // Update student count
                updateStudentCount();
            });
            
            // Apply filter button
            $('#applyFilterBtn').on('click', function() {
                applyAllFilters();
            });
            
            // Sorting options
            $('.sort-option').on('click', function(e) {
                e.preventDefault();
                const sortOption = $(this).data('sort');
                sortStudents(sortOption);
            });
            
            // Sort students function
            function sortStudents(sortOption) {
                const container = $('#studentCardsContainer');
                const students = container.children('.student-card').get();
                
                students.sort(function(a, b) {
                    if (sortOption === 'name-asc') {
                        return $(a).data('name').localeCompare($(b).data('name'));
                    } else if (sortOption === 'name-desc') {
                        return $(b).data('name').localeCompare($(a).data('name'));
                    } else if (sortOption === 'roll-asc') {
                        return $(a).data('roll').localeCompare($(b).data('roll'));
                    } else if (sortOption === 'roll-desc') {
                        return $(b).data('roll').localeCompare($(a).data('roll'));
                    } else if (sortOption === 'due-desc') {
                        const amountA = parseFloat($(a).data('due-amount') || 0);
                        const amountB = parseFloat($(b).data('due-amount') || 0);
                        return amountB - amountA;
                    }
                    return 0;
                });
                
                $.each(students, function(idx, student) {
                    container.append(student);
                });
            }
            
            // Payment Method Selection
            $('.payment-method-card').on('click', function() {
                $('.payment-method-card').removeClass('active');
                $(this).addClass('active');
                $('#payment_method').val($(this).data('method'));
            });
            
            // Handle fee selection
            $(".fee-checkbox").change(function() {
                const feeItem = $(this).closest('.fee-item');
                const feeId = $(this).data('fee-id');
                const dueAmount = $(this).data('due-amount');
                const formInputs = feeItem.find('input');
                
                // Check if this fee is already selected and exists elsewhere in the form
                if($(this).is(':checked')) {
                    // Look for other checkboxes with the same fee ID that are already checked
                    const duplicateCheckboxes = $(".fee-checkbox:checked").not(this).filter(function() {
                        return $(this).data('fee-id') === feeId;
                    });
                    
                    if(duplicateCheckboxes.length > 0) {
                        // If duplicates exist, uncheck this checkbox and show a message
                        $(this).prop('checked', false);
                        alert('এই ফি ইতিমধ্যে নির্বাচিত আছে!');
                        return;
                    }
                    
                    feeItem.addClass('selected');
                    formInputs.prop('disabled', false);
                } else {
                    feeItem.removeClass('selected');
                    formInputs.prop('disabled', true);
                }
                
                updateTotalPayment();
                checkSubmitButton();
            });
            
            // "Select All" button functionality
            $("#selectAllBtn").click(function() {
                // Find all unchecked checkboxes
                const uncheckedBoxes = $(".fee-checkbox:not(:checked)");
                
                // Check each one, but avoid duplicates
                uncheckedBoxes.each(function() {
                    const feeId = $(this).data('fee-id');
                    
                    // Check if this fee ID is already checked elsewhere
                    const duplicateCheckboxes = $(".fee-checkbox:checked").filter(function() {
                        return $(this).data('fee-id') === feeId;
                    });
                    
                    // Only check this box if no duplicates exist
                    if(duplicateCheckboxes.length === 0) {
                        $(this).prop('checked', true).trigger('change');
                    }
                });
            });
            
            // "Deselect All" button functionality
            $("#deselectAllBtn").click(function() {
                $(".fee-checkbox").prop('checked', false).trigger('change');
            });
            
            // "Pay Full" button functionality
            $("#payFullBtn").click(function() {
                $(".fee-checkbox:checked").each(function() {
                    const dueAmount = $(this).data('due-amount');
                    $(this).closest('.fee-item').find('.fee-amount').val(dueAmount.toFixed(2));
                });
                updateTotalPayment();
            });
            
            // "Pay Half" button functionality
            $("#payHalfBtn").click(function() {
                $(".fee-checkbox:checked").each(function() {
                    const dueAmount = $(this).data('due-amount');
                    $(this).closest('.fee-item').find('.fee-amount').val((dueAmount/2).toFixed(2));
                });
                updateTotalPayment();
            });
            
            // Handle amount input changes
            $(document).on('input', '.fee-amount', function() {
                const max = parseFloat($(this).attr('max'));
                const val = parseFloat($(this).val());
                
                if (val > max) {
                    $(this).val(max.toFixed(2));
                }
                
                updateTotalPayment();
            });
            
            // Calculate total payment
            function updateTotalPayment() {
                let total = 0;
                $(".fee-checkbox:checked").each(function() {
                    const amount = parseFloat($(this).closest('.fee-item').find('.fee-amount').val()) || 0;
                    total += amount;
                });
                $("#totalPaymentAmount").text("৳ " + total.toFixed(2));
            }
            
            // Enable/disable submit button based on selections
            function checkSubmitButton() {
                const anyChecked = $(".fee-checkbox:checked").length > 0;
                $("#submitPaymentBtn").prop('disabled', !anyChecked);
            }
            
            // Initialize payment form
            updateTotalPayment();
            checkSubmitButton();
        });

        function updateStudentCount() {
            // Count visible student cards (excluding the No Results message element)
            const visibleStudents = $('#studentCardsContainer a.student-card:visible').length;
            
            // Update counter text
            $('#studentCount').text(visibleStudents);
            
            // Show or hide No Results message
            if (visibleStudents === 0 && $('#studentSearch').val().trim() !== '') {
                $('#noResultsMessage').show();
                $('#studentCardsContainer a.student-card').hide();
            } else {
                $('#noResultsMessage').hide();
            }
        }
    </script>
</body>
</html> 