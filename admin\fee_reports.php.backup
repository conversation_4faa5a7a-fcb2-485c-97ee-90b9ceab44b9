<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get current tab
$currentTab = isset($_GET['tab']) ? $_GET['tab'] : 'search';
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// Get classes, departments, and sessions for filters
$classes = [];
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classesResult = $conn->query($classesQuery);
while ($class = $classesResult->fetch_assoc()) {
    $classes[] = $class;
}

$departments = [];
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departmentsResult = $conn->query($departmentsQuery);
while ($department = $departmentsResult->fetch_assoc()) {
    $departments[] = $department;
}

$sessions = [];
$sessionsQuery = "SELECT * FROM sessions ORDER BY session_name DESC";
$sessionsResult = $conn->query($sessionsQuery);
while ($session = $sessionsResult->fetch_assoc()) {
    $sessions[] = $session;
}

// Get student info if student ID is provided
$student = null;
if ($studentId > 0) {
    $studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                    FROM students s
                    LEFT JOIN classes c ON s.class_id = c.id
                    LEFT JOIN departments d ON s.department_id = d.id
                    LEFT JOIN sessions ss ON s.session_id = ss.id
                    WHERE s.id = ?";
    $stmt = $conn->prepare($studentQuery);
    $stmt->bind_param("i", $studentId);
    $stmt->execute();
    $studentResult = $stmt->get_result();
    
    if ($studentResult->num_rows > 0) {
        $student = $studentResult->fetch_assoc();
    }
}

// Search students based on filters
$students = [];
if (isset($_GET['search']) && $_GET['search'] === '1') {
    $classId = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
    $departmentId = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
    $sessionId = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
    $studentIdQuery = isset($_GET['student_id_query']) ? trim($_GET['student_id_query']) : '';
    $studentName = isset($_GET['student_name']) ? trim($_GET['student_name']) : '';
    
    $studentsQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                     FROM students s
                     LEFT JOIN classes c ON s.class_id = c.id
                     LEFT JOIN departments d ON s.department_id = d.id
                     LEFT JOIN sessions ss ON s.session_id = ss.id
                     WHERE 1=1";
    $params = [];
    $types = "";
    
    if ($classId > 0) {
        $studentsQuery .= " AND s.class_id = ?";
        $params[] = $classId;
        $types .= "i";
    }
    
    if ($departmentId > 0) {
        $studentsQuery .= " AND s.department_id = ?";
        $params[] = $departmentId;
        $types .= "i";
    }
    
    if ($sessionId > 0) {
        $studentsQuery .= " AND s.session_id = ?";
        $params[] = $sessionId;
        $types .= "i";
    }
    
    if (!empty($studentIdQuery)) {
        $studentsQuery .= " AND s.student_id LIKE ?";
        $params[] = "%$studentIdQuery%";
        $types .= "s";
    }
    
    if (!empty($studentName)) {
        $studentsQuery .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR CONCAT(s.first_name, ' ', s.last_name) LIKE ?)";
        $params[] = "%$studentName%";
        $params[] = "%$studentName%";
        $params[] = "%$studentName%";
        $types .= "sss";
    }
    
    $studentsQuery .= " ORDER BY s.first_name LIMIT 100";
    
    $stmt = $conn->prepare($studentsQuery);
    
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    
    $stmt->execute();
    $studentsResult = $stmt->get_result();
    
    while ($student = $studentsResult->fetch_assoc()) {
        $students[] = $student;
    }
}

// Get all students with fee data if needed
if ($currentTab === 'all-students') {
    // Filter parameters
    $allClassId = isset($_GET['all_class_id']) ? intval($_GET['all_class_id']) : 0;
    $allDepartmentId = isset($_GET['all_department_id']) ? intval($_GET['all_department_id']) : 0;
    $allSessionId = isset($_GET['all_session_id']) ? intval($_GET['all_session_id']) : 0;
    
    // Query to get all students with their fee data
    $allStudentsQuery = "SELECT s.id, s.student_id, s.first_name, s.last_name, 
                        c.class_name, d.department_name, ss.session_name,
                        (SELECT SUM(f.amount) FROM fees f WHERE f.student_id = s.id) as total_fees,
                        (SELECT SUM(fp.amount) FROM fee_payments fp JOIN fees f ON fp.fee_id = f.id WHERE f.student_id = s.id) as total_paid,
                        (SELECT COUNT(DISTINCT f.fee_type) FROM fees f WHERE f.student_id = s.id) as fee_types_count
                        FROM students s
                        LEFT JOIN classes c ON s.class_id = c.id
                        LEFT JOIN departments d ON s.department_id = d.id
                        LEFT JOIN sessions ss ON s.session_id = ss.id
                        WHERE 1=1";
    
    $allParams = [];
    $allTypes = "";
    
    if ($allClassId > 0) {
        $allStudentsQuery .= " AND s.class_id = ?";
        $allParams[] = $allClassId;
        $allTypes .= "i";
    }
    
    if ($allDepartmentId > 0) {
        $allStudentsQuery .= " AND s.department_id = ?";
        $allParams[] = $allDepartmentId;
        $allTypes .= "i";
    }
    
    if ($allSessionId > 0) {
        $allStudentsQuery .= " AND s.session_id = ?";
        $allParams[] = $allSessionId;
        $allTypes .= "i";
    }
    
    $allStudentsQuery .= " ORDER BY s.first_name, s.last_name LIMIT 500";
    
    $stmt = $conn->prepare($allStudentsQuery);
    if (!empty($allParams)) {
        $stmt->bind_param($allTypes, ...$allParams);
    }
    $stmt->execute();
    $allStudentsResult = $stmt->get_result();
    
    $allStudents = [];
    $grandTotalFees = 0;
    $grandTotalPaid = 0;
    $grandTotalDue = 0;
    
    while ($studentData = $allStudentsResult->fetch_assoc()) {
        $totalFees = $studentData['total_fees'] ?? 0;
        $totalPaid = $studentData['total_paid'] ?? 0;
        $totalDue = $totalFees - $totalPaid;
        
        $studentData['total_fees'] = $totalFees;
        $studentData['total_paid'] = $totalPaid;
        $studentData['total_due'] = $totalDue;
        
        $grandTotalFees += $totalFees;
        $grandTotalPaid += $totalPaid;
        $grandTotalDue += $totalDue;
        
        $allStudents[] = $studentData;
    }
}

// Get payments for receipt generation
$payments = [];
if ($currentTab === 'student-receipt' && $studentId > 0) {
    $paymentsQuery = "SELECT fp.*, f.fee_type, f.due_date, f.amount as total_fee_amount
                    FROM fee_payments fp
                    JOIN fees f ON fp.fee_id = f.id
                    WHERE f.student_id = ?
                    ORDER BY fp.payment_date DESC";
    
    $stmt = $conn->prepare($paymentsQuery);
    $stmt->bind_param("i", $studentId);
    $stmt->execute();
    $paymentsResult = $stmt->get_result();
    
    while ($payment = $paymentsResult->fetch_assoc()) {
        $payments[] = $payment;
    }
}

// Convert numbers to Bengali
function convertToBengaliNumber($number) {
    $bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    $englishNumber = (string) $number;
    $bengaliNumber = '';
    
    for ($i = 0; $i < strlen($englishNumber); $i++) {
        if (is_numeric($englishNumber[$i])) {
            $bengaliNumber .= $bengaliDigits[(int)$englishNumber[$i]];
        } else {
            $bengaliNumber .= $englishNumber[$i];
        }
    }
    
    return $bengaliNumber;
}

// Format date to Bengali
function formatDateToBengali($date) {
    if (empty($date)) return '';
    
    $timestamp = strtotime($date);
    $day = convertToBengaliNumber(date('d', $timestamp));
    $month = convertToBengaliNumber(date('m', $timestamp));
    $year = convertToBengaliNumber(date('Y', $timestamp));
    
    return "$day/$month/$year";
}

// Prepare data for fee summary report if needed
if ($currentTab === 'fee-summary') {
    // Get filter parameters
    $summarySessionId = isset($_GET['summary_session_id']) ? intval($_GET['summary_session_id']) : 0;
    $summaryClassId = isset($_GET['summary_class_id']) ? intval($_GET['summary_class_id']) : 0;
    
    // Query to get fee summary by class and fee type
    $summaryQuery = "SELECT 
                    c.class_name,
                    f.fee_type,
                    COUNT(DISTINCT f.student_id) as student_count,
                    SUM(f.amount) as total_amount,
                    SUM(f.paid) as total_paid,
                    SUM(f.amount - f.paid) as total_due
                FROM fees f
                JOIN students s ON f.student_id = s.id
                JOIN classes c ON s.class_id = c.id
                WHERE 1=1";
    
    $summaryParams = [];
    $summaryTypes = "";
    
    if ($summarySessionId > 0) {
        $summaryQuery .= " AND s.session_id = ?";
        $summaryParams[] = $summarySessionId;
        $summaryTypes .= "i";
    }
    
    if ($summaryClassId > 0) {
        $summaryQuery .= " AND s.class_id = ?";
        $summaryParams[] = $summaryClassId;
        $summaryTypes .= "i";
    }
    
    $summaryQuery .= " GROUP BY c.class_name, f.fee_type ORDER BY c.class_name, f.fee_type";
    
    $stmt = $conn->prepare($summaryQuery);
    if (!empty($summaryParams)) {
        $stmt->bind_param($summaryTypes, ...$summaryParams);
    }
    $stmt->execute();
    $summaryResult = $stmt->get_result();
    
    $summaryData = [];
    $classSummaries = [];
    $feeTypeSummaries = [];
    
    $grandTotalAmount = 0;
    $grandTotalPaid = 0;
    $grandTotalDue = 0;
    $grandTotalStudents = 0;
    
    while ($row = $summaryResult->fetch_assoc()) {
        $className = $row['class_name'];
        $feeType = $row['fee_type'];
        
        if (!isset($summaryData[$className])) {
            $summaryData[$className] = [
                'total_amount' => 0,
                'total_paid' => 0,
                'total_due' => 0,
                'student_count' => 0,
                'fee_types' => []
            ];
        }
        
        if (!isset($feeTypeSummaries[$feeType])) {
            $feeTypeSummaries[$feeType] = [
                'total_amount' => 0,
                'total_paid' => 0,
                'total_due' => 0,
                'student_count' => 0
            ];
        }
        
        $summaryData[$className]['fee_types'][$feeType] = $row;
        $summaryData[$className]['total_amount'] += $row['total_amount'];
        $summaryData[$className]['total_paid'] += $row['total_paid'];
        $summaryData[$className]['total_due'] += $row['total_due'];
        $summaryData[$className]['student_count'] = max($summaryData[$className]['student_count'], $row['student_count']);
        
        $feeTypeSummaries[$feeType]['total_amount'] += $row['total_amount'];
        $feeTypeSummaries[$feeType]['total_paid'] += $row['total_paid'];
        $feeTypeSummaries[$feeType]['total_due'] += $row['total_due'];
        $feeTypeSummaries[$feeType]['student_count'] += $row['student_count'];
        
        $grandTotalAmount += $row['total_amount'];
        $grandTotalPaid += $row['total_paid'];
        $grandTotalDue += $row['total_due'];
    }
    
    // Get total student count
    $totalStudentsQuery = "SELECT COUNT(DISTINCT s.id) as total_students FROM students s WHERE 1=1";
    $totalStudentsParams = [];
    $totalStudentsTypes = "";
    
    if ($summarySessionId > 0) {
        $totalStudentsQuery .= " AND s.session_id = ?";
        $totalStudentsParams[] = $summarySessionId;
        $totalStudentsTypes .= "i";
    }
    
    if ($summaryClassId > 0) {
        $totalStudentsQuery .= " AND s.class_id = ?";
        $totalStudentsParams[] = $summaryClassId;
        $totalStudentsTypes .= "i";
    }
    
    $stmt = $conn->prepare($totalStudentsQuery);
    if (!empty($totalStudentsParams)) {
        $stmt->bind_param($totalStudentsTypes, ...$totalStudentsParams);
    }
    $stmt->execute();
    $totalStudentsResult = $stmt->get_result();
    $totalStudentsRow = $totalStudentsResult->fetch_assoc();
    $grandTotalStudents = $totalStudentsRow['total_students'] ?? 0;
    
    // Get collection percentage
    $collectionPercent = $grandTotalAmount > 0 ? ($grandTotalPaid / $grandTotalAmount * 100) : 0;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি রিপোর্টস</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 15px 20px;
        }
        .nav-tabs .nav-link {
            border: none;
            color: #6c757d;
            font-weight: 500;
            padding: 10px 15px;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            background-color: transparent;
            border-bottom: 3px solid #0d6efd;
        }
        .tab-content {
            padding: 20px;
        }
        .form-label {
            font-weight: 500;
        }
        .student-card {
            transition: all 0.3s ease;
        }
        .student-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        .payment-item {
            border-left: 3px solid #0d6efd;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .payment-item.selected {
            background-color: #e2f0ff;
            border-left-color: #0d6efd;
        }
        @media print {
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                max-width: 100%;
            }
        }
        
        /* A4 Report Styles */
        @page {
            size: A4;
            margin: 0;
        }
        .a4-report {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20mm;
            position: relative;
            display: none;
        }
        .school-logo {
            height: 80px;
            width: auto;
            margin-bottom: 10px;
        }
        .report-header {
            text-align: center;
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .report-title {
            background-color: #0d6efd;
            color: white;
            padding: 8px 20px;
            display: inline-block;
            border-radius: 20px;
            margin: 15px 0 5px;
        }
        .report-title h4 {
            margin: 0;
            font-size: 18px;
        }
        .student-info-box {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .fee-summary {
            margin-bottom: 25px;
        }
        .summary-box {
            text-align: center;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }
        .summary-title {
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: 500;
        }
        .summary-amount {
            font-size: 20px;
            font-weight: 700;
        }
        .total-fee {
            background-color: #f0f8ff;
            border-left: 4px solid #0d6efd;
        }
        .paid-fee {
            background-color: #f0fff0;
            border-left: 4px solid #198754;
        }
        .due-fee {
            background-color: #fff0f0;
            border-left: 4px solid #dc3545;
        }
        .section-title {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }
        .fee-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .fee-table th, .fee-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
            font-size: 14px;
        }
        .fee-table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        .fee-table tfoot th {
            background-color: #f0f0f0;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-paid {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .status-partial {
            background-color: #fff3cd;
            color: #664d03;
        }
        .status-due {
            background-color: #f8d7da;
            color: #842029;
        }
        .fee-by-type {
            margin-bottom: 20px;
        }
        .fee-type-item {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .fee-type-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .fee-type-header h6 {
            margin: 0;
            font-weight: 500;
        }
        .fee-type-amount {
            font-size: 13px;
        }
        .fee-type-total {
            font-weight: 500;
            margin-right: 10px;
        }
        .fee-type-paid {
            color: #198754;
            margin-right: 10px;
        }
        .fee-type-due {
            color: #dc3545;
        }
        .fee-progress {
            height: 8px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .report-footer {
            margin-top: 40px;
            text-align: center;
            padding-top: 15px;
            border-top: 1px dashed #dee2e6;
            font-size: 13px;
            color: #6c757d;
        }
        @media print {
            body {
                background-color: white;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .a4-report {
                display: block;
                box-shadow: none;
                width: 100%;
                height: auto;
                padding: 20mm;
            }
            .d-print-none {
                display: none !important;
            }
            .report-title {
                background-color: #0d6efd !important;
                color: white !important;
            }
            .fee-table th {
                background-color: #f8f9fa !important;
            }
            .fee-table tfoot th {
                background-color: #f0f0f0 !important;
            }
        }
        
        /* Print styles */
        @media print {
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
            body {
                background-color: #fff;
            }
            .card {
                border: none;
            }
            .a4-report {
                display: block !important;
                box-shadow: none !important;
            }
        }
        
        /* A4 Page Setup */
        @page {
            size: A4;
            margin: 0;
        }
        
        /* A4 Report Base Styles */
        .a4-report {
            width: 210mm;
            min-height: 297mm;
            padding: 15mm;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            display: none;
            position: relative;
        }
        
        /* Fee Report Styles */
        .school-logo {
            max-height: 80px;
            max-width: 80px;
            margin-bottom: 10px;
        }
        
        .report-header {
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
            padding-bottom: 15px;
        }
        
        .report-title {
            margin: 20px 0;
            text-align: center;
        }
        
        .report-title h4 {
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 5px;
            display: inline-block;
            border: 1px solid #ddd;
        }
        
        .student-info-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .summary-box {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .summary-title {
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .summary-amount {
            font-size: 18px;
            font-weight: bold;
        }
        
        .total-fee {
            background-color: #e8f4f8;
            border: 1px solid #c7eaf8;
        }
        
        .paid-fee {
            background-color: #e8f8e8;
            border: 1px solid #c7e8c7;
        }
        
        .due-fee {
            background-color: #f8e8e8;
            border: 1px solid #e8c7c7;
        }
        
        .fee-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .fee-table th,
        .fee-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        
        .fee-table th {
            background-color: #f8f9fa;
            text-align: left;
        }
        
        .fee-table tfoot th {
            background-color: #f2f2f2;
        }
        
        .section-title {
            margin-bottom: 10px;
            font-size: 16px;
            padding: 5px 0;
            border-bottom: 1px solid #ddd;
        }
        
        .text-end {
            text-align: right;
        }
        
        .filter-info {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 20px;
        }
        
        .report-footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            position: absolute;
            bottom: 15mm;
            width: calc(100% - 30mm);
        }
        
        .copyright {
            margin-top: 5px;
            font-style: italic;
        }
        
        /* Fee Summary Report Specific Styles */
        .fee-summary-report .summary-boxes {
            margin-bottom: 20px;
        }
        
        .fee-summary-report .collection-progress {
            margin-bottom: 20px;
        }
        
        .fee-summary-report .class-summary,
        .fee-summary-report .fee-type-summary {
            margin-bottom: 20px;
        }
        
        /* Progress bar in print */
        @media print {
            .progress {
                border: 1px solid #ddd;
                position: relative;
                display: block;
                width: 100%;
                height: 30px;
                overflow: hidden;
            }
            
            .progress-bar {
                position: absolute;
                background-color: #28a745 !important;
                color: #fff !important;
                text-align: center;
                line-height: 30px;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .total-fee {
                background-color: #e8f4f8 !important;
                border: 1px solid #c7eaf8 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .paid-fee {
                background-color: #e8f8e8 !important;
                border: 1px solid #c7e8c7 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .due-fee {
                background-color: #f8e8e8 !important;
                border: 1px solid #e8c7c7 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .fee-table th {
                background-color: #f8f9fa !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            
            .fee-table tfoot th {
                background-color: #f2f2f2 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }
        
        /* Invoice styles */
        .a4-invoice {
            display: none;
        }
        
        .invoice-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 20px;
        }
        
        .invoice-title {
            margin: 15px 0;
        }
        
        .invoice-title h4 {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
        }
        
        .invoice-meta {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .invoice-summary-box {
            margin-bottom: 30px;
        }
        
        .invoice-detail {
            margin-bottom: 30px;
        }
        
        .fee-details-section {
            padding: 0 !important;
            background-color: rgba(0,0,0,.02);
        }
        
        .fee-details-inner {
            padding: 10px;
            font-size: 13px;
        }
        
        .fee-detail-item {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px dashed #ddd;
        }
        
        .fee-detail-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .fee-type {
            font-weight: bold;
            width: 30%;
        }
        
        .fee-due-date {
            width: 20%;
        }
        
        .fee-amount {
            width: 50%;
            text-align: right;
        }
        
        .fee-type-breakdown {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        
        .fee-note {
            margin-bottom: 0;
            line-height: 1.8;
        }
        
        .signature-section {
            margin-top: 40px;
        }
        
        .signature-line {
            width: 140px;
            border-top: 1px solid #000;
            margin: 0 auto 5px auto;
        }
        
        .invoice-footer {
            margin-top: 60px;
            text-align: center;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px dashed #dee2e6;
            padding-top: 20px;
        }
        
        .container-a4 {
            position: relative;
        }
        
        @media print {
            .a4-invoice {
                display: block !important;
            }
            .invoice-title h4 {
                background-color: #0d6efd !important;
                color: white !important;
            }
        }
        
        /* Print-specific styles */
        @media print {
            body * {
                visibility: hidden;
            }
            
            .a4-invoice, .a4-invoice * {
                visibility: visible;
            }
            
            .a4-invoice {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-after: always;
            }
        }
    </style>
</head>
<body>
    <div class="container my-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3">ফি রিপোর্টস</h1>
            <a href="dashboard.php" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>ড্যাশবোর্ডে ফিরে যান
            </a>
        </div>
        
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs">
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'search' ? 'active' : '' ?>" href="fee_reports.php?tab=search">
                            <i class="fas fa-search me-2"></i>শিক্ষার্থী অনুসন্ধান
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'all-students' ? 'active' : '' ?>" href="fee_reports.php?tab=all-students">
                            <i class="fas fa-users me-2"></i>সকল ছাত্র/ছাত্রী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'fee-summary' ? 'active' : '' ?>" href="fee_reports.php?tab=fee-summary">
                            <i class="fas fa-chart-pie me-2"></i>ফি সামারি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'summary-invoice' ? 'active' : '' ?>" href="fee_reports.php?tab=summary-invoice">
                            <i class="fas fa-file-invoice me-2"></i>ইনভয়েস প্রিন্ট
                        </a>
                    </li>
                    <?php if ($studentId > 0): ?>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'student-report' ? 'active' : '' ?>" href="fee_reports.php?tab=student-report&student_id=<?= $studentId ?>">
                            <i class="fas fa-file-alt me-2"></i>ফি রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?= $currentTab === 'student-receipt' ? 'active' : '' ?>" href="fee_reports.php?tab=student-receipt&student_id=<?= $studentId ?>">
                            <i class="fas fa-receipt me-2"></i>পেমেন্ট রসিদ
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="card-body tab-content">
                <!-- Search Tab -->
                <?php if ($currentTab === 'search'): ?>
                <div class="tab-pane fade show active">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">শিক্ষার্থী অনুসন্ধান ফিল্টার</h5>
                        </div>
                        <div class="card-body">
                            <form action="fee_reports.php" method="GET" class="row g-3">
                                <input type="hidden" name="tab" value="search">
                                <input type="hidden" name="search" value="1">
                                
                                <div class="col-md-3">
                                    <label for="session_id" class="form-label">সেশন</label>
                                    <select name="session_id" id="session_id" class="form-select">
                                        <option value="">সকল সেশন</option>
                                        <?php foreach ($sessions as $session): ?>
                                        <option value="<?= $session['id'] ?>" <?= isset($_GET['session_id']) && $_GET['session_id'] == $session['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($session['session_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="class_id" class="form-label">শ্রেণী</label>
                                    <select name="class_id" id="class_id" class="form-select">
                                        <option value="">সকল শ্রেণী</option>
                                        <?php foreach ($classes as $class): ?>
                                        <option value="<?= $class['id'] ?>" <?= isset($_GET['class_id']) && $_GET['class_id'] == $class['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class['class_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="department_id" class="form-label">বিভাগ</label>
                                    <select name="department_id" id="department_id" class="form-select">
                                        <option value="">সকল বিভাগ</option>
                                        <?php foreach ($departments as $department): ?>
                                        <option value="<?= $department['id'] ?>" <?= isset($_GET['department_id']) && $_GET['department_id'] == $department['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($department['department_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="student_id_query" class="form-label">শিক্ষার্থী আইডি</label>
                                    <input type="text" class="form-control" id="student_id_query" name="student_id_query" 
                                        value="<?= isset($_GET['student_id_query']) ? htmlspecialchars($_GET['student_id_query']) : '' ?>">
                                </div>
                                
                                <div class="col-md-6">
                                    <label for="student_name" class="form-label">শিক্ষার্থীর নাম</label>
                                    <input type="text" class="form-control" id="student_name" name="student_name" 
                                        value="<?= isset($_GET['student_name']) ? htmlspecialchars($_GET['student_name']) : '' ?>">
                                </div>
                                
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>অনুসন্ধান করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <?php if (!empty($students)): ?>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">অনুসন্ধান ফলাফল</h5>
                            <span class="badge bg-info"><?= count($students) ?> জন শিক্ষার্থী পাওয়া গেছে</span>
                        </div>
                        <div class="card-body">
                            <div class="row row-cols-1 row-cols-md-3 g-4">
                                <?php foreach ($students as $student): ?>
                                <div class="col">
                                    <div class="card h-100 student-card">
                                        <div class="card-body">
                                            <h5 class="card-title"><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></h5>
                                            <p class="card-text">
                                                <strong>আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?><br>
                                                <strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?><br>
                                                <strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?><br>
                                                <strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?>
                                            </p>
                                        </div>
                                        <div class="card-footer bg-transparent border-top-0">
                                            <div class="d-flex justify-content-between">
                                                <a href="fee_reports.php?tab=student-report&student_id=<?= $student['id'] ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-file-alt me-2"></i>ফি রিপোর্ট
                                                </a>
                                                <a href="fee_reports.php?tab=student-receipt&student_id=<?= $student['id'] ?>" class="btn btn-sm btn-outline-success">
                                                    <i class="fas fa-receipt me-2"></i>পেমেন্ট রসিদ
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php elseif (isset($_GET['search']) && $_GET['search'] === '1'): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>কোন শিক্ষার্থী পাওয়া যায়নি। অনুগ্রহ করে অন্য ফিল্টার ব্যবহার করে আবার চেষ্টা করুন।
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- All Students Tab -->
                <?php if ($currentTab === 'all-students'): ?>
                <div class="tab-pane fade show active">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">সকল ছাত্র/ছাত্রীর ফি রিপোর্ট ফিল্টার</h5>
                        </div>
                        <div class="card-body">
                            <form action="fee_reports.php" method="GET" class="row g-3">
                                <input type="hidden" name="tab" value="all-students">
                                
                                <div class="col-md-4">
                                    <label for="all_session_id" class="form-label">সেশন</label>
                                    <select name="all_session_id" id="all_session_id" class="form-select">
                                        <option value="">সকল সেশন</option>
                                        <?php foreach ($sessions as $session): ?>
                                        <option value="<?= $session['id'] ?>" <?= isset($_GET['all_session_id']) && $_GET['all_session_id'] == $session['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($session['session_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="all_class_id" class="form-label">শ্রেণী</label>
                                    <select name="all_class_id" id="all_class_id" class="form-select">
                                        <option value="">সকল শ্রেণী</option>
                                        <?php foreach ($classes as $class): ?>
                                        <option value="<?= $class['id'] ?>" <?= isset($_GET['all_class_id']) && $_GET['all_class_id'] == $class['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class['class_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="all_department_id" class="form-label">বিভাগ</label>
                                    <select name="all_department_id" id="all_department_id" class="form-select">
                                        <option value="">সকল বিভাগ</option>
                                        <?php foreach ($departments as $department): ?>
                                        <option value="<?= $department['id'] ?>" <?= isset($_GET['all_department_id']) && $_GET['all_department_id'] == $department['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($department['department_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                                    </button>
                                    <button type="button" class="btn btn-success ms-2" onclick="window.print()">
                                        <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <?php if (isset($allStudents)): ?>
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">ফি সামারি</h5>
                            <span class="badge bg-info"><?= count($allStudents) ?> জন শিক্ষার্থী</span>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="summary-box total-fee">
                                        <h5 class="summary-title">মোট ফি</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalFees, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box paid-fee">
                                        <h5 class="summary-title">মোট পরিশোধিত</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box due-fee">
                                        <h5 class="summary-title">মোট বকেয়া</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="table-responsive mt-4">
                                <table class="table table-bordered table-striped">
                                    <thead class="table-light">
                                        <tr>
                                            <th width="5%">ক্রম</th>
                                            <th width="10%">আইডি</th>
                                            <th width="20%">নাম</th>
                                            <th width="10%">সেশন</th>
                                            <th width="10%">শ্রেণী</th>
                                            <th width="10%">বিভাগ</th>
                                            <th width="10%">মোট ফি</th>
                                            <th width="10%">পরিশোধিত</th>
                                            <th width="10%">বকেয়া</th>
                                            <th width="5%" class="no-print">বিস্তারিত</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $i = 1;
                                        foreach ($allStudents as $studentData):
                                            $paidPercent = $studentData['total_fees'] > 0 ? 
                                                ($studentData['total_paid'] / $studentData['total_fees'] * 100) : 0;
                                            
                                            $statusClass = '';
                                            if ($studentData['total_due'] <= 0 && $studentData['total_fees'] > 0) {
                                                $statusClass = 'table-success';
                                            } else if ($studentData['total_paid'] > 0) {
                                                $statusClass = 'table-warning';
                                            } else if ($studentData['total_due'] > 0) {
                                                $statusClass = 'table-danger';
                                            }
                                        ?>
                                        <tr class="<?= $statusClass ?>">
                                            <td><?= convertToBengaliNumber($i++) ?></td>
                                            <td><?= htmlspecialchars($studentData['student_id']) ?></td>
                                            <td><?= htmlspecialchars($studentData['first_name'] . ' ' . $studentData['last_name']) ?></td>
                                            <td><?= htmlspecialchars($studentData['session_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($studentData['class_name'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($studentData['department_name'] ?? 'N/A') ?></td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($studentData['total_fees'], 2)) ?> ৳</td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($studentData['total_paid'], 2)) ?> ৳</td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($studentData['total_due'], 2)) ?> ৳</td>
                                            <td class="text-center no-print">
                                                <a href="fee_reports.php?tab=student-report&student_id=<?= $studentData['id'] ?>" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="6" class="text-end">মোট:</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalFees, 2)) ?> ৳</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</th>
                                            <th class="no-print"></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- A4 Report for Printing -->
                    <div class="a4-report">
                        <div class="report-header">
                            <?php 
                            // Get school settings if available
                            $schoolSettings = [];
                            $schoolQuery = "SELECT * FROM school_settings LIMIT 1";
                            $schoolResult = $conn->query($schoolQuery);
                            if ($schoolResult && $schoolResult->num_rows > 0) {
                                $schoolSettings = $schoolResult->fetch_assoc();
                            }
                            
                            // School name and address from settings or fallback to defaults
                            $schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'আজমগঞ্জ ফাজিল মাদ্রাসা';
                            $schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'আজমগঞ্জ, সিলেট';
                            $schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
                            $schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
                            $schoolLogo = isset($schoolSettings['school_logo']) ? $schoolSettings['school_logo'] : '';
                            ?>
                            
                            <?php if (!empty($schoolLogo)): ?>
                            <div class="text-center mb-2">
                                <img src="<?= htmlspecialchars($schoolLogo) ?>" alt="School Logo" class="school-logo">
                            </div>
                            <?php endif; ?>
                            
                            <h2 class="text-center mb-1"><?= htmlspecialchars($schoolName) ?></h2>
                            <p class="text-center mb-1"><?= htmlspecialchars($schoolAddress) ?></p>
                            
                            <?php if (!empty($schoolPhone) || !empty($schoolEmail)): ?>
                            <p class="text-center mb-2">
                                <?php if (!empty($schoolPhone)): ?>
                                <span class="me-3"><i class="fas fa-phone-alt me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($schoolEmail)): ?>
                                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            
                            <div class="report-title">
                                <h4>সকল ছাত্র/ছাত্রীর ফি রিপোর্ট</h4>
                            </div>
                        </div>
                        
                        <div class="filter-info">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>সেশন:</strong> <?= $allSessionId > 0 ? htmlspecialchars($sessions[array_search($allSessionId, array_column($sessions, 'id'))]['session_name']) : 'সকল সেশন' ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>শ্রেণী:</strong> <?= $allClassId > 0 ? htmlspecialchars($classes[array_search($allClassId, array_column($classes, 'id'))]['class_name']) : 'সকল শ্রেণী' ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fee-summary">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="summary-box total-fee">
                                        <h5 class="summary-title">মোট ফি</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalFees, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box paid-fee">
                                        <h5 class="summary-title">মোট পরিশোধিত</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box due-fee">
                                        <h5 class="summary-title">মোট বকেয়া</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fee-details mt-4">
                            <h5 class="section-title">বিস্তারিত বিবরণ</h5>
                            <table class="fee-table">
                                <thead>
                                    <tr>
                                        <th>ক্রম</th>
                                        <th>আইডি</th>
                                        <th>নাম</th>
                                        <th>সেশন</th>
                                        <th>শ্রেণী</th>
                                        <th>বিভাগ</th>
                                        <th>মোট ফি</th>
                                        <th>পরিশোধিত</th>
                                        <th>বকেয়া</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $i = 1;
                                    foreach ($allStudents as $studentData): 
                                    ?>
                                    <tr>
                                        <td><?= convertToBengaliNumber($i++) ?></td>
                                        <td><?= htmlspecialchars($studentData['student_id']) ?></td>
                                        <td><?= htmlspecialchars($studentData['first_name'] . ' ' . $studentData['last_name']) ?></td>
                                        <td><?= htmlspecialchars($studentData['session_name'] ?? 'N/A') ?></td>
                                        <td><?= htmlspecialchars($studentData['class_name'] ?? 'N/A') ?></td>
                                        <td><?= htmlspecialchars($studentData['department_name'] ?? 'N/A') ?></td>
                                        <td><?= convertToBengaliNumber(number_format($studentData['total_fees'], 2)) ?> ৳</td>
                                        <td><?= convertToBengaliNumber(number_format($studentData['total_paid'], 2)) ?> ৳</td>
                                        <td><?= convertToBengaliNumber(number_format($studentData['total_due'], 2)) ?> ৳</td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="6" class="text-end">মোট:</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalFees, 2)) ?> ৳</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <div class="report-footer">
                            <p>এই রিপোর্টটি <?= formatDateToBengali(date('Y-m-d')) ?> তারিখে জেনারেট করা হয়েছে।</p>
                            <p class="copyright">&copy; <?= date('Y') ?> <?= htmlspecialchars($schoolName) ?></p>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- Fee Summary Tab -->
                <?php if ($currentTab === 'fee-summary'): ?>
                <div class="tab-pane fade show active">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ফি সামারি রিপোর্ট ফিল্টার</h5>
                        </div>
                        <div class="card-body">
                            <form action="fee_reports.php" method="GET" class="row g-3">
                                <input type="hidden" name="tab" value="fee-summary">
                                
                                <div class="col-md-5">
                                    <label for="summary_session_id" class="form-label">সেশন</label>
                                    <select name="summary_session_id" id="summary_session_id" class="form-select">
                                        <option value="">সকল সেশন</option>
                                        <?php foreach ($sessions as $session): ?>
                                        <option value="<?= $session['id'] ?>" <?= isset($_GET['summary_session_id']) && $_GET['summary_session_id'] == $session['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($session['session_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-5">
                                    <label for="summary_class_id" class="form-label">শ্রেণী</label>
                                    <select name="summary_class_id" id="summary_class_id" class="form-select">
                                        <option value="">সকল শ্রেণী</option>
                                        <?php foreach ($classes as $class): ?>
                                        <option value="<?= $class['id'] ?>" <?= isset($_GET['summary_class_id']) && $_GET['summary_class_id'] == $class['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class['class_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-2 d-flex align-items-end">
                                    <div class="d-grid w-100">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-filter me-2"></i>ফিল্টার
                                        </button>
                                    </div>
                                </div>
                                
                                <?php if (isset($summaryData)): ?>
                                <div class="col-12 text-end mt-3">
                                    <button type="button" class="btn btn-success" onclick="window.print()">
                                        <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                    </button>
                                </div>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    
                    <?php if (isset($summaryData)): ?>
                    <!-- Regular View -->
                    <div class="d-print-none">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">ফি সংগ্রহ সামারি</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="summary-box total-fee bg-light">
                                            <h5 class="summary-title">মোট শিক্ষার্থী</h5>
                                            <div class="summary-amount"><?= convertToBengaliNumber($grandTotalStudents) ?> জন</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="summary-box total-fee">
                                            <h5 class="summary-title">মোট ফি</h5>
                                            <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalAmount, 2)) ?> ৳</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="summary-box paid-fee">
                                            <h5 class="summary-title">মোট পরিশোধিত</h5>
                                            <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="summary-box due-fee">
                                            <h5 class="summary-title">মোট বকেয়া</h5>
                                            <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="progress mb-4" style="height: 25px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: <?= $collectionPercent ?>%">
                                        <?= convertToBengaliNumber(number_format($collectionPercent, 1)) ?>%
                                    </div>
                                </div>
                                
                                <h5 class="mb-3">শ্রেণী অনুযায়ী সামারি</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>শ্রেণী</th>
                                                <th>শিক্ষার্থী সংখ্যা</th>
                                                <th>মোট ফি</th>
                                                <th>পরিশোধিত</th>
                                                <th>বকেয়া</th>
                                                <th>আদায় (%)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($summaryData as $className => $classData): 
                                                $classCollection = $classData['total_amount'] > 0 ? 
                                                    ($classData['total_paid'] / $classData['total_amount'] * 100) : 0;
                                            ?>
                                            <tr>
                                                <td><?= htmlspecialchars($className) ?></td>
                                                <td class="text-center"><?= convertToBengaliNumber($classData['student_count']) ?></td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_amount'], 2)) ?> ৳</td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_paid'], 2)) ?> ৳</td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_due'], 2)) ?> ৳</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $classCollection ?>%">
                                                            <?= convertToBengaliNumber(number_format($classCollection, 1)) ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <h5 class="mb-3 mt-5">ফি প্রকার অনুযায়ী সামারি</h5>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ফি প্রকার</th>
                                                <th>মোট ফি</th>
                                                <th>পরিশোধিত</th>
                                                <th>বকেয়া</th>
                                                <th>আদায় (%)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($feeTypeSummaries as $feeType => $feeData): 
                                                $feeCollection = $feeData['total_amount'] > 0 ? 
                                                    ($feeData['total_paid'] / $feeData['total_amount'] * 100) : 0;
                                            ?>
                                            <tr>
                                                <td><?= htmlspecialchars($feeType) ?></td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_amount'], 2)) ?> ৳</td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_paid'], 2)) ?> ৳</td>
                                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_due'], 2)) ?> ৳</td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $feeCollection ?>%">
                                                            <?= convertToBengaliNumber(number_format($feeCollection, 1)) ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- A4 Print Layout for Fee Summary -->
                    <div class="a4-report fee-summary-report">
                        <div class="report-header">
                            <?php 
                            // Get school settings if available
                            $schoolSettings = [];
                            $schoolQuery = "SELECT * FROM school_settings LIMIT 1";
                            $schoolResult = $conn->query($schoolQuery);
                            if ($schoolResult && $schoolResult->num_rows > 0) {
                                $schoolSettings = $schoolResult->fetch_assoc();
                            }
                            
                            // School name and address from settings or fallback to defaults
                            $schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'আজমগঞ্জ ফাজিল মাদ্রাসা';
                            $schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'আজমগঞ্জ, সিলেট';
                            $schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
                            $schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
                            $schoolLogo = isset($schoolSettings['school_logo']) ? $schoolSettings['school_logo'] : '';
                            ?>
                            
                            <?php if (!empty($schoolLogo)): ?>
                            <div class="text-center mb-2">
                                <img src="<?= htmlspecialchars($schoolLogo) ?>" alt="School Logo" class="school-logo">
                            </div>
                            <?php endif; ?>
                            
                            <h2 class="text-center mb-1"><?= htmlspecialchars($schoolName) ?></h2>
                            <p class="text-center mb-1"><?= htmlspecialchars($schoolAddress) ?></p>
                            
                            <?php if (!empty($schoolPhone) || !empty($schoolEmail)): ?>
                            <p class="text-center mb-2">
                                <?php if (!empty($schoolPhone)): ?>
                                <span class="me-3"><i class="fas fa-phone-alt me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($schoolEmail)): ?>
                                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            
                            <div class="report-title">
                                <h4>ফি সংগ্রহ সামারি রিপোর্ট</h4>
                            </div>
                        </div>
                        
                        <div class="filter-info">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <strong>সেশন:</strong> <?= $summarySessionId > 0 ? htmlspecialchars($sessions[array_search($summarySessionId, array_column($sessions, 'id'))]['session_name']) : 'সকল সেশন' ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>শ্রেণী:</strong> <?= $summaryClassId > 0 ? htmlspecialchars($classes[array_search($summaryClassId, array_column($classes, 'id'))]['class_name']) : 'সকল শ্রেণী' ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="summary-boxes">
                            <div class="row">
                                <div class="col-3">
                                    <div class="summary-box bg-light border">
                                        <h5 class="summary-title">মোট শিক্ষার্থী</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber($grandTotalStudents) ?> জন</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="summary-box total-fee">
                                        <h5 class="summary-title">মোট ফি</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalAmount, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="summary-box paid-fee">
                                        <h5 class="summary-title">মোট পরিশোধিত</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="summary-box due-fee">
                                        <h5 class="summary-title">মোট বকেয়া</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="collection-progress mb-4 mt-3">
                            <h5 class="section-title">আদায় হার</h5>
                            <div class="progress" style="height: 30px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?= $collectionPercent ?>%">
                                    <?= convertToBengaliNumber(number_format($collectionPercent, 1)) ?>%
                                </div>
                            </div>
                        </div>
                        
                        <div class="class-summary mb-5">
                            <h5 class="section-title">শ্রেণী অনুযায়ী সামারি</h5>
                            <table class="fee-table">
                                <thead>
                                    <tr>
                                        <th width="25%">শ্রেণী</th>
                                        <th width="15%">শিক্ষার্থী সংখ্যা</th>
                                        <th width="20%">মোট ফি</th>
                                        <th width="20%">পরিশোধিত</th>
                                        <th width="20%">বকেয়া</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($summaryData as $className => $classData): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($className) ?></td>
                                        <td class="text-center"><?= convertToBengaliNumber($classData['student_count']) ?></td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_amount'], 2)) ?> ৳</td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_paid'], 2)) ?> ৳</td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($classData['total_due'], 2)) ?> ৳</td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                <div class="fee-type-summary">
                    <h5 class="section-title">ফি প্রকার অনুযায়ী সামারি</h5>
                    <table class="fee-table">
                        <thead>
                            <tr>
                                <th width="40%">ফি প্রকার</th>
                                <th width="20%">মোট ফি</th>
                                <th width="20%">পরিশোধিত</th>
                                <th width="20%">বকেয়া</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($feeTypeSummaries as $feeType => $feeData): ?>
                            <tr>
                                <td><?= htmlspecialchars($feeType) ?></td>
                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_amount'], 2)) ?> ৳</td>
                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_paid'], 2)) ?> ৳</td>
                                <td class="text-end"><?= convertToBengaliNumber(number_format($feeData['total_due'], 2)) ?> ৳</td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr>
                                <th class="text-end">মোট:</th>
                                <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalAmount, 2)) ?> ৳</th>
                                <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalPaid, 2)) ?> ৳</th>
                                <th class="text-end"><?= convertToBengaliNumber(number_format($grandTotalDue, 2)) ?> ৳</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <div class="report-footer">
                    <p>এই রিপোর্টটি <?= formatDateToBengali(date('Y-m-d')) ?> তারিখে জেনারেট করা হয়েছে।</p>
                    <p class="copyright">&copy; <?= date('Y') ?> <?= htmlspecialchars($schoolName) ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
                <?php endif; ?>
                
                <!-- Summary Invoice Print Tab -->
                <?php if ($currentTab === 'summary-invoice'): ?>
                <div class="tab-pane fade show active">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">সামারি ইনভয়েস প্রিন্ট</h5>
                        </div>
                        <div class="card-body">
                            <form action="fee_reports.php" method="GET" class="row g-3" id="invoiceForm">
                                <input type="hidden" name="tab" value="summary-invoice">
                                
                                <div class="col-md-3">
                                    <label for="invoice_class_id" class="form-label">শ্রেণী</label>
                                    <select name="invoice_class_id" id="invoice_class_id" class="form-select">
                                        <option value="">শ্রেণী বাছাই করুন</option>
                                        <?php foreach ($classes as $class): ?>
                                        <option value="<?= $class['id'] ?>" <?= isset($_GET['invoice_class_id']) && $_GET['invoice_class_id'] == $class['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($class['class_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="invoice_session_id" class="form-label">সেশন</label>
                                    <select name="invoice_session_id" id="invoice_session_id" class="form-select">
                                        <option value="">সেশন বাছাই করুন</option>
                                        <?php foreach ($sessions as $session): ?>
                                        <option value="<?= $session['id'] ?>" <?= isset($_GET['invoice_session_id']) && $_GET['invoice_session_id'] == $session['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($session['session_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="invoice_fee_type" class="form-label">ফি প্রকার</label>
                                    <select name="invoice_fee_type" id="invoice_fee_type" class="form-select">
                                        <option value="">সব ফি</option>
                                        <?php 
                                        // Fetch unique fee types
                                        $feeTypesQuery = "SELECT DISTINCT fee_type FROM fees ORDER BY fee_type";
                                        $feeTypesResult = $conn->query($feeTypesQuery);
                                        $feeTypesList = [];
                                        while ($feeType = $feeTypesResult->fetch_assoc()) {
                                            $feeTypesList[] = $feeType['fee_type'];
                                        }
                                        foreach ($feeTypesList as $feeType): 
                                        ?>
                                        <option value="<?= htmlspecialchars($feeType) ?>" <?= isset($_GET['invoice_fee_type']) && $_GET['invoice_fee_type'] == $feeType ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($feeType) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label for="invoice_date" class="form-label">তারিখ</label>
                                    <input type="date" class="form-control" id="invoice_date" name="invoice_date" value="<?= isset($_GET['invoice_date']) ? $_GET['invoice_date'] : date('Y-m-d') ?>">
                                </div>
                                
                                <div class="col-12 text-end mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>অনুসন্ধান করুন
                                    </button>
                                    <?php if (isset($_GET['invoice_class_id']) || isset($_GET['invoice_session_id'])): ?>
                                    <button type="button" class="btn btn-success ms-2" id="printInvoiceBtn">
                                        <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <?php 
                    // Process invoice data if form submitted
                    if (isset($_GET['invoice_class_id']) || isset($_GET['invoice_session_id'])):
                        $invoiceClassId = isset($_GET['invoice_class_id']) ? intval($_GET['invoice_class_id']) : 0;
                        $invoiceSessionId = isset($_GET['invoice_session_id']) ? intval($_GET['invoice_session_id']) : 0;
                        $invoiceFeeType = isset($_GET['invoice_fee_type']) ? $_GET['invoice_fee_type'] : '';
                        $invoiceDate = isset($_GET['invoice_date']) ? $_GET['invoice_date'] : date('Y-m-d');
                        
                        // Query to get invoice data
                        $invoiceDataQuery = "SELECT s.id, s.student_id, s.first_name, s.last_name, 
                                           c.class_name, d.department_name, ss.session_name,
                                           f.fee_type, f.amount, f.due_date, f.paid
                                    FROM students s
                                    JOIN fees f ON s.id = f.student_id
                                    LEFT JOIN classes c ON s.class_id = c.id
                                    LEFT JOIN departments d ON s.department_id = d.id
                                    LEFT JOIN sessions ss ON s.session_id = ss.id
                                    WHERE 1=1";
                        
                        $invoiceParams = [];
                        $invoiceTypes = "";
                        
                        if ($invoiceClassId > 0) {
                            $invoiceDataQuery .= " AND s.class_id = ?";
                            $invoiceParams[] = $invoiceClassId;
                            $invoiceTypes .= "i";
                        }
                        
                        if ($invoiceSessionId > 0) {
                            $invoiceDataQuery .= " AND s.session_id = ?";
                            $invoiceParams[] = $invoiceSessionId;
                            $invoiceTypes .= "i";
                        }
                        
                        if (!empty($invoiceFeeType)) {
                            $invoiceDataQuery .= " AND f.fee_type = ?";
                            $invoiceParams[] = $invoiceFeeType;
                            $invoiceTypes .= "s";
                        }
                        
                        $invoiceDataQuery .= " ORDER BY s.first_name, s.last_name, f.fee_type";
                        
                        $stmt = $conn->prepare($invoiceDataQuery);
                        if (!empty($invoiceParams)) {
                            $stmt->bind_param($invoiceTypes, ...$invoiceParams);
                        }
                        $stmt->execute();
                        $invoiceResult = $stmt->get_result();
                        
                        // Process data for invoice
                        $invoiceData = [];
                        $totalFees = 0;
                        $totalPaid = 0;
                        $totalDue = 0;
                        
                        while ($row = $invoiceResult->fetch_assoc()) {
                            if (!isset($invoiceData[$row['student_id']])) {
                                $invoiceData[$row['student_id']] = [
                                    'id' => $row['id'],
                                    'student_id' => $row['student_id'],
                                    'name' => $row['first_name'] . ' ' . $row['last_name'],
                                    'class' => $row['class_name'],
                                    'department' => $row['department_name'],
                                    'session' => $row['session_name'],
                                    'fees' => [],
                                    'total_amount' => 0,
                                    'total_paid' => 0,
                                    'total_due' => 0
                                ];
                            }
                            
                            $due = $row['amount'] - $row['paid'];
                            
                            $invoiceData[$row['student_id']]['fees'][] = [
                                'fee_type' => $row['fee_type'],
                                'amount' => $row['amount'],
                                'paid' => $row['paid'],
                                'due' => $due,
                                'due_date' => $row['due_date']
                            ];
                            
                            $invoiceData[$row['student_id']]['total_amount'] += $row['amount'];
                            $invoiceData[$row['student_id']]['total_paid'] += $row['paid'];
                            $invoiceData[$row['student_id']]['total_due'] += $due;
                            
                            $totalFees += $row['amount'];
                            $totalPaid += $row['paid'];
                            $totalDue += $due;
                        }
                        
                        // Get school information
                        $schoolSettings = [];
                        $schoolQuery = "SELECT * FROM school_settings LIMIT 1";
                        $schoolResult = $conn->query($schoolQuery);
                        if ($schoolResult && $schoolResult->num_rows > 0) {
                            $schoolSettings = $schoolResult->fetch_assoc();
                        }
                        
                        $schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'আজমগঞ্জ ফাজিল মাদ্রাসা';
                        $schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'আজমগঞ্জ, সিলেট';
                        $schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
                        $schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
                        $schoolLogo = isset($schoolSettings['school_logo']) ? $schoolSettings['school_logo'] : '';
                        
                        // Generate invoice number
                        $invoiceNo = 'INV-' . date('Ym', strtotime($invoiceDate)) . '-' . sprintf('%04d', rand(1, 9999));
                    ?>
                    
                    <!-- Invoice Preview Section -->
                    <div class="card d-print-none">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                সামারি ইনভয়েস প্রিভিউ
                                <span class="badge bg-primary ms-2"><?= count($invoiceData) ?> জন শিক্ষার্থী</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>ক্রম</th>
                                            <th>আইডি</th>
                                            <th>নাম</th>
                                            <th>শ্রেণী</th>
                                            <th>সেশন</th>
                                            <th>মোট ফি</th>
                                            <th>পরিশোধিত</th>
                                            <th>বকেয়া</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $i = 1;
                                        foreach ($invoiceData as $studentId => $student): 
                                        ?>
                                        <tr>
                                            <td><?= convertToBengaliNumber($i++) ?></td>
                                            <td><?= htmlspecialchars($student['student_id']) ?></td>
                                            <td><?= htmlspecialchars($student['name']) ?></td>
                                            <td><?= htmlspecialchars($student['class']) ?></td>
                                            <td><?= htmlspecialchars($student['session']) ?></td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_amount'], 2)) ?> ৳</td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_paid'], 2)) ?> ৳</td>
                                            <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_due'], 2)) ?> ৳</td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="5" class="text-end">মোট:</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($totalFees, 2)) ?> ৳</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($totalPaid, 2)) ?> ৳</th>
                                            <th class="text-end"><?= convertToBengaliNumber(number_format($totalDue, 2)) ?> ৳</th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- A4 Invoice Print Layout -->
                    <div class="container-a4 a4-invoice" id="invoicePrintSection">
                        <!-- Invoice Header -->
                        <div class="invoice-header">
                            <?php if (!empty($schoolLogo)): ?>
                            <div class="text-center mb-2">
                                <img src="<?= htmlspecialchars($schoolLogo) ?>" alt="School Logo" class="school-logo">
                            </div>
                            <?php endif; ?>
                            
                            <h2 class="text-center mb-1"><?= htmlspecialchars($schoolName) ?></h2>
                            <p class="text-center mb-1"><?= htmlspecialchars($schoolAddress) ?></p>
                            
                            <?php if (!empty($schoolPhone) || !empty($schoolEmail)): ?>
                            <p class="text-center mb-2">
                                <?php if (!empty($schoolPhone)): ?>
                                <span class="me-3"><i class="fas fa-phone-alt me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($schoolEmail)): ?>
                                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            
                            <div class="invoice-title">
                                <h4>সামারি ফি ইনভয়েস</h4>
                            </div>
                        </div>
                        
                        <!-- Invoice Meta Information -->
                        <div class="invoice-meta">
                            <div class="row">
                                <div class="col-6">
                                    <p><strong>ইনভয়েস নং:</strong> <?= htmlspecialchars($invoiceNo) ?></p>
                                    <p><strong>তারিখ:</strong> <?= formatDateToBengali($invoiceDate) ?></p>
                                </div>
                                <div class="col-6 text-end">
                                    <p>
                                        <strong>শ্রেণী:</strong> 
                                        <?= $invoiceClassId > 0 ? htmlspecialchars($classes[array_search($invoiceClassId, array_column($classes, 'id'))]['class_name']) : 'সকল শ্রেণী' ?>
                                    </p>
                                    <p>
                                        <strong>সেশন:</strong> 
                                        <?= $invoiceSessionId > 0 ? htmlspecialchars($sessions[array_search($invoiceSessionId, array_column($sessions, 'id'))]['session_name']) : 'সকল সেশন' ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fee Summary Information -->
                        <div class="invoice-summary-box">
                            <div class="row mt-3 mb-4">
                                <div class="col-4">
                                    <div class="summary-box total-fee">
                                        <h5 class="summary-title">মোট ফি</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalFees, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="summary-box paid-fee">
                                        <h5 class="summary-title">মোট পরিশোধিত</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalPaid, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="summary-box due-fee">
                                        <h5 class="summary-title">মোট বকেয়া</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalDue, 2)) ?> ৳</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Invoice Items Table -->
                        <div class="invoice-detail">
                            <h5 class="section-title mb-3">শিক্ষার্থীদের ফি বিবরণ</h5>
                            <table class="fee-table">
                                <thead>
                                    <tr>
                                        <th width="5%">ক্রম</th>
                                        <th width="10%">আইডি</th>
                                        <th width="25%">নাম</th>
                                        <th width="15%">শ্রেণী</th>
                                        <th width="15%">সেশন</th>
                                        <th width="10%">মোট ফি</th>
                                        <th width="10%">পরিশোধিত</th>
                                        <th width="10%">বকেয়া</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $i = 1;
                                    foreach ($invoiceData as $studentId => $student):
                                        $collectionPercent = $student['total_amount'] > 0 ? 
                                            ($student['total_paid'] / $student['total_amount'] * 100) : 0;
                                    ?>
                                    <tr>
                                        <td><?= convertToBengaliNumber($i++) ?></td>
                                        <td><?= htmlspecialchars($student['student_id']) ?></td>
                                        <td><?= htmlspecialchars($student['name']) ?></td>
                                        <td><?= htmlspecialchars($student['class']) ?></td>
                                        <td><?= htmlspecialchars($student['session']) ?></td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_amount'], 2)) ?> ৳</td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_paid'], 2)) ?> ৳</td>
                                        <td class="text-end"><?= convertToBengaliNumber(number_format($student['total_due'], 2)) ?> ৳</td>
                                    </tr>
                                    <?php if (!empty($invoiceFeeType) && count($student['fees']) > 0): ?>
                                    <tr>
                                        <td colspan="8" class="fee-details-section">
                                            <div class="fee-details-inner">
                                                <?php foreach($student['fees'] as $fee): ?>
                                                <?php if ($invoiceFeeType == '' || $fee['fee_type'] == $invoiceFeeType): ?>
                                                <div class="fee-detail-item">
                                                    <span class="fee-type"><?= htmlspecialchars($fee['fee_type']) ?></span>
                                                    <span class="fee-due-date">তারিখ: <?= formatDateToBengali($fee['due_date']) ?></span>
                                                    <span class="fee-amount">
                                                        ফি: <?= convertToBengaliNumber(number_format($fee['amount'], 2)) ?> ৳ | 
                                                        পরিশোধিত: <?= convertToBengaliNumber(number_format($fee['paid'], 2)) ?> ৳ | 
                                                        বকেয়া: <?= convertToBengaliNumber(number_format($fee['due'], 2)) ?> ৳
                                                    </span>
                                                </div>
                                                <?php endif; ?>
                                                <?php endforeach; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endif; ?>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="5" class="text-end">মোট:</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($totalFees, 2)) ?> ৳</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($totalPaid, 2)) ?> ৳</th>
                                        <th class="text-end"><?= convertToBengaliNumber(number_format($totalDue, 2)) ?> ৳</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <!-- Fee Type Breakdown (if specific fee type selected) -->
                        <?php if (!empty($invoiceFeeType)): ?>
                        <div class="fee-type-breakdown mt-4 mb-4">
                            <h5 class="section-title mb-3"><?= htmlspecialchars($invoiceFeeType) ?> ফি সংক্ষিপ্ত বিবরণ</h5>
                            <p class="fee-note">
                                ফি প্রকার: <?= htmlspecialchars($invoiceFeeType) ?><br>
                                মোট শিক্ষার্থী: <?= convertToBengaliNumber(count($invoiceData)) ?> জন<br>
                                মোট ফি: <?= convertToBengaliNumber(number_format($totalFees, 2)) ?> ৳<br>
                                মোট আদায়: <?= convertToBengaliNumber(number_format($totalPaid, 2)) ?> ৳ 
                                (<?= convertToBengaliNumber(number_format($totalFees > 0 ? ($totalPaid / $totalFees * 100) : 0, 1)) ?>%)
                            </p>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Note and Terms -->
                        <div class="invoice-note">
                            <h5 class="section-title mb-2">বিশেষ তথ্য</h5>
                            <p>
                                এই ইনভয়েসটি <?= formatDateToBengali($invoiceDate) ?> তারিখ পর্যন্ত ফি বকেয়া এবং পরিশোধের তথ্য প্রদর্শন করে।<br>
                                ফি পরিশোধ সম্পর্কিত যেকোনো প্রশ্ন অফিসে যোগাযোগ করুন।
                            </p>
                        </div>
                        
                        <!-- Signature Section -->
                        <div class="signature-section mt-5">
                            <div class="row">
                                <div class="col-4 text-center">
                                    <div class="signature-line"></div>
                                    <p>প্রস্তুতকারী</p>
                                </div>
                                <div class="col-4 text-center">
                                    <div class="signature-line"></div>
                                    <p>হিসাব রক্ষক</p>
                                </div>
                                <div class="col-4 text-center">
                                    <div class="signature-line"></div>
                                    <p>অধ্যক্ষ</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Invoice Footer -->
                        <div class="invoice-footer mt-5">
                            <p>এই ইনভয়েসটি <?= formatDateToBengali(date('Y-m-d')) ?> তারিখে জেনারেট করা হয়েছে।</p>
                            <p>&copy; <?= date('Y') ?> <?= htmlspecialchars($schoolName) ?></p>
                        </div>
                    </div>
                    
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <!-- Student Report Tab -->
                <?php if ($currentTab === 'student-report' && $student): ?>
                <div class="tab-pane fade show active">
                    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                        <h4><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?> - ফি রিপোর্ট</h4>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>প্রিন্ট করুন
                        </button>
                    </div>

                    <!-- A4 Report Design - Will show when printing -->
                    <div class="a4-report">
                        <div class="report-header">
                            <?php 
                            // Get school settings if available
                            $schoolSettings = [];
                            $schoolQuery = "SELECT * FROM school_settings LIMIT 1";
                            $schoolResult = $conn->query($schoolQuery);
                            if ($schoolResult && $schoolResult->num_rows > 0) {
                                $schoolSettings = $schoolResult->fetch_assoc();
                            }
                            
                            // School name and address from settings or fallback to defaults
                            $schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'আজমগঞ্জ ফাজিল মাদ্রাসা';
                            $schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'আজমগঞ্জ, সিলেট';
                            $schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
                            $schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
                            $schoolLogo = isset($schoolSettings['school_logo']) ? $schoolSettings['school_logo'] : '';
                            ?>
                            
                            <?php if (!empty($schoolLogo)): ?>
                            <div class="text-center mb-2">
                                <img src="<?= htmlspecialchars($schoolLogo) ?>" alt="School Logo" class="school-logo">
                            </div>
                            <?php endif; ?>
                            
                            <h2 class="text-center mb-1"><?= htmlspecialchars($schoolName) ?></h2>
                            <p class="text-center mb-1"><?= htmlspecialchars($schoolAddress) ?></p>
                            
                            <?php if (!empty($schoolPhone) || !empty($schoolEmail)): ?>
                            <p class="text-center mb-2">
                                <?php if (!empty($schoolPhone)): ?>
                                <span class="me-3"><i class="fas fa-phone-alt me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($schoolEmail)): ?>
                                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                                <?php endif; ?>
                            </p>
                            <?php endif; ?>
                            
                            <div class="report-title">
                                <h4>শিক্ষার্থী ফি রিপোর্ট</h4>
                            </div>
                        </div>
                        
                        <div class="student-info-box">
                            <div class="row">
                                <div class="col-6">
                                    <p><strong>শিক্ষার্থী আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                                    <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                                    <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                                </div>
                                <div class="col-6">
                                    <p><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                                    <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                                    <p><strong>তারিখ:</strong> <?= formatDateToBengali(date('Y-m-d')) ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <?php 
                        // Get all fees for the student
                        $feesQuery = "SELECT f.*, 
                                    (SELECT SUM(fp.amount) FROM fee_payments fp WHERE fp.fee_id = f.id) as paid_amount
                                    FROM fees f
                                    WHERE f.student_id = ?
                                    ORDER BY f.due_date DESC";
                        
                        $stmt = $conn->prepare($feesQuery);
                        $stmt->bind_param("i", $studentId);
                        $stmt->execute();
                        $feesResult = $stmt->get_result();
                        
                        $fees = [];
                        $totalAmount = 0;
                        $totalPaid = 0;
                        $totalDue = 0;
                        
                        while ($fee = $feesResult->fetch_assoc()) {
                            $paidAmount = $fee['paid_amount'] ?? 0;
                            $due = $fee['amount'] - $paidAmount;
                            
                            $totalAmount += $fee['amount'];
                            $totalPaid += $paidAmount;
                            $totalDue += $due;
                            
                            $fee['paid'] = $paidAmount;
                            $fee['due'] = $due;
                            $fees[] = $fee;
                        }
                        
                        // Group fees by type
                        $feesByType = [];
                        foreach ($fees as $fee) {
                            $feeType = $fee['fee_type'];
                            if (!isset($feesByType[$feeType])) {
                                $feesByType[$feeType] = [];
                            }
                            $feesByType[$feeType][] = $fee;
                        }
                        ?>
                        
                        <div class="fee-summary">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="summary-box total-fee">
                                        <h5 class="summary-title">মোট ফি</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalAmount, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box paid-fee">
                                        <h5 class="summary-title">পরিশোধিত</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalPaid, 2)) ?> ৳</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="summary-box due-fee">
                                        <h5 class="summary-title">বকেয়া</h5>
                                        <div class="summary-amount"><?= convertToBengaliNumber(number_format($totalDue, 2)) ?> ৳</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="fee-details">
                            <h5 class="section-title">ফি বিবরণ</h5>
                            <table class="fee-table">
                                <thead>
                                    <tr>
                                        <th>ক্রম</th>
                                        <th>ফি প্রকার</th>
                                        <th>বকেয়া তারিখ</th>
                                        <th>পরিমাণ (৳)</th>
                                        <th>পরিশোধিত (৳)</th>
                                        <th>বকেয়া (৳)</th>
                                        <th>অবস্থা</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $i = 1; 
                                    foreach ($fees as $fee): 
                                        $statusClass = 'status-due';
                                        $statusText = 'বকেয়া';
                                        
                                        if ($fee['paid'] >= $fee['amount']) {
                                            $statusClass = 'status-paid';
                                            $statusText = 'পরিশোধিত';
                                        } else if ($fee['paid'] > 0) {
                                            $statusClass = 'status-partial';
                                            $statusText = 'আংশিক';
                                        }
                                    ?>
                                    <tr>
                                        <td><?= convertToBengaliNumber($i++) ?></td>
                                        <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                                        <td><?= formatDateToBengali($fee['due_date']) ?></td>
                                        <td><?= convertToBengaliNumber(number_format($fee['amount'], 2)) ?></td>
                                        <td><?= convertToBengaliNumber(number_format($fee['paid'], 2)) ?></td>
                                        <td><?= convertToBengaliNumber(number_format($fee['due'], 2)) ?></td>
                                        <td><span class="status-badge <?= $statusClass ?>"><?= $statusText ?></span></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <th colspan="3" class="text-end">মোট:</th>
                                        <th><?= convertToBengaliNumber(number_format($totalAmount, 2)) ?></th>
                                        <th><?= convertToBengaliNumber(number_format($totalPaid, 2)) ?></th>
                                        <th><?= convertToBengaliNumber(number_format($totalDue, 2)) ?></th>
                                        <th></th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                        
                        <?php if (!empty($feesByType)): ?>
                        <div class="fee-by-type">
                            <h5 class="section-title">ফি প্রকার অনুযায়ী</h5>
                            
                            <?php foreach ($feesByType as $type => $typesFees): 
                                $typeTotal = array_sum(array_column($typesFees, 'amount'));
                                $typePaid = array_sum(array_column($typesFees, 'paid'));
                                $typeDue = $typeTotal - $typePaid;
                                
                                $percentPaid = ($typeTotal > 0) ? ($typePaid / $typeTotal * 100) : 0;
                            ?>
                            <div class="fee-type-item">
                                <div class="fee-type-header">
                                    <h6><?= htmlspecialchars($type) ?></h6>
                                    <div class="fee-type-amount">
                                        <span class="fee-type-total"><?= convertToBengaliNumber(number_format($typeTotal, 2)) ?> ৳</span>
                                        <span class="fee-type-paid"><?= convertToBengaliNumber(number_format($typePaid, 2)) ?> ৳ পরিশোধিত</span>
                                        <span class="fee-type-due"><?= convertToBengaliNumber(number_format($typeDue, 2)) ?> ৳ বকেয়া</span>
                                    </div>
                                </div>
                                <div class="progress fee-progress">
                                    <div class="progress-bar bg-success" style="width: <?= $percentPaid ?>%"></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="report-footer">
                            <p>এই রিপোর্টটি <?= formatDateToBengali(date('Y-m-d')) ?> তারিখে জেনারেট করা হয়েছে।</p>
                            <p class="copyright">&copy; <?= date('Y') ?> <?= htmlspecialchars($schoolName) ?></p>
                        </div>
                    </div>
                    
                    <!-- Old alert - will only show in regular view, not when printing -->
                    <div class="alert alert-info d-print-none">
                        <h5 class="alert-heading">শিক্ষার্থী তথ্য</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                                <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                                <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                                <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                                <p><strong>তারিখ:</strong> <?= formatDateToBengali(date('Y-m-d')) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mb-4 d-print-none">
                        <a href="student_fee_report.php?student_id=<?= $studentId ?>" class="btn btn-lg btn-success" target="_blank">
                            <i class="fas fa-file-alt me-2"></i>বিস্তারিত ফি রিপোর্ট দেখুন
                        </a>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Student Receipt Tab -->
                <?php if ($currentTab === 'student-receipt' && $student): ?>
                <div class="tab-pane fade show active">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?> - পেমেন্ট রসিদ</h4>
                    </div>
                    
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading">শিক্ষার্থী তথ্য</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                                <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                                <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                                <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                                <p><strong>তারিখ:</strong> <?= formatDateToBengali(date('Y-m-d')) ?></p>
                            </div>
                        </div>
                    </div>
                    
                    <?php if (empty($payments)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>এই শিক্ষার্থীর কোন পেমেন্ট নেই।
                    </div>
                    <?php else: ?>
                    <form action="generate_receipt.php" method="GET" id="receiptForm">
                        <input type="hidden" name="student_id" value="<?= $studentId ?>">
                        
                        <div class="card mb-4">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0">পেমেন্ট বাছাই করুন</h5>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="includeAllPayments" name="include_all_payments" value="1">
                                        <label class="form-check-label" for="includeAllPayments">সমস্ত পেমেন্ট অন্তর্ভুক্ত করুন</label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="payments-container">
                                    <?php foreach ($payments as $payment): ?>
                                    <div class="payment-item">
                                        <div class="form-check">
                                            <input class="form-check-input payment-checkbox" type="checkbox" name="payment_ids[]" value="<?= $payment['id'] ?>" id="payment_<?= $payment['id'] ?>">
                                            <label class="form-check-label" for="payment_<?= $payment['id'] ?>">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <strong><?= htmlspecialchars($payment['fee_type']) ?></strong><br>
                                                        <span class="text-muted">পেমেন্ট তারিখ: <?= formatDateToBengali($payment['payment_date']) ?></span>
                                                    </div>
                                                    <div class="text-end">
                                                        <strong class="text-success"><?= convertToBengaliNumber(number_format($payment['amount'], 2)) ?> ৳</strong><br>
                                                        <span class="text-muted">রেফারেন্স: <?= htmlspecialchars($payment['payment_reference'] ?? 'N/A') ?></span>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="me-2">বাছাইকৃত পেমেন্ট:</span>
                                        <span id="selectedCount" class="badge bg-primary">0</span>
                                    </div>
                                    <button type="submit" class="btn btn-success" id="generateButton" disabled>
                                        <i class="fas fa-file-invoice me-2"></i>রসিদ তৈরি করুন
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize select2 dropdown
            $('.select2').select2();
            
            // Handle fee summary filter form submission
            $('#feeSummaryFilterForm').submit(function(e) {
                e.preventDefault();
                var formData = $(this).serialize();
                
                $.ajax({
                    url: window.location.href,
                    type: 'GET',
                    data: formData,
                    success: function(response) {
                        // Update the content with the response
                        var content = $(response).find('#feeSummaryReport').html();
                        $('#feeSummaryReport').html(content);
                        
                        // Show the A4 report for viewing
                        $('.a4-report').show();
                    },
                    error: function(xhr, status, error) {
                        alert('Error loading report: ' + error);
                    }
                });
            });
            
            // Handle print button click
            $('#printFeeSummaryReport').click(function() {
                window.print();
            });
            
            // If filter parameters are already set on page load, show the report
            if ($('#session_id').val() !== '' && $('#class_id').val() !== '') {
                $('.a4-report').show();
            }
            
            // Handle invoice print button click
            $('#printInvoiceBtn').click(function() {
                // Show the invoice section for printing
                $('.a4-invoice').show();
                // Print the invoice
                window.print();
                // After printing dialog closes, hide the invoice again
                setTimeout(function() {
                    if (!window.matchMedia('print').matches) {
                        $('.a4-invoice').hide();
                    }
                }, 500);
            });
            
            // Format select2 elements
            $('.form-select').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        });
    </script>
</body>
</html> 