<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'add_fee_type':
            if (isset($_POST['name']) && !empty($_POST['name'])) {
                $name = $_POST['name'];
                $description = $_POST['description'] ?? '';
                $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
                $amount = isset($_POST['amount']) ? $_POST['amount'] : 0;

                // Check if fee type with this name already exists
                $checkQuery = "SELECT id FROM fee_types WHERE name = ?";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bind_param("s", $name);
                $checkStmt->execute();
                $checkResult = $checkStmt->get_result();
                
                if ($checkResult->num_rows > 0) {
                    $errorMessage = "এই নামের একটি ফি টাইপ ইতিমধ্যে বিদ্যমান!";
                } else {
                    $query = "INSERT INTO fee_types (name, description, is_recurring, amount) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("ssid", $name, $description, $isRecurring, $amount);
                    
                    if ($stmt->execute()) {
                        $successMessage = "ফি টাইপ সফলভাবে যোগ করা হয়েছে!";
                    } else {
                        $errorMessage = "ফি টাইপ যোগ করতে সমস্যা: " . $conn->error;
                    }
                }
            } else {
                $errorMessage = "ফি টাইপের নাম প্রয়োজনীয়!";
            }
            break;
            
        case 'update_fee_type':
            if (isset($_POST['id']) && !empty($_POST['id']) && isset($_POST['name']) && !empty($_POST['name'])) {
                $id = $_POST['id'];
                $name = $_POST['name'];
                $description = $_POST['description'] ?? '';
                $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
                $amount = isset($_POST['amount']) ? $_POST['amount'] : 0;

                $query = "UPDATE fee_types SET name = ?, description = ?, is_recurring = ?, amount = ? WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ssidi", $name, $description, $isRecurring, $amount, $id);
                
                if ($stmt->execute()) {
                    $successMessage = "ফি টাইপ সফলভাবে আপডেট করা হয়েছে!";
                } else {
                    $errorMessage = "ফি টাইপ আপডেট করতে সমস্যা: " . $conn->error;
                }
            } else {
                $errorMessage = "ফি টাইপের ID এবং নাম প্রয়োজনীয়!";
            }
            break;
            
        case 'delete_fee_type':
            if (isset($_POST['id']) && !empty($_POST['id'])) {
                $id = $_POST['id'];
                
                $query = "DELETE FROM fee_types WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $id);
                
                if ($stmt->execute()) {
                    $successMessage = "ফি টাইপ সফলভাবে মুছে ফেলা হয়েছে!";
                } else {
                    $errorMessage = "ফি টাইপ মুছতে সমস্যা: " . $conn->error;
                }
            }
            break;
    }
}

// Get fee type for editing if ID is provided in GET
$editFeeType = null;
if (isset($_GET['edit']) && !empty($_GET['edit'])) {
    $editId = $_GET['edit'];
    $query = "SELECT * FROM fee_types WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $editId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $editFeeType = $result->fetch_assoc();
    }
}

// Get fee type for viewing if ID is provided in GET
$viewFeeType = null;
if (isset($_GET['view']) && !empty($_GET['view'])) {
    $viewId = $_GET['view'];
    $query = "SELECT * FROM fee_types WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $viewId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $viewFeeType = $result->fetch_assoc();
    }
}

// Get all fee types for display
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypesResult = $conn->query($feeTypesQuery);
$feeTypes = ($feeTypesResult && $feeTypesResult->num_rows > 0) ? $feeTypesResult : null;

// Check if fee_types table exists, if not create it
$checkTableExists = "SHOW TABLES LIKE 'fee_types'";
$tableExists = $conn->query($checkTableExists);

if ($tableExists->num_rows == 0) {
    // Create fee_types table if it doesn't exist
    $createTableQuery = "CREATE TABLE `fee_types` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `is_recurring` tinyint(1) DEFAULT 0,
        `amount` decimal(10,2) DEFAULT 0.00,
        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($createTableQuery)) {
        $successMessage = "ফি টাইপের টেবিল সফলভাবে তৈরি করা হয়েছে!";
    } else {
        $errorMessage = "ফি টাইপের টেবিল তৈরি করতে সমস্যা: " . $conn->error;
    }
}
// If the table exists but amount column is missing, add it
else {
    $checkAmountColumn = "SHOW COLUMNS FROM fee_types LIKE 'amount'";
    $amountColumnExists = $conn->query($checkAmountColumn);

    if ($amountColumnExists->num_rows == 0) {
        // Add amount column if it doesn't exist
        $addAmountColumn = "ALTER TABLE fee_types ADD amount DECIMAL(10,2) DEFAULT 0";
        $conn->query($addAmountColumn);
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি টাইপ ম্যানেজমেন্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .form-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fee_types.php">
                            <i class="fas fa-tags me-2"></i> ফি এর ধরন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_reports.php">
                            <i class="fas fa-chart-pie me-2"></i> ফি রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <h2 class="mb-4">
                    <i class="fas fa-tags me-2"></i> ফি এর ধরন ম্যানেজমেন্ট
                </h2>
                
                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= $successMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $errorMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Fee Type Form -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <?php if ($editFeeType): ?>
                                        <i class="fas fa-edit me-2"></i> ফি টাইপ সম্পাদনা করুন
                                    <?php else: ?>
                                        <i class="fas fa-plus-circle me-2"></i> নতুন ফি টাইপ যোগ করুন
                                    <?php endif; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="POST">
                                    <?php if ($editFeeType): ?>
                                        <input type="hidden" name="action" value="update_fee_type">
                                        <input type="hidden" name="id" value="<?= $editFeeType['id'] ?>">
                                    <?php else: ?>
                                        <input type="hidden" name="action" value="add_fee_type">
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <label for="name" class="form-label">ফি টাইপের নাম</label>
                                        <input type="text" class="form-control" id="name" name="name" value="<?= $editFeeType ? htmlspecialchars($editFeeType['name']) : '' ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="amount" class="form-label">পরিমাণ (টাকা)</label>
                                        <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" value="<?= $editFeeType ? htmlspecialchars($editFeeType['amount']) : '0' ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">বিবরণ</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"><?= $editFeeType ? htmlspecialchars($editFeeType['description']) : '' ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="is_recurring" name="is_recurring" <?= $editFeeType && $editFeeType['is_recurring'] ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="is_recurring">নিয়মিত ফি (প্রতি মাসে পুনরাবৃত্তি)</label>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <?php if ($editFeeType): ?>
                                                <i class="fas fa-save me-2"></i> আপডেট করুন
                                            <?php else: ?>
                                                <i class="fas fa-plus-circle me-2"></i> যোগ করুন
                                            <?php endif; ?>
                                        </button>
                                        <?php if ($editFeeType): ?>
                                            <a href="fee_types.php" class="btn btn-secondary">
                                                <i class="fas fa-times me-2"></i> বাতিল করুন
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <?php if ($viewFeeType): ?>
                        <!-- Fee Type Details (View Mode) -->
                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-eye me-2"></i> ফি টাইপ বিবরণ</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>আইডি:</strong>
                                        <span><?= $viewFeeType['id'] ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>নাম:</strong>
                                        <span><?= htmlspecialchars($viewFeeType['name']) ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>পরিমাণ:</strong>
                                        <span>৳ <?= number_format($viewFeeType['amount'], 2) ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>বিবরণ:</strong>
                                        <span><?= htmlspecialchars($viewFeeType['description'] ?: 'কোন বিবরণ নেই') ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>নিয়মিত ফি?</strong>
                                        <span>
                                            <?php if ($viewFeeType['is_recurring']): ?>
                                                <span class="badge bg-success">হ্যাঁ</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">না</span>
                                            <?php endif; ?>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <strong>তৈরির তারিখ:</strong>
                                        <span><?= isset($viewFeeType['created_at']) ? date('d/m/Y H:i', strtotime($viewFeeType['created_at'])) : 'N/A' ?></span>
                                    </li>
                                </ul>
                                <div class="d-grid gap-2 mt-3">
                                    <a href="fee_types.php?edit=<?= $viewFeeType['id'] ?>" class="btn btn-warning">
                                        <i class="fas fa-edit me-2"></i> সম্পাদনা করুন
                                    </a>
                                    <a href="fee_types.php" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                                    </a>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Fee Types List -->
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i> ফি টাইপের তালিকা</h5>
                                    <a href="fees.php" class="btn btn-sm btn-light">
                                        <i class="fas fa-money-bill-wave me-1"></i> ফি ম্যানেজমেন্টে ফিরে যান
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover table-striped">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>আইডি</th>
                                                    <th>নাম</th>
                                                    <th>পরিমাণ</th>
                                                    <th>নিয়মিত?</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?= $type['id'] ?></td>
                                                        <td><?= htmlspecialchars($type['name']) ?></td>
                                                        <td>৳ <?= number_format($type['amount'] ?? 0, 2) ?></td>
                                                        <td>
                                                            <?php if (isset($type['is_recurring']) && $type['is_recurring']): ?>
                                                                <span class="badge bg-success">হ্যাঁ</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">না</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm" role="group">
                                                                <a href="fee_types.php?view=<?= $type['id'] ?>" class="btn btn-info" title="দেখুন">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                                <a href="fee_types.php?edit=<?= $type['id'] ?>" class="btn btn-warning" title="সম্পাদনা করুন">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <button type="button" class="btn btn-danger delete-fee-type-btn" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#deleteFeeTypeModal"
                                                                    data-id="<?= $type['id'] ?>"
                                                                    data-name="<?= htmlspecialchars($type['name']) ?>"
                                                                    title="মুছুন">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> কোন ফি টাইপ পাওয়া যায়নি। উপরের ফর্ম ব্যবহার করে নতুন ফি টাইপ যোগ করুন।
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Delete Fee Type Modal -->
    <div class="modal fade" id="deleteFeeTypeModal" tabindex="-1" aria-labelledby="deleteFeeTypeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteFeeTypeModalLabel"><i class="fas fa-trash-alt me-2"></i> ফি টাইপ মুছুন</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি <strong><span id="delete-fee-type-name"></span></strong> ফি টাইপ মুছতে চান?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i> এই ফি টাইপ মুছে ফেললে, এর সাথে সম্পর্কিত সকল ফি রেকর্ডও মুছে যাবে। এই অ্যাকশন ফিরিয়ে আনা যাবে না!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                    <form action="" method="POST">
                        <input type="hidden" name="action" value="delete_fee_type">
                        <input type="hidden" name="id" id="delete-fee-type-id">
                        <button type="submit" class="btn btn-danger">মুছুন</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Delete fee type modal handling
            $('.delete-fee-type-btn').click(function() {
                const id = $(this).data('id');
                const name = $(this).data('name');
                
                $('#delete-fee-type-id').val(id);
                $('#delete-fee-type-name').text(name);
            });
        });
    </script>
</body>
</html> 