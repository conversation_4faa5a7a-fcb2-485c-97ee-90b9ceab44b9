<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create fees table if it doesn't exist
$feesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_status ENUM('due', 'partial', 'paid') DEFAULT 'due',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feesTableQuery);

// Create fee_types table if it doesn't exist
$feeTypesTableQuery = "CREATE TABLE IF NOT EXISTS fee_types (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_recurring TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($feeTypesTableQuery);

// Create fee_map_class table if it doesn't exist
$feeMapClassTableQuery = "CREATE TABLE IF NOT EXISTS fee_map_class (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_type_id INT(11) NOT NULL,
    class_id INT(11) NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
)";
$conn->query($feeMapClassTableQuery);

// Create fee_map_session table if it doesn't exist
$feeMapSessionTableQuery = "CREATE TABLE IF NOT EXISTS fee_map_session (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_type_id INT(11) NOT NULL,
    session_name VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE
)";
$conn->query($feeMapSessionTableQuery);

// Create fee_map_student table if it doesn't exist
$feeMapStudentTableQuery = "CREATE TABLE IF NOT EXISTS fee_map_student (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_type_id INT(11) NOT NULL,
    student_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feeMapStudentTableQuery);

// Handle fee type operations
if (isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'add_fee_type':
            if (isset($_POST['name']) && !empty($_POST['name'])) {
                $name = $_POST['name'];
                $description = $_POST['description'] ?? '';
                $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;
                $amount = isset($_POST['amount']) ? floatval($_POST['amount']) : 0;

                $checkQuery = "SELECT * FROM fee_types WHERE name = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("s", $name);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $errorMessage = "এই নামের একটি ফি টাইপ ইতিমধ্যে বিদ্যমান!";
                } else {
                    $query = "INSERT INTO fee_types (name, description, is_recurring) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("ssi", $name, $description, $isRecurring);
                    
                    if ($stmt->execute()) {
                        $successMessage = "ফি টাইপ সফলভাবে যোগ করা হয়েছে!";
                    } else {
                        $errorMessage = "ফি টাইপ যোগ করতে সমস্যা: " . $conn->error;
                    }
                }
            } else {
                $errorMessage = "ফি টাইপের নাম প্রয়োজনীয়!";
            }
            break;
            
        case 'delete_fee_type':
            if (isset($_POST['fee_type_id']) && !empty($_POST['fee_type_id'])) {
                $feeTypeId = $_POST['fee_type_id'];
                
                $query = "DELETE FROM fee_types WHERE id = ?";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $feeTypeId);
                
                if ($stmt->execute()) {
                    $successMessage = "ফি টাইপ সফলভাবে মুছে ফেলা হয়েছে!";
                } else {
                    $errorMessage = "ফি টাইপ মুছতে সমস্যা: " . $conn->error;
                }
            }
            break;
            
        case 'add_fee':
            if (isset($_POST['student_id']) && !empty($_POST['student_id']) && 
                isset($_POST['fee_type']) && !empty($_POST['fee_type']) && 
                isset($_POST['amount']) && !empty($_POST['amount']) && 
                isset($_POST['due_date']) && !empty($_POST['due_date'])) {
                
                $studentId = $_POST['student_id'];
                $feeType = $_POST['fee_type'];
                $amount = $_POST['amount'];
                $dueDate = $_POST['due_date'];
                $paid = $_POST['paid'] ?? 0;
                
                $paymentStatus = 'due';
                if ($paid >= $amount) {
                    $paymentStatus = 'paid';
                } elseif ($paid > 0) {
                    $paymentStatus = 'partial';
                }
                
                $query = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status) 
                          VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($query);
                $stmt->bind_param("isddss", $studentId, $feeType, $amount, $paid, $dueDate, $paymentStatus);
                
                if ($stmt->execute()) {
                    $successMessage = "ফি সফলভাবে যোগ করা হয়েছে!";
                } else {
                    $errorMessage = "ফি যোগ করতে সমস্যা: " . $conn->error;
                }
            }
            break;
            
        case 'update_payment':
            if (isset($_POST['fee_id']) && !empty($_POST['fee_id']) && 
                isset($_POST['paid_amount']) && $_POST['paid_amount'] !== '') {
                
                $feeId = $_POST['fee_id'];
                $paidAmount = $_POST['paid_amount'];
                
                // Get current fee amount
                $checkQuery = "SELECT amount FROM fees WHERE id = ?";
                $stmt = $conn->prepare($checkQuery);
                $stmt->bind_param("i", $feeId);
                $stmt->execute();
                $result = $stmt->get_result();
                $feeData = $result->fetch_assoc();
                
                if ($feeData) {
                    $amount = $feeData['amount'];
                    
                    $paymentStatus = 'due';
                    if ($paidAmount >= $amount) {
                        $paymentStatus = 'paid';
                    } elseif ($paidAmount > 0) {
                        $paymentStatus = 'partial';
                    }
                    
                    $updateQuery = "UPDATE fees SET paid = ?, payment_status = ? WHERE id = ?";
                    $stmt = $conn->prepare($updateQuery);
                    $stmt->bind_param("dsi", $paidAmount, $paymentStatus, $feeId);
                    
                    if ($stmt->execute()) {
                        $successMessage = "পেমেন্ট সফলভাবে আপডেট করা হয়েছে!";
                    } else {
                        $errorMessage = "পেমেন্ট আপডেট করতে সমস্যা: " . $conn->error;
                    }
                }
            }
            break;
    }
}

// Get fee types
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);

// Get students
$studentsQuery = "SELECT s.*, u.username, d.department_name, c.class_name, ss.session_name FROM students s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                ORDER BY s.first_name, s.last_name";
$students = $conn->query($studentsQuery);

// Get fees with student information
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$itemsPerPage = 10;
$offset = ($page - 1) * $itemsPerPage;

// Search and filter parameters
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';
$statusFilter = isset($_GET['status']) ? $_GET['status'] : '';
$feeTypeFilter = isset($_GET['fee_type_filter']) ? $_GET['fee_type_filter'] : '';

$whereClause = "";
$params = [];
$types = "";

if (!empty($searchTerm)) {
    $whereClause .= " WHERE (s.student_id LIKE ? OR s.first_name LIKE ? OR s.last_name LIKE ? OR f.fee_type LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = [$searchParam, $searchParam, $searchParam, $searchParam];
    $types = "ssss";
}

if (!empty($statusFilter)) {
    $whereClause = empty($whereClause) ? " WHERE f.payment_status = ?" : $whereClause . " AND f.payment_status = ?";
    $params[] = $statusFilter;
    $types .= "s";
}

if (!empty($feeTypeFilter)) {
    $whereClause = empty($whereClause) ? " WHERE f.fee_type = ?" : $whereClause . " AND f.fee_type = ?";
    $params[] = $feeTypeFilter;
    $types .= "s";
} 

// Get total fees count for pagination
$countQuery = "SELECT COUNT(*) as total FROM fees f 
              LEFT JOIN students s ON f.student_id = s.id" . $whereClause;
$stmt = $conn->prepare($countQuery);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Get fees for current page
$feesQuery = "SELECT f.*, s.student_id as student_roll, s.first_name, s.last_name, 
            c.class_name, ss.session_name FROM fees f
            LEFT JOIN students s ON f.student_id = s.id
            LEFT JOIN classes c ON s.class_id = c.id
            LEFT JOIN sessions ss ON s.session_id = ss.id" . 
            $whereClause . 
            " ORDER BY f.due_date DESC, f.payment_status
            LIMIT ? OFFSET ?";

$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

$stmt = $conn->prepare($feesQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$fees = $stmt->get_result();

// Calculate total stats
$statsQuery = "SELECT 
                SUM(amount) as total_amount,
                SUM(paid) as total_paid,
                SUM(amount - paid) as total_due,
                COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as paid_count,
                COUNT(CASE WHEN payment_status = 'partial' THEN 1 END) as partial_count,
                COUNT(CASE WHEN payment_status = 'due' THEN 1 END) as due_count
              FROM fees";
$statsResult = $conn->query($statsQuery);
$stats = $statsResult->fetch_assoc();

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ম্যানেজমেন্ট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .dashboard-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .fee-status-paid {
            background-color: #d1e7dd;
            color: #0f5132;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .fee-status-partial {
            background-color: #fff3cd;
            color: #856404;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .fee-status-due {
            background-color: #f8d7da;
            color: #842029;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .form-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .nav-tabs .nav-link {
            font-weight: bold;
        }
        .nav-tabs .nav-link.active {
            border-bottom: 3px solid #0d6efd;
        }
        .notification-card {
            transition: transform 0.3s;
        }
        .notification-card:hover {
            transform: translateY(-5px);
        }
        .dashboard-stats .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .dashboard-stats .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }
        .nav-tabs .nav-link {
            border-radius: 0;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background-color: #f8f9fa;
            border-bottom: 3px solid #0d6efd;
        }
        .form-check-label {
            cursor: pointer;
        }
        .select2-container--bootstrap-5 .select2-selection {
            border: 1px solid #ced4da;
            padding: 0.375rem 0.75rem;
            height: auto;
            min-height: calc(1.5em + 0.75rem + 2px);
        }
        .fee-status-paid {
            background-color: #d1e7dd;
            color: #0f5132;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }
        .fee-status-partial {
            background-color: #fff3cd;
            color: #664d03;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }
        .fee-status-due {
            background-color: #f8d7da;
            color: #842029;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-weight: 500;
        }
        .view-due-btn {
            font-size: 0.75rem;
            padding: 0.1rem 0.4rem;
            vertical-align: middle;
        }
        .status-container {
            display: flex;
            align-items: center;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_types.php">
                            <i class="fas fa-tags me-2"></i> ফি এর ধরন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fee_reports.php">
                            <i class="fas fa-chart-pie me-2"></i> ফি রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <h2 class="mb-4">
                    <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                </h2>
                
                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= $successMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= $errorMessage ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Dashboard Cards -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-4">
                        <div class="card dashboard-card border-primary h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title">মোট ফি</h5>
                                <h2 class="display-5 text-primary">৳ <?= number_format($stats['total_amount'] ?? 0, 2) ?></h2>
                                <div class="mt-3">
                                    <span class="badge bg-success">পরিশোধিত: ৳ <?= number_format($stats['total_paid'] ?? 0, 2) ?></span>
                                    <span class="badge bg-danger">বকেয়া: ৳ <?= number_format($stats['total_due'] ?? 0, 2) ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="card dashboard-card border-info h-100">
                            <div class="card-body text-center">
                                <h5 class="card-title">পেমেন্ট স্ট্যাটাস</h5>
                                <canvas id="paymentStatusChart" width="100" height="100" class="mb-2"></canvas>
                                <div class="mt-2">
                                    <span class="badge bg-success">পরিশোধিত: <?= $stats['paid_count'] ?? 0 ?></span>
                                    <span class="badge bg-warning">আংশিক: <?= $stats['partial_count'] ?? 0 ?></span>
                                    <span class="badge bg-danger">বকেয়া: <?= $stats['due_count'] ?? 0 ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-4">
                        <div class="card dashboard-card border-success h-100">
                            <div class="card-body">
                                <h5 class="card-title text-center">দ্রুত অ্যাকশন</h5>
                                <div class="d-grid gap-2 mt-3">
                                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFeeModal">
                                        <i class="fas fa-plus-circle me-2"></i> নতুন ফি যোগ করুন
                                    </button>
                                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#bulkFeeModal">
                                        <i class="fas fa-th-list me-2"></i> বাল্ক ফি যোগ করুন
                                    </button>
                                    <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#feeTypeModal">
                                        <i class="fas fa-tags me-2"></i> ফি টাইপ ম্যানেজ করুন
                                    </button>
                                    <a href="fee_reports.php" class="btn btn-secondary">
                                        <i class="fas fa-chart-bar me-2"></i> ফি রিপোর্ট দেখুন
                                    </a>
                                    <a href="fee_assign.php" class="btn btn-danger">
                                        <i class="fas fa-money-bill-wave me-2"></i> ফি এসাইন করুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Add a button to the top actions section -->
                <div class="d-flex justify-content-end mb-4">
                    <a href="fee_assign.php" class="btn btn-primary me-2">
                        <i class="fas fa-plus-circle me-1"></i> ফি এসাইন
                    </a>
                    <a href="fee_collect.php" class="btn btn-success me-2">
                        <i class="fas fa-money-bill me-1"></i> ফি কালেকশন
                    </a>
                    <a href="reset_all_fees.php" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি ফি ডাটা রিসেট পেজে যেতে চান?');">
                        <i class="fas fa-redo-alt me-1"></i> ফি ডাটা রিসেট
                    </a>
                </div>
                
                <!-- Fee Management Tabs -->
                <ul class="nav nav-tabs mb-4" id="feeManagementTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees-tab-pane" type="button" role="tab" aria-controls="fees-tab-pane" aria-selected="true">
                            <i class="fas fa-list-alt me-2"></i> ফি তালিকা
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="fee-types-tab" data-bs-toggle="tab" data-bs-target="#fee-types-tab-pane" type="button" role="tab" aria-controls="fee-types-tab-pane" aria-selected="false">
                            <i class="fas fa-tags me-2"></i> ফি এর ধরন
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="collection-tab" data-bs-toggle="tab" data-bs-target="#collection-tab-pane" type="button" role="tab" aria-controls="collection-tab-pane" aria-selected="false">
                            <i class="fas fa-hand-holding-usd me-2"></i> ফি কালেকশন
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="feeManagementTabsContent">
                    <!-- Fees List Tab -->
                    <div class="tab-pane fade show active" id="fees-tab-pane" role="tabpanel" aria-labelledby="fees-tab" tabindex="0">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h5 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i> ফি তালিকা</h5>
                                    </div>
                                    <div class="col-auto">
                                        <button class="btn btn-sm btn-light" data-bs-toggle="modal" data-bs-target="#addFeeModal">
                                            <i class="fas fa-plus-circle me-1"></i> নতুন ফি যোগ করুন
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Search and Filter -->
                                <form action="" method="GET" class="mb-4 form-section">
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <label for="search" class="form-label">অনুসন্ধান:</label>
                                            <input type="text" class="form-control" id="search" name="search" placeholder="শিক্ষার্থী আইডি, নাম বা ফি টাইপ" value="<?= htmlspecialchars($searchTerm) ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="status" class="form-label">পেমেন্ট স্ট্যাটাস:</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="">সকল স্ট্যাটাস</option>
                                                <option value="paid" <?= $statusFilter === 'paid' ? 'selected' : '' ?>>পরিশোধিত</option>
                                                <option value="partial" <?= $statusFilter === 'partial' ? 'selected' : '' ?>>আংশিক</option>
                                                <option value="due" <?= $statusFilter === 'due' ? 'selected' : '' ?>>বকেয়া</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="fee_type_filter" class="form-label">ফি টাইপ:</label>
                                            <select class="form-select" id="fee_type_filter" name="fee_type_filter">
                                                <option value="">সকল ফি টাইপ</option>
                                                <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                    <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                        <option value="<?= htmlspecialchars($type['name']) ?>" <?= $feeTypeFilter === $type['name'] ? 'selected' : '' ?>>
                                                            <?= htmlspecialchars($type['name']) ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                    <?php $feeTypes->data_seek(0); // Reset result pointer ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-2 align-self-end">
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-search me-1"></i> অনুসন্ধান
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                
                                <!-- Fees Table -->
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>ক্রমিক</th>
                                                <th>শিক্ষার্থী আইডি</th>
                                                <th>নাম</th>
                                                <th>ক্লাস</th>
                                                <th>ফি টাইপ</th>
                                                <th>মোট পরিমাণ</th>
                                                <th>পরিশোধিত</th>
                                                <th>বাকি</th>
                                                <th>শেষ তারিখ</th>
                                                <th>স্ট্যাটাস</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($fees && $fees->num_rows > 0): ?>
                                                <?php 
                                                $counter = ($page - 1) * $itemsPerPage + 1;
                                                while ($fee = $fees->fetch_assoc()): 
                                                ?>
                                                    <tr>
                                                        <td><?= $counter++ ?></td>
                                                        <td><?= htmlspecialchars($fee['student_roll']) ?></td>
                                                        <td><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></td>
                                                        <td><?= htmlspecialchars($fee['class_name']) ?></td>
                                                        <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                                                        <td>৳ <?= number_format($fee['amount'], 2) ?></td>
                                                        <td>৳ <?= number_format($fee['paid'], 2) ?></td>
                                                        <td>৳ <?= number_format($fee['amount'] - $fee['paid'], 2) ?></td>
                                                        <td><?= date('d/m/Y', strtotime($fee['due_date'])) ?></td>
                                                        <td>
                                                            <?php if ($fee['payment_status'] === 'paid'): ?>
                                                                <span class="fee-status-paid">পরিশোধিত</span>
                                                            <?php elseif ($fee['payment_status'] === 'partial'): ?>
                                                                <span class="fee-status-partial">আংশিক</span>
                                                            <?php else: ?>
                                                                <div class="status-container">
                                                                    <span class="fee-status-due">বকেয়া</span>
                                                                    <button type="button" class="btn btn-sm btn-info ms-2 view-due-btn" 
                                                                        data-bs-toggle="modal" 
                                                                        data-bs-target="#viewFeeModal"
                                                                        data-fee-id="<?= $fee['id'] ?>"
                                                                        data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                                        data-student-id="<?= htmlspecialchars($fee['student_roll']) ?>"
                                                                        data-class="<?= htmlspecialchars($fee['class_name']) ?>"
                                                                        data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                                        data-amount="<?= $fee['amount'] ?>"
                                                                        data-paid="<?= $fee['paid'] ?>"
                                                                        data-due="<?= $fee['amount'] - $fee['paid'] ?>"
                                                                        data-due-date="<?= date('d/m/Y', strtotime($fee['due_date'])) ?>"
                                                                        data-status="<?= $fee['payment_status'] ?>">
                                                                        <i class="fas fa-eye"></i> ভিউ
                                                                    </button>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm" role="group">
                                                                <button type="button" class="btn btn-primary payment-btn" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#paymentModal"
                                                                    data-fee-id="<?= $fee['id'] ?>"
                                                                    data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                                    data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                                    data-amount="<?= $fee['amount'] ?>"
                                                                    data-paid="<?= $fee['paid'] ?>"
                                                                    data-due="<?= $fee['amount'] - $fee['paid'] ?>">
                                                                    <i class="fas fa-hand-holding-usd"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-info view-btn" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#viewFeeModal"
                                                                    data-fee-id="<?= $fee['id'] ?>"
                                                                    data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                                    data-student-id="<?= htmlspecialchars($fee['student_roll']) ?>"
                                                                    data-class="<?= htmlspecialchars($fee['class_name']) ?>"
                                                                    data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>"
                                                                    data-amount="<?= $fee['amount'] ?>"
                                                                    data-paid="<?= $fee['paid'] ?>"
                                                                    data-due="<?= $fee['amount'] - $fee['paid'] ?>"
                                                                    data-due-date="<?= date('d/m/Y', strtotime($fee['due_date'])) ?>"
                                                                    data-status="<?= $fee['payment_status'] ?>">
                                                                    <i class="fas fa-eye"></i>
                                                                </button>
                                                                <a href="edit_fee.php?id=<?= $fee['id'] ?>" class="btn btn-warning">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <button type="button" class="btn btn-danger delete-btn" 
                                                                    data-bs-toggle="modal" 
                                                                    data-bs-target="#deleteFeeModal"
                                                                    data-fee-id="<?= $fee['id'] ?>"
                                                                    data-student-name="<?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?>"
                                                                    data-fee-type="<?= htmlspecialchars($fee['fee_type']) ?>">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="11" class="text-center">কোন ফি রেকর্ড পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination justify-content-center">
                                            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&fee_type_filter=<?= urlencode($feeTypeFilter) ?>">আগের</a>
                                            </li>
                                            
                                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                                <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&fee_type_filter=<?= urlencode($feeTypeFilter) ?>"><?= $i ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($searchTerm) ?>&status=<?= urlencode($statusFilter) ?>&fee_type_filter=<?= urlencode($feeTypeFilter) ?>">পরের</a>
                                            </li>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Fee Types Tab -->
                    <div class="tab-pane fade" id="fee-types-tab-pane" role="tabpanel" aria-labelledby="fee-types-tab" tabindex="0">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 class="card-title mb-0"><i class="fas fa-plus-circle me-2"></i> নতুন ফি এর ধরন যোগ করুন</h5>
                                            <a href="fee_types.php" class="btn btn-sm btn-light">
                                                <i class="fas fa-cog me-1"></i> সকল ফি ধরন ম্যানেজ করুন
                                            </a>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <form action="" method="POST">
                                            <input type="hidden" name="action" value="add_fee_type">
                                            
                                            <div class="mb-3">
                                                <label for="name" class="form-label">ফি টাইপের নাম</label>
                                                <input type="text" class="form-control" id="name" name="name" required>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="amount" class="form-label">পরিমাণ (টাকা)</label>
                                                <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" value="0" required>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="description" class="form-label">বিবরণ</label>
                                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                            </div>
                                            
                                            <div class="mb-3 form-check">
                                                <input type="checkbox" class="form-check-input" id="is_recurring" name="is_recurring">
                                                <label class="form-check-label" for="is_recurring">নিয়মিত ফি (প্রতি মাসে পুনরাবৃত্তি)</label>
                                            </div>
                                            
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="fas fa-save me-2"></i> সংরক্ষণ করুন
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="card-title mb-0"><i class="fas fa-list-alt me-2"></i> ফি এর ধরন তালিকা</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover table-striped">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>আইডি</th>
                                                        <th>নাম</th>
                                                        <th>বিবরণ</th>
                                                        <th>পরিমাণ</th>
                                                        <th>নিয়মিত?</th>
                                                        <th>অ্যাকশন</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                        <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                            <tr>
                                                                <td><?= $type['id'] ?></td>
                                                                <td><?= htmlspecialchars($type['name']) ?></td>
                                                                <td><?= htmlspecialchars($type['description'] ?? '-') ?></td>
                                                                <td>৳ <?= number_format($type['amount'] ?? 0, 2) ?></td>
                                                                <td>
                                                                    <?php if (isset($type['is_recurring']) && $type['is_recurring']): ?>
                                                                        <span class="badge bg-success">হ্যাঁ</span>
                                                                    <?php else: ?>
                                                                        <span class="badge bg-secondary">না</span>
                                                                    <?php endif; ?>
                                                                </td>
                                                                <td>
                                                                    <div class="btn-group btn-group-sm" role="group">
                                                                        <a href="fee_types.php?edit=<?= $type['id'] ?>" class="btn btn-warning" title="সম্পাদনা করুন">
                                                                            <i class="fas fa-edit"></i>
                                                                        </a>
                                                                        <a href="fee_types.php?view=<?= $type['id'] ?>" class="btn btn-info" title="দেখুন">
                                                                            <i class="fas fa-eye"></i>
                                                                        </a>
                                                                        <button type="button" class="btn btn-danger delete-fee-type-btn"
                                                                            data-bs-toggle="modal" 
                                                                            data-bs-target="#deleteFeeTypeModal"
                                                                            data-fee-type-id="<?= $type['id'] ?>"
                                                                            data-fee-type-name="<?= htmlspecialchars($type['name']) ?>"
                                                                            title="মুছুন">
                                                                            <i class="fas fa-trash-alt"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        <?php endwhile; ?>
                                                    <?php else: ?>
                                                        <tr>
                                                            <td colspan="6" class="text-center">কোন ফি টাইপ পাওয়া যায়নি</td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Fee Collection Tab -->
                    <div class="tab-pane fade" id="collection-tab-pane" role="tabpanel" aria-labelledby="collection-tab" tabindex="0">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-hand-holding-usd me-2"></i> ফি কালেকশন</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-section mb-4">
                                    <h5 class="mb-3">শিক্ষার্থীর ফি কালেকশন করুন</h5>
                                    <form id="findStudentForm" class="row g-3">
                                        <div class="col-md-4">
                                            <label for="collection_session_id" class="form-label">সেশন নির্বাচন করুন</label>
                                            <select class="form-select" id="collection_session_id" name="session_id" required>
                                                <option value="">সেশন নির্বাচন করুন</option>
                                                <?php 
                                                $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                                                $sessions = $conn->query($sessionsQuery);
                                                if ($sessions && $sessions->num_rows > 0):
                                                    while ($session = $sessions->fetch_assoc()):
                                                ?>
                                                    <option value="<?= $session['id'] ?>"><?= htmlspecialchars($session['session_name']) ?></option>
                                                <?php 
                                                    endwhile;
                                                endif;
                                                ?>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="collection_class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                            <select class="form-select" id="collection_class_id" name="class_id" disabled required>
                                                <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                                            </select>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="student_selection_type" class="form-label">শিক্ষার্থী নির্বাচন ধরন</label>
                                            <select class="form-select" id="student_selection_type" name="student_selection_type" disabled>
                                                <option value="single">একজন শিক্ষার্থী</option>
                                                <option value="multiple">একাধিক শিক্ষার্থী</option>
                                                <option value="all">সমস্ত শিক্ষার্থী</option>
                                            </select>
                                        </div>
                                        
                                        <!-- Single Student Selection -->
                                        <div class="col-md-12" id="single_student_section">
                                            <label for="collection_student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন</label>
                                            <select class="form-select" id="collection_student_id" name="student_id" disabled required>
                                                <option value="">প্রথমে ক্লাস নির্বাচন করুন</option>
                                            </select>
                                        </div>
                                        
                                        <!-- Multiple Students Selection -->
                                        <div class="col-md-12 d-none" id="multiple_students_section">
                                            <div class="card">
                                                <div class="card-header bg-light">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <h6 class="mb-0">একাধিক শিক্ষার্থী নির্বাচন করুন</h6>
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="selectAllStudents">
                                                            <label class="form-check-label" for="selectAllStudents">সকল শিক্ষার্থী নির্বাচন করুন</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                                    <div id="studentListContainer">
                                                        <div class="text-center text-muted"><i class="fas fa-info-circle me-2"></i> প্রথমে ক্লাস নির্বাচন করুন</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- All Students Selection -->
                                        <div class="col-md-12 d-none" id="all_students_section">
                                            <div class="alert alert-info">
                                                <i class="fas fa-info-circle me-2"></i> এই ক্লাসের সমস্ত শিক্ষার্থী নির্বাচিত হবে।
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-12 text-end">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search me-1"></i> শিক্ষার্থীর ফি দেখুন
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                
                                <div id="studentFeeContainer" class="d-none">
                                    <div class="card mb-4">
                                        <div class="card-header bg-info text-white">
                                            <h5 class="student-name mb-0">শিক্ষার্থী তথ্য</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>আইডি:</strong></span>
                                                            <span class="student-id"></span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>নাম:</strong></span>
                                                            <span class="student-full-name"></span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>ক্লাস:</strong></span>
                                                            <span class="student-class"></span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-md-6">
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>সেশন:</strong></span>
                                                            <span class="student-session"></span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>বিভাগ:</strong></span>
                                                            <span class="student-department"></span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span><strong>মোট বকেয়া:</strong></span>
                                                            <span class="student-total-due badge bg-danger fs-6"></span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5>বকেয়া ফি সমূহ</h5>
                                        <div>
                                            <button type="button" class="btn btn-success" id="bulkPaymentBtn" disabled>
                                                <i class="fas fa-money-bill-wave me-2"></i> নির্বাচিত ফি পরিশোধ করুন
                                            </button>
                                            <div class="form-check form-check-inline ms-2">
                                                <input class="form-check-input" type="checkbox" id="selectAllFees">
                                                <label class="form-check-label" for="selectAllFees">সবগুলো নির্বাচন করুন</label>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-hover table-striped" id="studentFeesTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th style="width: 40px;"><input type="checkbox" class="select-all-checkbox"></th>
                                                    <th>ফি টাইপ</th>
                                                    <th>মোট পরিমাণ</th>
                                                    <th>পরিশোধিত</th>
                                                    <th>বাকি</th>
                                                    <th>শেষ তারিখ</th>
                                                    <th>স্ট্যাটাস</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- JavaScript will populate this section -->
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-primary">
                                                    <th colspan="2">মোট</th>
                                                    <th class="total-amount"></th>
                                                    <th class="total-paid"></th>
                                                    <th class="total-due"></th>
                                                    <th colspan="3"></th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                                
                                <div id="noStudentFound" class="alert alert-warning d-none">
                                    <i class="fas fa-exclamation-triangle me-2"></i> কোন শিক্ষার্থী পাওয়া যায়নি। দয়া করে অন্য আইডি দিয়ে চেষ্টা করুন।
                                </div>
                                
                                <div id="noFeesFound" class="alert alert-info d-none">
                                    <i class="fas fa-info-circle me-2"></i> এই শিক্ষার্থীর কোন বকেয়া ফি নেই।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Add Fee Modal -->
                <div class="modal fade" id="addFeeModal" tabindex="-1" aria-labelledby="addFeeModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="addFeeModalLabel"><i class="fas fa-plus-circle me-2"></i> নতুন ফি যোগ করুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="" method="POST">
                                    <input type="hidden" name="action" value="add_fee">
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="student_id" class="form-label">শিক্ষার্থী নির্বাচন করুন</label>
                                            <select class="form-select" id="student_id" name="student_id" required>
                                                <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                                <?php if ($students && $students->num_rows > 0): ?>
                                                    <?php while ($student = $students->fetch_assoc()): ?>
                                                        <option value="<?= $student['id'] ?>">
                                                            <?= htmlspecialchars($student['student_id'] . ' - ' . $student['first_name'] . ' ' . $student['last_name']) ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="fee_type" class="form-label">ফি টাইপ</label>
                                            <select class="form-select" id="fee_type" name="fee_type" required>
                                                <option value="">ফি টাইপ নির্বাচন করুন</option>
                                                <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                    <?php $feeTypes->data_seek(0); // Reset pointer ?>
                                                    <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                        <option value="<?= htmlspecialchars($type['name']) ?>" data-amount="<?= htmlspecialchars($type['amount'] ?? 0) ?>">
                                                            <?= htmlspecialchars($type['name']) ?>
                                                        </option>
                                                    <?php endwhile; ?>
                                                <?php endif; ?>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="amount" class="form-label">ফি পরিমাণ (৳)</label>
                                            <input type="number" step="0.01" min="0" class="form-control" id="amount" name="amount" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="paid" class="form-label">পরিশোধিত পরিমাণ (৳)</label>
                                            <input type="number" step="0.01" min="0" class="form-control" id="paid" name="paid" value="0">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="due_date" class="form-label">শেষ তারিখ</label>
                                        <input type="date" class="form-control" id="due_date" name="due_date" required>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i> সংরক্ষণ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Payment Modal -->
                <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="paymentModalLabel"><i class="fas fa-hand-holding-usd me-2"></i> পেমেন্ট আপডেট করুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="" method="POST" id="paymentForm">
                                    <input type="hidden" name="action" value="update_payment">
                                    <input type="hidden" name="fee_id" id="payment_fee_id">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">শিক্ষার্থী</label>
                                        <input type="text" class="form-control" id="payment_student_name" readonly>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">ফি টাইপ</label>
                                        <input type="text" class="form-control" id="payment_fee_type" readonly>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label class="form-label">মোট পরিমাণ</label>
                                            <div class="input-group">
                                                <span class="input-group-text">৳</span>
                                                <input type="text" class="form-control" id="payment_amount" readonly>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">বকেয়া</label>
                                            <div class="input-group">
                                                <span class="input-group-text">৳</span>
                                                <input type="text" class="form-control" id="payment_due" readonly>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="paid_amount" class="form-label">পরিশোধিত পরিমাণ</label>
                                        <div class="input-group">
                                            <span class="input-group-text">৳</span>
                                            <input type="number" step="0.01" min="0" class="form-control" id="paid_amount" name="paid_amount" required>
                                        </div>
                                        <div class="form-text">বর্তমানে পরিশোধিত: ৳ <span id="payment_already_paid">0.00</span></div>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-2"></i> আপডেট করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- View Fee Modal -->
                <div class="modal fade" id="viewFeeModal" tabindex="-1" aria-labelledby="viewFeeModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-info text-white">
                                <h5 class="modal-title" id="viewFeeModalLabel"><i class="fas fa-eye me-2"></i> ফি বিবরণ</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Tab navigation for fee details -->
                                <ul class="nav nav-tabs mb-3" id="feeDetailsTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" id="fee-details-tab" data-bs-toggle="tab" data-bs-target="#fee-details-content" 
                                            type="button" role="tab" aria-controls="fee-details-content" aria-selected="true">
                                            <i class="fas fa-file-invoice-dollar me-2"></i> ফি বিবরণ
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="student-fee-report-tab" data-bs-toggle="tab" data-bs-target="#student-fee-report-content" 
                                            type="button" role="tab" aria-controls="student-fee-report-content" aria-selected="false">
                                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী ফি রিপোর্ট
                                        </button>
                                    </li>
                                </ul>
                                
                                <!-- Tab contents -->
                                <div class="tab-content" id="feeDetailsTabsContent">
                                    <!-- Fee Details Tab -->
                                    <div class="tab-pane fade show active" id="fee-details-content" role="tabpanel" aria-labelledby="fee-details-tab">
                                        <div class="card">
                                            <div class="card-body">
                                                <ul class="list-group list-group-flush">
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>শিক্ষার্থী আইডি:</strong>
                                                        <span id="view_student_id"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>শিক্ষার্থী নাম:</strong>
                                                        <span id="view_student_name"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>ক্লাস:</strong>
                                                        <span id="view_class"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>ফি টাইপ:</strong>
                                                        <span id="view_fee_type"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>মোট পরিমাণ:</strong>
                                                        <span id="view_amount"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>পরিশোধিত:</strong>
                                                        <span id="view_paid"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>বকেয়া:</strong>
                                                        <span id="view_due"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>শেষ তারিখ:</strong>
                                                        <span id="view_due_date"></span>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <strong>পেমেন্ট স্ট্যাটাস:</strong>
                                                        <span id="view_status"></span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <!-- Payment History Section -->
                                        <div class="mt-4" id="payment_history_section">
                                            <h5 class="mb-3"><i class="fas fa-history me-2"></i> পেমেন্ট ইতিহাস</h5>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>তারিখ</th>
                                                            <th>পরিমাণ</th>
                                                            <th>পেমেন্ট পদ্ধতি</th>
                                                            <th>নোট</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="payment_history_table">
                                                        <!-- Will be populated by JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        
                                        <!-- Monthly Fee Progress -->
                                        <div class="mt-4" id="monthly_fee_progress_section" style="display: none;">
                                            <h5 class="mb-3"><i class="fas fa-calendar-check me-2"></i> মাসিক বেতন প্রগ্রেস</h5>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>মাস</th>
                                                            <th>স্ট্যাটাস</th>
                                                            <th>পরিমাণ</th>
                                                            <th>পরিশোধিত</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="monthly_fee_progress_table">
                                                        <!-- Will be populated by JavaScript -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Student Fee Report Tab -->
                                    <div class="tab-pane fade" id="student-fee-report-content" role="tabpanel" aria-labelledby="student-fee-report-tab">
                                        <div id="student_fee_report">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i> শিক্ষার্থী ফি রিপোর্ট</h5>
                                                <button type="button" class="btn btn-sm btn-success" id="printStudentFeeReport">
                                                    <i class="fas fa-print me-2"></i> প্রিন্ট করুন
                                                </button>
                                            </div>
                                            
                                            <!-- Student Information Card -->
                                            <div class="card mb-3">
                                                <div class="card-header bg-info text-white">
                                                    <h6 class="mb-0"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী তথ্য</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <p><strong>শিক্ষার্থী আইডি:</strong> <span id="report_student_id"></span></p>
                                                            <p><strong>নাম:</strong> <span id="report_student_name"></span></p>
                                                            <p><strong>ক্লাস:</strong> <span id="report_class"></span></p>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <p><strong>ফি স্ট্যাটাস সারসংক্ষেপ:</strong></p>
                                                            <ul>
                                                                <li><strong>মোট ফি:</strong> <span id="report_total_amount"></span></li>
                                                                <li><strong>পরিশোধিত:</strong> <span id="report_total_paid"></span></li>
                                                                <li><strong>বকেয়া:</strong> <span id="report_total_due"></span></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Fee Summary Table -->
                                            <div class="card mb-3">
                                                <div class="card-header bg-primary text-white">
                                                    <h6 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i> ফি সারসংক্ষেপ</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>ফি টাইপ</th>
                                                                    <th>পরিমাণ</th>
                                                                    <th>পরিশোধিত</th>
                                                                    <th>বকেয়া</th>
                                                                    <th>স্ট্যাটাস</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="fee_summary_table">
                                                                <!-- Will be populated by JavaScript -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Monthly Fee Status Table -->
                                            <div class="card mb-3" id="monthly_status_card" style="display: none;">
                                                <div class="card-header bg-success text-white">
                                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i> মাসিক বেতন স্ট্যাটাস</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>মাস</th>
                                                                    <th>পরিমাণ</th>
                                                                    <th>পরিশোধিত</th>
                                                                    <th>বকেয়া</th>
                                                                    <th>স্ট্যাটাস</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="monthly_status_table">
                                                                <!-- Will be populated by JavaScript -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Recent Payment History -->
                                            <div class="card">
                                                <div class="card-header bg-warning text-dark">
                                                    <h6 class="mb-0"><i class="fas fa-history me-2"></i> সাম্প্রতিক পেমেন্ট ইতিহাস</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered table-striped">
                                                            <thead class="table-light">
                                                                <tr>
                                                                    <th>তারিখ</th>
                                                                    <th>ফি টাইপ</th>
                                                                    <th>পরিমাণ</th>
                                                                    <th>পেমেন্ট পদ্ধতি</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="recent_payment_table">
                                                                <!-- Will be populated by JavaScript -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Delete Fee Modal -->
                <div class="modal fade" id="deleteFeeModal" tabindex="-1" aria-labelledby="deleteFeeModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="deleteFeeModalLabel"><i class="fas fa-trash-alt me-2"></i> ফি মুছুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>আপনি কি নিশ্চিত যে আপনি <strong><span id="delete_student_name"></span></strong> এর <strong><span id="delete_fee_type"></span></strong> ফি মুছতে চান?</p>
                                <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i> এই অ্যাকশন ফিরিয়ে আনা যাবে না!</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                <form action="" method="POST">
                                    <input type="hidden" name="action" value="delete_fee">
                                    <input type="hidden" name="fee_id" id="delete_fee_id">
                                    <button type="submit" class="btn btn-danger">মুছুন</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Delete Fee Type Modal -->
                <div class="modal fade" id="deleteFeeTypeModal" tabindex="-1" aria-labelledby="deleteFeeTypeModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-danger text-white">
                                <h5 class="modal-title" id="deleteFeeTypeModalLabel"><i class="fas fa-trash-alt me-2"></i> ফি টাইপ মুছুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>আপনি কি নিশ্চিত যে আপনি <strong><span id="delete_fee_type_name"></span></strong> ফি টাইপ মুছতে চান?</p>
                                <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i> এই ফি টাইপ মুছে ফেললে, এর সাথে সম্পর্কিত সকল ফি রেকর্ডও মুছে যাবে। এই অ্যাকশন ফিরিয়ে আনা যাবে না!</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                <form action="" method="POST">
                                    <input type="hidden" name="action" value="delete_fee_type">
                                    <input type="hidden" name="fee_type_id" id="delete_fee_type_id">
                                    <button type="submit" class="btn btn-danger">মুছুন</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Bulk Fee Modal -->
                <div class="modal fade" id="bulkFeeModal" tabindex="-1" aria-labelledby="bulkFeeModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="bulkFeeModalLabel"><i class="fas fa-th-list me-2"></i> বাল্ক ফি যোগ করুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="process_bulk_fee.php" method="POST" id="bulkFeeForm">
                                    <input type="hidden" name="action" value="add_bulk_fee">
                                    
                                    <div class="mb-3">
                                        <label for="bulk_fee_type" class="form-label">ফি টাইপ নির্বাচন করুন</label>
                                        <select class="form-select" id="bulk_fee_type" name="fee_type" required>
                                            <option value="">ফি টাইপ নির্বাচন করুন</option>
                                            <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                                <?php $feeTypes->data_seek(0); // Reset pointer ?>
                                                <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                    <option value="<?= htmlspecialchars($type['name']) ?>" data-amount="<?= htmlspecialchars($type['amount'] ?? 0) ?>">
                                                        <?= htmlspecialchars($type['name']) ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <!-- Monthly fee selector - will show when "মাসিক বেতন" is selected -->
                                    <div class="mb-3" id="monthly_fee_selector" style="display: none;">
                                        <label class="form-label">মাস নির্বাচন করুন</label>
                                        <div class="d-flex justify-content-end mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllMonths">
                                                <label class="form-check-label" for="selectAllMonths">সব মাস নির্বাচন করুন</label>
                                            </div>
                                        </div>
                                        <div class="border rounded p-3">
                                            <div class="row">
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="january" id="month_january">
                                                        <label class="form-check-label" for="month_january">জানুয়ারি</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="february" id="month_february">
                                                        <label class="form-check-label" for="month_february">ফেব্রুয়ারি</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="march" id="month_march">
                                                        <label class="form-check-label" for="month_march">মার্চ</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="april" id="month_april">
                                                        <label class="form-check-label" for="month_april">এপ্রিল</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="may" id="month_may">
                                                        <label class="form-check-label" for="month_may">মে</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="june" id="month_june">
                                                        <label class="form-check-label" for="month_june">জুন</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="july" id="month_july">
                                                        <label class="form-check-label" for="month_july">জুলাই</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="august" id="month_august">
                                                        <label class="form-check-label" for="month_august">আগস্ট</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="september" id="month_september">
                                                        <label class="form-check-label" for="month_september">সেপ্টেম্বর</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="october" id="month_october">
                                                        <label class="form-check-label" for="month_october">অক্টোবর</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="november" id="month_november">
                                                        <label class="form-check-label" for="month_november">নভেম্বর</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 col-6 mb-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input month-checkbox" type="checkbox" name="months[]" value="december" id="month_december">
                                                        <label class="form-check-label" for="month_december">ডিসেম্বর</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="bulk_session" class="form-label">সেশন নির্বাচন করুন</label>
                                        <select class="form-select" id="bulk_session" name="session_id" required>
                                            <option value="">সেশন নির্বাচন করুন</option>
                                            <?php 
                                            $sessionsQuery = "SELECT * FROM sessions ORDER BY session_name";
                                            $sessions = $conn->query($sessionsQuery);
                                            if ($sessions && $sessions->num_rows > 0):
                                                while ($session = $sessions->fetch_assoc()):
                                            ?>
                                                <option value="<?= $session['id'] ?>"><?= htmlspecialchars($session['session_name']) ?></option>
                                            <?php 
                                                endwhile;
                                            endif;
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="bulk_class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                        <select class="form-select" id="bulk_class_id" name="class_id" disabled required>
                                            <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">শিক্ষার্থী নির্বাচন করুন</label>
                                        <div class="d-flex justify-content-end mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllStudents">
                                                <label class="form-check-label" for="selectAllStudents">সবাইকে নির্বাচন করুন</label>
                                            </div>
                                        </div>
                                        <div id="studentListContainer" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                            <div class="text-center text-muted">
                                                <i class="fas fa-info-circle me-2"></i> সেশন এবং ক্লাস নির্বাচন করুন
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="bulk_amount" class="form-label">ফি পরিমাণ (৳)</label>
                                            <input type="number" step="0.01" min="0" class="form-control" id="bulk_amount" name="amount" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="bulk_due_date" class="form-label">শেষ তারিখ</label>
                                            <input type="date" class="form-control" id="bulk_due_date" name="due_date" required>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i> বাল্ক ফি যোগ করলে নির্বাচিত সকল শিক্ষার্থীর জন্য একই ফি যোগ হবে।
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary" id="bulkFeeSubmitBtn">
                                            <i class="fas fa-save me-2"></i> বাল্ক ফি যোগ করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Bulk Payment Modal -->
                <div class="modal fade" id="bulkPaymentModal" tabindex="-1" aria-labelledby="bulkPaymentModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-success text-white">
                                <h5 class="modal-title" id="bulkPaymentModalLabel"><i class="fas fa-money-bill-wave me-2"></i> একাধিক ফি পরিশোধ করুন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="student-info-summary mb-4 bg-light p-3 rounded">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p class="mb-1"><strong>শিক্ষার্থী আইডি:</strong> <span id="bulk_student_id"></span></p>
                                            <p class="mb-1"><strong>নাম:</strong> <span id="bulk_student_name"></span></p>
                                            <p class="mb-0"><strong>ক্লাস:</strong> <span id="bulk_student_class"></span></p>
                                        </div>
                                        <div class="col-md-6 text-md-end">
                                            <p class="mb-1"><strong>সেশন:</strong> <span id="bulk_student_session"></span></p>
                                            <p class="mb-1"><strong>বিভাগ:</strong> <span id="bulk_student_department"></span></p>
                                            <p class="mb-0"><strong>মোট নির্বাচিত ফি:</strong> <span class="badge bg-primary fs-6" id="bulk_total_fees">৳ 0.00</span></p>
                                        </div>
                                    </div>
                                </div>

                                <form action="" method="POST" id="bulkPaymentForm">
                                    <input type="hidden" name="action" value="bulk_payment">
                                    <input type="hidden" name="student_id" id="bulk_payment_student_id">
                                    
                                    <div class="table-responsive mb-3">
                                        <table class="table table-hover table-bordered" id="bulkPaymentTable">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>ফি টাইপ</th>
                                                    <th>মোট পরিমাণ</th>
                                                    <th>বাকি</th>
                                                    <th>পরিশোধিত</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <!-- JavaScript will populate this -->
                                            </tbody>
                                            <tfoot>
                                                <tr class="table-success">
                                                    <th colspan="2">মোট</th>
                                                    <th id="bulk_payment_total_due">৳ 0.00</th>
                                                    <th id="bulk_payment_total_paying">৳ 0.00</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                            <select class="form-select" id="payment_method" name="payment_method" required>
                                                <option value="">পেমেন্ট পদ্ধতি নির্বাচন করুন</option>
                                                <option value="cash">নগদ</option>
                                                <option value="bank">ব্যাংক ট্রান্সফার</option>
                                                <option value="mobile_banking">মোবাইল ব্যাংকিং</option>
                                                <option value="online">অনলাইন পেমেন্ট</option>
                                                <option value="other">অন্যান্য</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="payment_date" class="form-label">পেমেন্টের তারিখ</label>
                                            <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="payment_note" class="form-label">পেমেন্ট নোট</label>
                                        <textarea class="form-control" id="payment_note" name="payment_note" rows="2" placeholder="পেমেন্ট সম্পর্কে নোট লিখুন (ঐচ্ছিক)"></textarea>
                                    </div>
                                    
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="print_receipt" name="print_receipt">
                                        <label class="form-check-label" for="print_receipt">পেমেন্টের রসিদ প্রিন্ট করুন</label>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-check-circle me-2"></i> পেমেন্ট নিশ্চিত করুন
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable for searchable tables
            $('.datatable').DataTable({
                "language": {
                    "lengthMenu": "প্রতি পেজে _MENU_ রেকর্ড দেখুন",
                    "zeroRecords": "কোন রেকর্ড পাওয়া যায়নি",
                    "info": "পেজ _PAGE_ / _PAGES_",
                    "infoEmpty": "কোন রেকর্ড পাওয়া যায়নি",
                    "infoFiltered": "(মোট _MAX_ রেকর্ড থেকে ফিল্টার করা হয়েছে)",
                    "search": "সার্চ করুন:",
                    "paginate": {
                        "first": "প্রথম",
                        "last": "শেষ",
                        "next": "পরবর্তী",
                        "previous": "আগের"
                    }
                }
            });
            
            // Check if view_fee parameter is present
            const urlParams = new URLSearchParams(window.location.search);
            const viewFeeId = urlParams.get('view_fee');
            
            if (viewFeeId) {
                // Find the fee data in the table
                const feeRow = $(`button.view-btn[data-fee-id="${viewFeeId}"]`);
                
                if (feeRow.length > 0) {
                    // Simulate a click on the view button
                    feeRow.click();
                } else {
                    // If not found in current page, load fee details via AJAX
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_fee_details',
                            fee_id: viewFeeId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.status === 'success' && response.fee) {
                                const fee = response.fee;
                                
                                // Set modal data
                                $('#fee_id').val(fee.id);
                                $('#student_name').text(fee.student_name);
                                $('#student_id').text(fee.student_roll);
                                $('#class_name').text(fee.class_name);
                                $('#fee_type').text(fee.fee_type);
                                $('#amount').text('৳ ' + parseFloat(fee.amount).toFixed(2));
                                $('#paid').text('৳ ' + parseFloat(fee.paid).toFixed(2));
                                $('#due').text('৳ ' + parseFloat(fee.amount - fee.paid).toFixed(2));
                                $('#due_date').text(fee.due_date);
                                
                                let statusText = '';
                                let statusClass = '';
                                
                                if(fee.payment_status === 'paid') {
                                    statusText = 'পরিশোধিত';
                                    statusClass = 'text-success';
                                } else if(fee.payment_status === 'partial') {
                                    statusText = 'আংশিক';
                                    statusClass = 'text-warning';
                                } else {
                                    statusText = 'বকেয়া';
                                    statusClass = 'text-danger';
                                }
                                
                                $('#status').text(statusText).removeClass('text-success text-warning text-danger').addClass(statusClass);
                                
                                // Load payment history
                                $.ajax({
                                    url: 'ajax_handler.php',
                                    type: 'POST',
                                    data: {
                                        action: 'get_fee_payment_history',
                                        fee_id: viewFeeId
                                    },
                                    dataType: 'json',
                                    success: function(historyResponse) {
                                        if(historyResponse.status === 'success') {
                                            const isMonthlyFee = fee.fee_type.includes('মাসিক বেতন');
                                            
                                            // Populate payment history
                                            if(historyResponse.payments && historyResponse.payments.length > 0) {
                                                let paymentHistoryHtml = '';
                                                
                                                historyResponse.payments.forEach(function(payment, index) {
                                                    paymentHistoryHtml += `
                                                        <tr>
                                                            <td>${index + 1}</td>
                                                            <td>${payment.payment_date}</td>
                                                            <td>৳ ${parseFloat(payment.amount).toFixed(2)}</td>
                                                            <td>${payment.payment_method || '-'}</td>
                                                        </tr>
                                                    `;
                                                });
                                                
                                                $('#payment_history_table').html(paymentHistoryHtml);
                                            } else {
                                                $('#payment_history_table').html('<tr><td colspan="4" class="text-center text-muted">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td></tr>');
                                            }
                                        } else {
                                            $('#payment_history_table').html('<tr><td colspan="4" class="text-center text-danger">পেমেন্ট ইতিহাস লোড করতে সমস্যা হয়েছে!</td></tr>');
                                        }
                                    },
                                    error: function() {
                                        $('#payment_history_table').html('<tr><td colspan="4" class="text-center text-danger">পেমেন্ট ইতিহাস লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।</td></tr>');
                                    }
                                });
                                
                                // Show the view fee modal
                                $('#viewFeeModal').modal('show');
                            } else {
                                alert('ফি বিবরণ লোড করতে সমস্যা হয়েছে!');
                            }
                        },
                        error: function() {
                            alert('ফি বিবরণ লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        }
                    });
                }
            }
            
            // Initialize chart
            const statusCtx = document.getElementById('paymentStatusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['পরিশোধিত', 'আংশিক', 'বকেয়া'],
                    datasets: [{
                        data: [
                            <?= $stats['paid_count'] ?? 0 ?>, 
                            <?= $stats['partial_count'] ?? 0 ?>, 
                            <?= $stats['due_count'] ?? 0 ?>
                        ],
                        backgroundColor: ['#198754', '#ffc107', '#dc3545'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Fee Collection - Session Change
            $('#collection_session_id').change(function() {
                const sessionId = $(this).val();
                if(sessionId) {
                    $('#collection_class_id').html('<option value="">লোড হচ্ছে...</option>');
                    $('#collection_class_id').prop('disabled', true);
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#collection_student_id').prop('disabled', true);
                    
                    // AJAX call to get classes by session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_classes_by_session',
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let classOptions = '<option value="">ক্লাস নির্বাচন করুন</option>';
                                if(response.classes.length > 0) {
                                    response.classes.forEach(function(cls) {
                                        classOptions += `<option value="${cls.id}">${cls.class_name}</option>`;
                                    });
                                } else {
                                    classOptions = '<option value="">কোন ক্লাস পাওয়া যায়নি</option>';
                                }
                                $('#collection_class_id').html(classOptions);
                                $('#collection_class_id').prop('disabled', false);
                            } else {
                                alert('ক্লাস লোড করতে সমস্যা হয়েছে!');
                            }
                        },
                        error: function() {
                            alert('ক্লাস লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        }
                    });
                } else {
                    $('#collection_class_id').html('<option value="">প্রথমে সেশন নির্বাচন করুন</option>');
                    $('#collection_class_id').prop('disabled', true);
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#collection_student_id').prop('disabled', true);
                }
            });
            
            // Fee Collection - Class Change
            $('#collection_class_id').change(function() {
                const classId = $(this).val();
                const sessionId = $('#collection_session_id').val();
                
                if(classId && sessionId) {
                    $('#collection_student_id').html('<option value="">লোড হচ্ছে...</option>');
                    $('#collection_student_id').prop('disabled', true);
                    
                    // AJAX call to get students by class and session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_students_by_class_session',
                            class_id: classId,
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let studentOptions = '<option value="">শিক্ষার্থী নির্বাচন করুন</option>';
                                if(response.students.length > 0) {
                                    response.students.forEach(function(student) {
                                        studentOptions += `<option value="${student.id}">${student.student_id} - ${student.first_name} ${student.last_name}</option>`;
                                    });
                                } else {
                                    studentOptions = '<option value="">কোন শিক্ষার্থী পাওয়া যায়নি</option>';
                                }
                                $('#collection_student_id').html(studentOptions);
                                $('#collection_student_id').prop('disabled', false);
                            } else {
                                alert('শিক্ষার্থী লোড করতে সমস্যা হয়েছে!');
                            }
                        },
                        error: function() {
                            alert('শিক্ষার্থী লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                        }
                    });
                } else {
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#collection_student_id').prop('disabled', true);
                }
            });
            
            // Fee Collection - Find Student Form Submit
            $('#findStudentForm').submit(function(e) {
                e.preventDefault();
                
                const sessionId = $('#collection_session_id').val();
                const classId = $('#collection_class_id').val();
                const selectionType = $('#student_selection_type').val();
                
                if(!sessionId || !classId) {
                    alert('সেশন এবং ক্লাস নির্বাচন করুন!');
                    return false;
                }
                
                // Prepare data based on student selection type
                let studentIds = [];
                let isAllStudents = false;
                
                if(selectionType === 'single') {
                    const studentId = $('#collection_student_id').val();
                    if(!studentId) {
                        alert('শিক্ষার্থী নির্বাচন করুন!');
                        return false;
                    }
                    studentIds.push(studentId);
                } else if(selectionType === 'multiple') {
                    $('.student-checkbox:checked').each(function() {
                        studentIds.push($(this).val());
                    });
                    
                    if(studentIds.length === 0) {
                        alert('কমপক্ষে একজন শিক্ষার্থী নির্বাচন করুন!');
                        return false;
                    }
                } else if(selectionType === 'all') {
                    isAllStudents = true;
                    // We'll get all students from the server based on class and session
                }
                
                // Send request to get fee information
                $.ajax({
                    url: 'ajax_handler.php',
                    type: 'POST',
                    data: {
                        action: 'get_student_fees',
                        session_id: sessionId,
                        class_id: classId,
                        student_ids: studentIds,
                        is_all_students: isAllStudents,
                        selection_type: selectionType
                    },
                    dataType: 'json',
                    success: function(response) {
                        // Handle response
                        // Existing code to show student fees
                    },
                    error: function() {
                        alert('শিক্ষার্থীর ফি লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।');
                    }
                });
                
                return false; // Prevent default form submission
            });
            
            // Select All Fees checkbox
            $('#selectAllFees').change(function() {
                // Code for select all fees
            });
            
            // Load students when class is selected for fee collection
            $('#collection_class_id').change(function() {
                const classId = $(this).val();
                
                if(classId) {
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_students_by_class',
                            class_id: classId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Populate student dropdown
                                let options = '<option value="">শিক্ষার্থী নির্বাচন করুন</option>';
                                response.students.forEach(function(student) {
                                    options += `<option value="${student.id}">${student.student_id} - ${student.first_name} ${student.last_name}</option>`;
                                });
                                $('#collection_student_id').html(options).prop('disabled', false);
                                
                                // Enable student selection type
                                $('#student_selection_type').prop('disabled', false);
                            } else {
                                $('#collection_student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                                $('#studentListContainer').html('<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।</div>');
                            }
                        },
                        error: function() {
                            $('#collection_student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                            $('#studentListContainer').html('<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।</div>');
                        }
                    });
                } else {
                    // Reset and disable student dropdown
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#collection_student_id').prop('disabled', true);
                    $('#student_selection_type').prop('disabled', true);
                    
                    // Hide all student sections
                    $('#single_student_section, #multiple_students_section, #all_students_section').addClass('d-none');
                    $('#single_student_section').removeClass('d-none');
                }
            });
            
            // Handle student selection type change
            $('#student_selection_type').change(function() {
                const selectedType = $(this).val();
                
                // Hide all student selection options
                $('#single_student_section, #multiple_students_section, #all_students_section').addClass('d-none');
                
                // Show the selected option
                if(selectedType === 'single') {
                    $('#single_student_section').removeClass('d-none');
                } else if(selectedType === 'multiple') {
                    $('#multiple_students_section').removeClass('d-none');
                } else if(selectedType === 'all') {
                    $('#all_students_section').removeClass('d-none');
                }
            });
            
            // ====== BULK FEE MODULE ======
            
            // Function to handle fee type selection
            $('#bulk_fee_type').change(function() {
                const selectedOption = $(this).find('option:selected');
                const amount = selectedOption.data('amount');
                
                if(amount && $(this).val() !== 'মাসিক বেতন') {
                    $('#bulk_amount').val(amount);
                } else {
                    $('#bulk_amount').val('');
                }
                
                // Show/hide monthly fee selector
                if($(this).val() === 'মাসিক বেতন') {
                    $('#monthly_fee_selector').show();
                    $('#bulk_amount').prop('disabled', true);
                } else {
                    $('#monthly_fee_selector').hide();
                    $('#bulk_amount').prop('disabled', false);
                }
            });
            
            // Function to handle student selection type change
            $('#student_selection_type').change(function() {
                const selectedType = $(this).val();
                
                // Hide all student selection options
                $('#single_student_section, #multiple_students_section, #all_students_section').addClass('d-none');
                
                // Show the selected option
                if(selectedType === 'single') {
                    $('#single_student_section').removeClass('d-none');
                } else if(selectedType === 'multiple') {
                    $('#multiple_students_section').removeClass('d-none');
                } else if(selectedType === 'all') {
                    $('#all_students_section').removeClass('d-none');
                }
            });
            
            // Function to load students when class is selected
            $('#bulk_class_id').change(function() {
                const classId = $(this).val();
                const sessionId = $('#bulk_session').val();
                
                if(classId) {
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_students_by_class',
                            class_id: classId,
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                // Update single student dropdown
                                let options = '<option value="">শিক্ষার্থী নির্বাচন করুন</option>';
                                response.students.forEach(function(student) {
                                    options += `<option value="${student.id}">${student.student_id} - ${student.first_name} ${student.last_name}</option>`;
                                });
                                $('#collection_student_id').html(options).prop('disabled', false);
                                
                                // Update multiple students list
                                let studentList = '';
                                response.students.forEach(function(student) {
                                    studentList += `
                                        <div class="form-check">
                                            <input class="form-check-input student-checkbox" type="checkbox" value="${student.id}" id="student_${student.id}">
                                            <label class="form-check-label" for="student_${student.id}">
                                                ${student.student_id} - ${student.first_name} ${student.last_name}
                                            </label>
                                        </div>
                                    `;
                                });
                                $('#studentListContainer').html(studentList);
                            } else {
                                $('#collection_student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                                $('#studentListContainer').html('<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।</div>');
                            }
                        },
                        error: function() {
                            $('#collection_student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                            $('#studentListContainer').html('<div class="text-center text-danger">শিক্ষার্থী লোড করতে সমস্যা হয়েছে! সার্ভারে সমস্যা।</div>');
                        }
                    });
                } else {
                    // Reset student dropdown if no class selected
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#studentListContainer').html('');
                }
            });
            
            // Function to handle session selection
            $('#bulk_session').change(function() {
                const sessionId = $(this).val();
                
                // Enable class dropdown when session is selected
                if(sessionId) {
                    $('#bulk_class_id').prop('disabled', false);
                } else {
                    $('#bulk_class_id').prop('disabled', true);
                    $('#bulk_class_id').val('');
                    $('#collection_student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#collection_student_id').prop('disabled', true);
                }
            });
        });
    </script>
</body>
</html>