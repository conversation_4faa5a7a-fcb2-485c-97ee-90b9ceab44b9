<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// This script will immediately clean up duplicate fees
$conn->begin_transaction();

try {
    // Step 1: Identify duplicates based on student_id and fee_type
    $sql = "
        SELECT student_id, fee_type, GROUP_CONCAT(id ORDER BY id ASC) as ids
        FROM fees
        GROUP BY student_id, fee_type 
        HAVING COUNT(*) > 1
    ";
    
    $result = $conn->query($sql);
    
    if (!$result) {
        throw new Exception("Error finding duplicates: " . $conn->error);
    }
    
    $duplicatesFound = $result->num_rows;
    $duplicatesRemoved = 0;
    
    if ($duplicatesFound > 0) {
        while ($row = $result->fetch_assoc()) {
            $ids = explode(',', $row['ids']);
            $keepId = $ids[0]; // Keep the oldest entry
            
            // Step 2: Calculate the total paid amount across all duplicates
            $paidSql = "SELECT SUM(paid) as total_paid FROM fees WHERE id IN (" . $row['ids'] . ")";
            $paidResult = $conn->query($paidSql);
            
            if (!$paidResult) {
                throw new Exception("Error calculating totals: " . $conn->error);
            }
            
            $paidRow = $paidResult->fetch_assoc();
            $totalPaid = $paidRow['total_paid'];
            
            // Step 3: Update the fee to keep with the total paid amount
            $updateSql = "
                UPDATE fees 
                SET paid = ?, 
                    payment_status = CASE 
                        WHEN ? >= amount THEN 'paid'
                        WHEN ? > 0 THEN 'partial'
                        ELSE 'due'
                    END
                WHERE id = ?
            ";
            
            $updateStmt = $conn->prepare($updateSql);
            $updateStmt->bind_param("dddi", $totalPaid, $totalPaid, $totalPaid, $keepId);
            $updateResult = $updateStmt->execute();
            
            if (!$updateResult) {
                throw new Exception("Error updating fee record: " . $updateStmt->error);
            }
            
            // Step 4: Delete the duplicates (all except the first one)
            array_shift($ids); // Remove the first ID which we are keeping
            
            if (!empty($ids)) {
                $deleteIds = implode(',', $ids);
                $deleteSql = "DELETE FROM fees WHERE id IN ($deleteIds)";
                
                $deleteResult = $conn->query($deleteSql);
                
                if (!$deleteResult) {
                    throw new Exception("Error deleting duplicates: " . $conn->error);
                }
                
                $duplicatesRemoved += count($ids);
            }
        }
    }
    
    // Commit changes if all went well
    $conn->commit();
    
    // Success status
    $success = true;
    $message = "ডুপ্লিকেট ফি সফলভাবে ফিক্স করা হয়েছে!";
    
} catch (Exception $e) {
    // Roll back in case of errors
    $conn->rollback();
    $success = false;
    $message = "ডুপ্লিকেট ফি ফিক্স করতে সমস্যা: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডুপ্লিকেট ফি ফিক্স করুন - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header <?= $success ? 'bg-success' : 'bg-danger' ?> text-white">
                        <h3 class="card-title mb-0">
                            <?php if ($success): ?>
                                <i class="fas fa-check-circle me-2"></i> ডুপ্লিকেট ফি ফিক্স করা হয়েছে
                            <?php else: ?>
                                <i class="fas fa-exclamation-triangle me-2"></i> ত্রুটি!
                            <?php endif; ?>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert <?= $success ? 'alert-success' : 'alert-danger' ?>">
                            <p class="mb-0"><?= $message ?></p>
                        </div>
                        
                        <?php if ($success): ?>
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title">ডুপ্লিকেট শিক্ষার্থী</h5>
                                            <h2 class="text-primary"><?= $duplicatesFound ?></h2>
                                            <p class="text-muted">মোট শিক্ষার্থী সংখ্যা যাদের ডুপ্লিকেট ফি ছিল</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h5 class="card-title">অপসারণ করা ডুপ্লিকেট</h5>
                                            <h2 class="text-success"><?= $duplicatesRemoved ?></h2>
                                            <p class="text-muted">মোট ডুপ্লিকেট ফি রেকর্ড অপসারণ করা হয়েছে</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <div class="text-center mt-4">
                            <a href="fees.php" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-1"></i> ফি মেনুতে ফিরে যান
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 