<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
if (!$studentId) {
    echo "Invalid student ID.";
    exit();
}

// Get filter parameters
$includeAllPayments = isset($_GET['include_all_payments']) && $_GET['include_all_payments'] == 1;
$paymentIds = isset($_GET['payment_ids']) ? $_GET['payment_ids'] : [];

// Get student info
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows == 0) {
    echo "Student not found.";
    exit();
}

$student = $studentResult->fetch_assoc();

// Get payments
$paymentsQuery = "SELECT fp.*, f.fee_type, f.due_date, f.amount as total_fee_amount
                FROM fee_payments fp
                JOIN fees f ON fp.fee_id = f.id
                WHERE f.student_id = ?";

if (!$includeAllPayments && !empty($paymentIds)) {
    $placeholders = str_repeat('?,', count($paymentIds) - 1) . '?';
    $paymentsQuery .= " AND fp.id IN ($placeholders)";
}

$paymentsQuery .= " ORDER BY fp.payment_date DESC";

$stmt = $conn->prepare($paymentsQuery);

if (!$includeAllPayments && !empty($paymentIds)) {
    $types = str_repeat('i', count($paymentIds) + 1);
    $params = array_merge([$studentId], $paymentIds);
    $stmt->bind_param($types, ...$params);
} else {
    $stmt->bind_param("i", $studentId);
}

$stmt->execute();
$payments = $stmt->get_result();

// Calculate total
$totalAmount = 0;
$paymentsData = [];
while ($payment = $payments->fetch_assoc()) {
    $totalAmount += $payment['amount'];
    $paymentsData[] = $payment;
}

// Get school settings if available
$schoolSettings = [];
$schoolQuery = "SELECT * FROM school_settings LIMIT 1";
$schoolResult = $conn->query($schoolQuery);
if ($schoolResult && $schoolResult->num_rows > 0) {
    $schoolSettings = $schoolResult->fetch_assoc();
}

// Generate receipt number with prefix FP (Fee Payment), current year, month and a random number
$receiptNo = 'FP-' . date('Ym') . '-' . sprintf('%04d', rand(1, 9999));

// Format payment date
$receiptDate = date('d/m/Y');

// Function to convert number to Bengali words
function convertNumberToBengaliWords($number) {
    $number = (float) $number;
    
    $bengaliDigits = [
        '০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'
    ];
    
    $words = '';
    $strNumber = (string) $number;
    
    for ($i = 0; $i < strlen($strNumber); $i++) {
        $digit = $strNumber[$i];
        if ($digit == '.') {
            $words .= '.';
        } else {
            $words .= $bengaliDigits[(int) $digit];
        }
    }
    
    return $words;
}

// Format the amount for displaying
$formattedAmount = number_format($totalAmount, 2);
$amountInWords = convertNumberToBengaliWords($totalAmount);

// Get all unique fee types in the selected payments
$feeTypes = [];
foreach ($paymentsData as $payment) {
    if (!in_array($payment['fee_type'], $feeTypes)) {
        $feeTypes[] = $payment['fee_type'];
    }
}

// Fee type text
$feeTypeText = implode(', ', $feeTypes);

// Function to convert English number to Bengali
function convertToBengaliNumber($number) {
    $bengaliDigits = ['০', '১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯'];
    $englishNumber = (string) $number;
    $bengaliNumber = '';
    
    for ($i = 0; $i < strlen($englishNumber); $i++) {
        if (is_numeric($englishNumber[$i])) {
            $bengaliNumber .= $bengaliDigits[(int)$englishNumber[$i]];
        } else {
            $bengaliNumber .= $englishNumber[$i];
        }
    }
    
    return $bengaliNumber;
}

// Format date to Bengali
function formatDateToBengali($date) {
    if (empty($date)) return '';
    
    $timestamp = strtotime($date);
    $day = convertToBengaliNumber(date('d', $timestamp));
    $month = convertToBengaliNumber(date('m', $timestamp));
    $year = convertToBengaliNumber(date('Y', $timestamp));
    
    return "$day/$month/$year";
}

// Format Bengali month name
function getBengaliMonthName($month) {
    $bengaliMonths = [
        1 => 'জানুয়ারি', 
        2 => 'ফেব্রুয়ারি', 
        3 => 'মার্চ', 
        4 => 'এপ্রিল',
        5 => 'মে', 
        6 => 'জুন', 
        7 => 'জুলাই', 
        8 => 'আগস্ট',
        9 => 'সেপ্টেম্বর', 
        10 => 'অক্টোবর', 
        11 => 'নভেম্বর', 
        12 => 'ডিসেম্বর'
    ];
    
    return $bengaliMonths[$month] ?? '';
}

// School name and address from settings or fallback to defaults
$schoolName = isset($schoolSettings['school_name']) ? $schoolSettings['school_name'] : 'আজমগঞ্জ ফাজিল মাদ্রাসা';
$schoolAddress = isset($schoolSettings['school_address']) ? $schoolSettings['school_address'] : 'আজমগঞ্জ, সিলেট';
$schoolPhone = isset($schoolSettings['school_phone']) ? $schoolSettings['school_phone'] : '';
$schoolEmail = isset($schoolSettings['school_email']) ? $schoolSettings['school_email'] : '';
$schoolLogo = isset($schoolSettings['school_logo']) ? $schoolSettings['school_logo'] : '';

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট রিসিপ্ট</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        @page {
            size: A4;
            margin: 0;
        }
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .container-a4 {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            position: relative;
            padding: 20mm;
        }
        .receipt-header {
            text-align: center;
            border-bottom: 2px solid #0d6efd;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .school-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
        }
        .receipt-title {
            background-color: #0d6efd;
            color: white;
            padding: 10px 20px;
            display: inline-block;
            border-radius: 20px;
            margin: 15px 0;
        }
        .receipt-number {
            position: absolute;
            top: 20mm;
            right: 20mm;
            font-size: 14px;
            text-align: right;
        }
        .receipt-date {
            position: absolute;
            top: 30mm;
            right: 20mm;
            font-size: 14px;
            text-align: right;
        }
        .student-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .payment-details {
            margin-bottom: 30px;
        }
        .payment-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .payment-table th, .payment-table td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        .payment-table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        .payment-total {
            text-align: right;
            font-weight: 700;
            font-size: 18px;
            color: #0d6efd;
            margin-top: 15px;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .amount-in-words {
            font-style: italic;
            margin-bottom: 25px;
            padding: 10px;
            background-color: #f0f8ff;
            border-left: 3px solid #0d6efd;
            border-radius: 5px;
        }
        .receipt-footer {
            margin-top: 50px;
            text-align: center;
            padding-top: 20px;
            border-top: 1px dashed #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }
        .signature-area {
            display: flex;
            justify-content: space-between;
            margin-top: 80px;
        }
        .signature-line {
            width: 180px;
            border-top: 1px solid #000;
            margin: 0 auto 5px auto;
        }
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            opacity: 0.05;
            font-size: 100px;
            color: #000;
            z-index: 0;
            pointer-events: none;
        }
        .barcode-area {
            text-align: center;
            margin-top: 30px;
        }
        .barcode {
            padding: 10px;
            background: #f8f9fa;
            display: inline-block;
            border-radius: 5px;
        }
        @media print {
            body {
                background: white;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .container-a4 {
                box-shadow: none;
                margin: 0;
                padding: 20mm;
                width: 100%;
            }
            .no-print {
                display: none !important;
            }
            .receipt-title {
                background-color: #0d6efd !important;
                color: white !important;
            }
            a {
                text-decoration: none;
                color: inherit;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-4 no-print">
        <div class="d-flex justify-content-between mb-4">
            <a href="fee_reports.php?tab=student-receipt&student_id=<?= $studentId ?>" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i> ফিরে যান
            </a>
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print me-2"></i> প্রিন্ট করুন
            </button>
        </div>
    </div>
    
    <div class="container-a4">
        <!-- Watermark -->
        <div class="watermark"><?= $schoolName ?></div>
        
        <!-- Receipt Header -->
        <div class="receipt-header">
            <?php if (!empty($schoolLogo)): ?>
            <img src="<?= htmlspecialchars($schoolLogo) ?>" alt="School Logo" class="school-logo">
            <?php endif; ?>
            <h1 class="mb-1"><?= htmlspecialchars($schoolName) ?></h1>
            <p class="mb-1"><?= htmlspecialchars($schoolAddress) ?></p>
            <?php if (!empty($schoolPhone) || !empty($schoolEmail)): ?>
            <p class="mb-2">
                <?php if (!empty($schoolPhone)): ?>
                <span class="me-3"><i class="fas fa-phone-alt me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span>
                <?php endif; ?>
                <?php if (!empty($schoolEmail)): ?>
                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
                <?php endif; ?>
            </p>
            <?php endif; ?>
            <div class="receipt-title">
                <h4 class="mb-0">পেমেন্ট রসিদ</h4>
            </div>
        </div>
        
        <!-- Receipt Number and Date -->
        <div class="receipt-number">
            <p><strong>রসিদ নং:</strong> <?= htmlspecialchars($receiptNo) ?></p>
        </div>
        <div class="receipt-date">
            <p><strong>তারিখ:</strong> <?= formatDateToBengali(date('Y-m-d')) ?></p>
        </div>
        
        <!-- Student Information -->
        <div class="student-info">
            <div class="row">
                <div class="col-6">
                    <p><strong>শিক্ষার্থী আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                    <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                    <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                </div>
                <div class="col-6">
                    <p><strong>শ্রেণী:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                    <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                    <p><strong>ফোন:</strong> <?= htmlspecialchars($student['phone'] ?? 'N/A') ?></p>
                </div>
            </div>
        </div>
        
        <!-- Payment Details -->
        <div class="payment-details">
            <h5 class="mb-3">পেমেন্ট বিবরণ:</h5>
            <table class="payment-table">
                <thead>
                    <tr>
                        <th width="5%">ক্রম</th>
                        <th width="35%">ফি প্রকার</th>
                        <th width="20%">বকেয়া তারিখ</th>
                        <th width="20%">পেমেন্ট তারিখ</th>
                        <th width="20%" class="text-end">পরিমাণ (৳)</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $i = 1; foreach ($paymentsData as $payment): ?>
                    <tr>
                        <td><?= convertToBengaliNumber($i++) ?></td>
                        <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                        <td><?= formatDateToBengali($payment['due_date']) ?></td>
                        <td><?= formatDateToBengali($payment['payment_date']) ?></td>
                        <td class="text-end"><?= convertToBengaliNumber(number_format($payment['amount'], 2)) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <div class="payment-total">
                মোট পেমেন্ট: <?= convertToBengaliNumber(number_format($totalAmount, 2)) ?> ৳
            </div>
            
            <div class="amount-in-words">
                <strong>কথায়:</strong> <?= $amountInWords ?> টাকা মাত্র
            </div>
        </div>
        
        <!-- Signature Area -->
        <div class="signature-area">
            <div class="text-center">
                <div class="signature-line"></div>
                <p>শিক্ষার্থীর স্বাক্ষর</p>
            </div>
            
            <div class="text-center">
                <div class="signature-line"></div>
                <p>প্রাপ্তকর্তার স্বাক্ষর</p>
            </div>
            
            <div class="text-center">
                <div class="signature-line"></div>
                <p>অনুমোদনকারী</p>
            </div>
        </div>
        
        <!-- Barcode/QR Code Area (for verification) -->
        <div class="barcode-area">
            <div class="barcode">
                <?= $receiptNo ?>
            </div>
        </div>
        
        <!-- Receipt Footer -->
        <div class="receipt-footer">
            <p>এই রসিদটি কম্পিউটার দ্বারা তৈরি করা হয়েছে এবং এটি বৈধ দলিল হিসাবে বিবেচিত হবে।</p>
            <p>&copy; <?= date('Y') ?> <?= htmlspecialchars($schoolName) ?></p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 