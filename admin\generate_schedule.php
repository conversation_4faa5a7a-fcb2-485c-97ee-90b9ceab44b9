<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get POST data
$start_date = isset($_POST['start_date']) ? $_POST['start_date'] : date('Y-m-d');
$end_date = isset($_POST['end_date']) ? $_POST['end_date'] : date('Y-m-d', strtotime('+30 days'));
$class_id = isset($_POST['class_id']) ? $_POST['class_id'] : '';
$subject_filter = isset($_POST['subject_filter']) ? $_POST['subject_filter'] : '';
$format = isset($_POST['format']) ? $_POST['format'] : 'html';
$title = isset($_POST['title']) ? $_POST['title'] : 'পরীক্ষার সিডিউল';
$include_instructions = isset($_POST['include_instructions']) ? true : false;
$instructions = isset($_POST['instructions']) ? $_POST['instructions'] : '';

// Build the query
$query = "SELECT e.*, c.class_name FROM exams e 
          JOIN classes c ON e.class_id = c.id 
          WHERE e.exam_date BETWEEN ? AND ?";

$params = [$start_date, $end_date];
$types = "ss";

// Add class filter if specified
if (!empty($class_id)) {
    $query .= " AND e.class_id = ?";
    $params[] = $class_id;
    $types .= "i";
}

// Add subject filter if specified
if (!empty($subject_filter)) {
    $query .= " AND e.course_name = ?";
    $params[] = $subject_filter;
    $types .= "s";
}

$query .= " ORDER BY e.exam_date ASC, c.class_name ASC, e.course_name ASC";

// Execute the query
$stmt = $conn->prepare($query);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$result = $stmt->get_result();

// Organize exams by date
$exams_by_date = [];
while ($exam = $result->fetch_assoc()) {
    $date = $exam['exam_date'];
    if (!isset($exams_by_date[$date])) {
        $exams_by_date[$date] = [];
    }
    $exams_by_date[$date][] = $exam;
}

// Generate schedule
if ($format === 'pdf') {
    // If PDF is selected but TCPDF is not available, fallback to HTML
    if (!class_exists('TCPDF')) {
        $format = 'html';
    }
}

// HTML output
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }
        .schedule-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        .school-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .schedule-title {
            font-size: 20px;
            color: #007bff;
            margin-bottom: 10px;
        }
        .date-range {
            font-size: 16px;
            color: #6c757d;
        }
        .subject-filter {
            display: inline-block;
            background-color: #e7f3ff;
            padding: 2px 8px;
            border-radius: 4px;
            color: #0d6efd;
            font-weight: bold;
        }
        .date-header {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 10px 15px;
            margin: 20px 0 10px 0;
            font-size: 18px;
            font-weight: bold;
        }
        .exam-row {
            margin-bottom: 15px;
            padding: 10px;
            border-bottom: 1px solid #e9ecef;
        }
        .exam-row:last-child {
            border-bottom: none;
        }
        .exam-name {
            font-weight: bold;
        }
        .exam-details {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: 5px;
        }
        .exam-detail {
            flex: 1;
            min-width: 150px;
            padding: 5px 10px;
        }
        .instructions {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .instructions h3 {
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .instructions ul {
            padding-left: 20px;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        @media print {
            body {
                font-size: 12pt;
            }
            .no-print {
                display: none !important;
            }
            .schedule-container {
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
            }
            .page-break {
                page-break-after: always;
            }
        }
    </style>
</head>
<body>
    <div class="schedule-container">
        <div class="header">
            <div class="school-name">স্কুল ম্যানেজমেন্ট সিস্টেম</div>
            <div class="schedule-title"><?php echo $title; ?></div>
            <div class="date-range">
                <?php echo date('d F Y', strtotime($start_date)) . ' থেকে ' . date('d F Y', strtotime($end_date)); ?>
                <?php if (!empty($subject_filter)): ?>
                <div class="mt-1">বিষয়: <span class="subject-filter"><?php echo $subject_filter; ?></span></div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="no-print mb-4">
            <button class="btn btn-primary" onclick="window.print()">প্রিন্ট করুন</button>
            <a href="exam_management.php" class="btn btn-secondary">ফিরে যান</a>
        </div>
        
        <?php if (empty($exams_by_date)): ?>
            <div class="alert alert-info">
                নির্বাচিত সময়ে কোনো পরীক্ষা নেই।
            </div>
        <?php else: ?>
            <?php foreach ($exams_by_date as $date => $exams): ?>
                <div class="date-header">
                    <?php echo date('d F Y (l)', strtotime($date)); ?>
                </div>
                
                <?php foreach ($exams as $exam): ?>
                    <div class="exam-row">
                        <div class="exam-name">
                            <?php echo $exam['exam_name']; ?>
                            <?php if ($exam['status'] !== 'active'): ?>
                                <span class="badge bg-danger">নিষ্ক্রিয়</span>
                            <?php endif; ?>
                        </div>
                        <div class="exam-details">
                            <div class="exam-detail">
                                <strong>শ্রেণী:</strong> <?php echo $exam['class_name']; ?>
                            </div>
                            <div class="exam-detail">
                                <strong>বিষয়:</strong> 
                                <?php if (!empty($subject_filter) && $exam['course_name'] === $subject_filter): ?>
                                <span class="subject-filter"><?php echo $exam['course_name']; ?></span>
                                <?php else: ?>
                                <?php echo $exam['course_name']; ?>
                                <?php endif; ?>
                            </div>
                            <div class="exam-detail">
                                <strong>মোট নম্বর:</strong> <?php echo $exam['total_marks']; ?>
                            </div>
                        </div>
                        <?php if (!empty($exam['description'])): ?>
                            <div class="exam-description mt-2 small">
                                <em><?php echo $exam['description']; ?></em>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <?php if ($include_instructions): ?>
            <div class="instructions">
                <h3>পরীক্ষার নিয়মাবলী</h3>
                <?php if (!empty($instructions)): ?>
                    <div class="custom-instructions">
                        <?php echo nl2br(htmlspecialchars($instructions)); ?>
                    </div>
                <?php else: ?>
                    <ul>
                        <li>সকল ছাত্র-ছাত্রীকে পরীক্ষা শুরুর ৩০ মিনিট আগে উপস্থিত থাকতে হবে।</li>
                        <li>পরীক্ষার সময় মোবাইল ফোন ব্যবহার নিষিদ্ধ।</li>
                        <li>প্রতিটি পরীক্ষায় সর্বনিম্ন ৩৩% নম্বর পেতে হবে পাশ করার জন্য।</li>
                        <li>পরীক্ষার হলে কোনো প্রকার অসদাচরণ করা হলে পরীক্ষা বাতিল করা হবে।</li>
                        <li>প্রয়োজনীয় উপকরণ (কলম, পেন্সিল, ক্যালকুলেটর, ইত্যাদি) সাথে আনতে হবে।</li>
                    </ul>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <div class="footer">
            <p>এই সিডিউল তৈরি করা হয়েছে: <?php echo date('d/m/Y H:i'); ?></p>
            <p>© <?php echo date('Y'); ?> স্কুল ম্যানেজমেন্ট সিস্টেম</p>
        </div>
    </div>
</body>
</html>
<?php
// Close connections
$stmt->close();
$conn->close();
?> 