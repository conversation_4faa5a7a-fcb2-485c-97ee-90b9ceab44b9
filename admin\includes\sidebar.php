<?php
// Get the current page filename
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="position-sticky pt-3">
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'dashboard.php') ? 'active' : ''; ?>" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'students.php') ? 'active' : ''; ?>" href="students.php">
                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'teachers.php') ? 'active' : ''; ?>" href="teachers.php">
                    <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'classes.php') ? 'active' : ''; ?>" href="classes.php">
                    <i class="fas fa-school me-2"></i> শ্রেণী
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'subjects.php') ? 'active' : ''; ?>" href="subjects.php">
                    <i class="fas fa-book me-2"></i> বিষয়
                </a>
            </li>
            <!-- পরিচালনা পর্ষদ মেনু আইটেম -->
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'committee.php') ? 'active' : ''; ?>" href="committee.php">
                    <i class="fas fa-users me-2"></i> পরিচালনা পর্ষদ
                </a>
            </li>
            <!-- পরীক্ষা সংক্রান্ত মেনু আইটেম -->
      <li class="nav-item">
          <a class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_') !== false || strpos($_SERVER['REQUEST_URI'], 'passing_marks_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_passing_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_minimum_pass.php') !== false) ? 'active' : 'collapsed'; ?>" href="#" data-bs-toggle="collapse" data-bs-target="#examSubmenu" aria-expanded="<?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_') !== false || strpos($_SERVER['REQUEST_URI'], 'passing_marks_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_passing_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_minimum_pass.php') !== false) ? 'true' : 'false'; ?>">
              <i class="fas fa-file-alt"></i>
              <span>পরীক্ষা ব্যবস্থাপনা</span>
              <i class="fas fa-angle-down float-end mt-1"></i>
          </a>
          <div class="collapse <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_') !== false || strpos($_SERVER['REQUEST_URI'], 'passing_marks_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_passing_config.php') !== false || strpos($_SERVER['REQUEST_URI'], 'subject_minimum_pass.php') !== false) ? 'show' : ''; ?>" id="examSubmenu">
              <ul class="btn-toggle-nav list-unstyled fw-normal small">
                  <li><a href="exam_dashboard.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_dashboard.php') !== false || strpos($_SERVER['REQUEST_URI'], 'exam_management.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-tachometer-alt me-2"></i>ড্যাশবোর্ড</a></li>
                  <li><a href="create_exam.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'create_exam.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-plus-circle me-2"></i>নতুন পরীক্ষা</a></li>
                  <li><a href="manage_exams.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'manage_exams.php') !== false || strpos($_SERVER['REQUEST_URI'], 'edit_exam.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-edit me-2"></i>পরীক্ষা সম্পাদনা</a></li>
                  <li><a href="exam_subject_assign.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_subject_assign.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-tasks me-2"></i>বিষয় বরাদ্দকরণ</a></li>
                  <li><a href="marks_entry.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'marks_entry.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-pen-alt me-2"></i>নম্বর এন্ট্রি</a></li>
                  <li><a href="exam_timetable.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_timetable.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-calendar-alt me-2"></i>রুটিন</a></li>
                  <li><a href="exam_schedule.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'exam_schedule.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-calendar-alt me-2"></i>পরীক্ষার সময়সূচি</a></li>
                  <li><a href="passing_marks_config.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'passing_marks_config.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-cogs me-2"></i>গ্রেডিং কনফিগারেশন</a></li>
                  <li><a href="subject_passing_config.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'subject_passing_config.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-wrench me-2"></i>বিষয় ভিত্তিক পাসিং কনফিগ</a></li>
                  <li><a href="subject_minimum_pass.php" class="nav-link <?php echo (strpos($_SERVER['REQUEST_URI'], 'subject_minimum_pass.php') !== false) ? 'active' : ''; ?>"><i class="fas fa-check-circle me-2"></i>ন্যূনতম পাস মার্কস</a></li>
                </ul>
          </div>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'notices.php') ? 'active' : ''; ?>" href="notices.php">
                    <i class="fas fa-bullhorn me-2"></i> নোটিশ
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'fees.php') ? 'active' : ''; ?>" href="fees.php">
                    <i class="fas fa-money-bill-alt me-2"></i> ফি সমূহ
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($currentPage == 'settings.php') ? 'active' : ''; ?>" href="settings.php">
                    <i class="fas fa-cog me-2"></i> সেটিংস
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="../includes/logout.inc.php">
                    <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                </a>
            </li>
      <li class="nav-item">
          <a href="student_marksheet.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'student_marksheet.php') ? 'active' : ''; ?>">
              <i class="fas fa-file-alt me-2"></i> ছাত্র/ছাত্রী মার্কশীট
          </a>
      </li>
      <li class="nav-item">
          <a href="tabulation_sheet.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'tabulation_sheet.php') ? 'active' : ''; ?>">
              <i class="fas fa-table me-2"></i> টেবুলেশন শীট
          </a>
      </li>
        </ul>
    </div>
</nav> 