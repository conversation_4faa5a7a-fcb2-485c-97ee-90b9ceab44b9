<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Delete exam if requested
if (isset($_GET['delete_id']) && !empty($_GET['delete_id'])) {
    $delete_id = $_GET['delete_id'];
    
    // Get exam name for confirmation message
    $get_exam_name = "SELECT exam_name FROM exams WHERE id = ?";
    $stmt = $conn->prepare($get_exam_name);
    $stmt->bind_param("i", $delete_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $exam_data = $result->fetch_assoc();
    $exam_name = $exam_data ? $exam_data['exam_name'] : '';
    
    // Delete the exam
    $delete_sql = "DELETE FROM exams WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $delete_id);
    
    if ($stmt->execute()) {
        $success_message = "পরীক্ষা \"$exam_name\" সফলভাবে মুছে ফেলা হয়েছে।";
    } else {
        $error_message = "পরীক্ষা মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Get filters if submitted
$filter_class_id = isset($_GET['class_id']) ? $_GET['class_id'] : '';
$filter_exam_type = isset($_GET['exam_type']) ? $_GET['exam_type'] : '';
$filter_status = isset($_GET['status']) ? $_GET['status'] : '';
$search_query = isset($_GET['search']) ? $_GET['search'] : '';

// Get all exams with filters
$exams_sql = "SELECT e.* 
              FROM exams e 
              WHERE 1=1";

$params = [];
$types = "";

if (!empty($filter_class_id)) {
    // Only add this filter if class_id exists in the table
    $check_class_id = $conn->query("SHOW COLUMNS FROM exams LIKE 'class_id'");
    if ($check_class_id->num_rows > 0) {
        $exams_sql .= " AND e.class_id = ?";
        $params[] = $filter_class_id;
        $types .= "i";
    }
}

if (!empty($filter_exam_type)) {
    // Only add this filter if exam_type exists in the table
    $check_exam_type = $conn->query("SHOW COLUMNS FROM exams LIKE 'exam_type'");
    if ($check_exam_type->num_rows > 0) {
        $exams_sql .= " AND e.exam_type = ?";
        $params[] = $filter_exam_type;
        $types .= "s";
    }
}

if (!empty($filter_status)) {
    // Only add this filter if status exists in the table
    $check_status = $conn->query("SHOW COLUMNS FROM exams LIKE 'status'");
    if ($check_status->num_rows > 0) {
        $exams_sql .= " AND e.status = ?";
        $params[] = $filter_status;
        $types .= "s";
    }
}

if (!empty($search_query)) {
    $exams_sql .= " AND (e.exam_name LIKE ? OR e.course_name LIKE ?)";
    $search_param = "%$search_query%";
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ss";
}

$exams_sql .= " ORDER BY e.exam_date DESC";

$stmt = $conn->prepare($exams_sql);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$exams_result = $stmt->get_result();

// Get classes for filter dropdown
$check_classes_table = $conn->query("SHOW TABLES LIKE 'classes'");
if ($check_classes_table->num_rows > 0) {
    $classes_sql = "SELECT * FROM classes ORDER BY class_name";
    $classes_result = $conn->query($classes_sql);
} else {
    // If classes table doesn't exist, create an empty result
    $classes_result = false;
}

// Array of exam types for filter dropdown
$exam_types = [
    'সামায়িক', 'অর্ধ-বার্ষিক', 'বার্ষিক', 'মডেল টেস্ট', 'নির্বাচনী', 'সাপ্তাহিক', 'মাসিক', 'অন্যান্য'
];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষা ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .filter-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
        }
        .exam-card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 15px;
            transition: all 0.3s ease;
            border-left: 3px solid #007bff;
        }
        .exam-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .exam-card .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .exam-card .card-body {
            padding: 15px 20px;
        }
        .exam-card .status-badge {
            padding: 4px 8px;
            border-radius: 30px;
            font-size: 0.75rem;
        }
        .exam-card .status-active {
            background-color: #d4edda;
            color: #155724;
        }
        .exam-card .status-inactive {
            background-color: #f8d7da;
            color: #721c24;
        }
        .exam-card .exam-details {
            margin-top: 10px;
            display: flex;
            flex-wrap: wrap;
        }
        .exam-card .exam-details > div {
            margin-right: 20px;
            margin-bottom: 10px;
        }
        .exam-card .action-buttons {
            display: flex;
            margin-top: 15px;
        }
        .exam-card .action-buttons .btn {
            margin-right: 8px;
        }
        .search-input {
            position: relative;
        }
        .search-input .form-control {
            padding-left: 35px;
        }
        .search-input i {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .no-exams {
            text-align: center;
            padding: 40px 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .no-exams i {
            font-size: 48px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        .no-exams p {
            font-size: 1.1rem;
            color: #495057;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পরীক্ষা ব্যবস্থাপনা</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="exam_dashboard.php" class="btn btn-sm btn-outline-primary me-2">
                            <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                        </a>
                        <a href="create_exam.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> নতুন পরীক্ষা যোগ করুন
                        </a>
                    </div>
                </div>
                
                <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Filter Card -->
                <div class="filter-card">
                    <h5 class="mb-3"><i class="fas fa-filter me-2"></i>পরীক্ষা খুঁজুন ও ফিল্টার করুন</h5>
                    <form method="get" action="" class="row g-3">
                        <div class="col-md-3">
                            <?php if ($classes_result && $classes_result->num_rows > 0): ?>
                            <label for="class_id" class="form-label">শ্রেণী:</label>
                            <select class="form-select" id="class_id" name="class_id">
                                <option value="">সব শ্রেণী</option>
                                <?php 
                                while ($class = $classes_result->fetch_assoc()): 
                                ?>
                                    <option value="<?php echo $class['id']; ?>" <?php echo ($filter_class_id == $class['id']) ? 'selected' : ''; ?>>
                                        <?php echo $class['class_name']; ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-3">
                            <label for="exam_type" class="form-label">পরীক্ষার ধরন:</label>
                            <select class="form-select" id="exam_type" name="exam_type">
                                <option value="">সব ধরন</option>
                                <?php foreach ($exam_types as $type): ?>
                                    <option value="<?php echo $type; ?>" <?php echo ($filter_exam_type == $type) ? 'selected' : ''; ?>>
                                        <?php echo $type; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">স্ট্যাটাস:</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">সব স্ট্যাটাস</option>
                                <option value="active" <?php echo ($filter_status == 'active') ? 'selected' : ''; ?>>সক্রিয়</option>
                                <option value="inactive" <?php echo ($filter_status == 'inactive') ? 'selected' : ''; ?>>নিষ্ক্রিয়</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">সার্চ:</label>
                            <div class="search-input">
                                <i class="fas fa-search"></i>
                                <input type="text" class="form-control" id="search" name="search" placeholder="পরীক্ষার নাম বা বিষয়..." value="<?php echo $search_query; ?>">
                            </div>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> ফিল্টার করুন
                            </button>
                            <a href="manage_exams.php" class="btn btn-secondary ms-2">
                                <i class="fas fa-sync"></i> রিসেট করুন
                            </a>
                        </div>
                    </form>
                </div>
                
                <!-- Exams List -->
                <?php if ($exams_result->num_rows > 0): ?>
                    <?php while ($exam = $exams_result->fetch_assoc()): ?>
                        <div class="exam-card">
                            <div class="card-header">
                                <h5 class="mb-0"><?php echo $exam['exam_name']; ?></h5>
                                <span class="status-badge <?php echo (isset($exam['status']) && $exam['status'] == 'active') ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo (isset($exam['status']) && $exam['status'] == 'active') ? 'সক্রিয়' : 'নিষ্ক্রিয়'; ?>
                                </span>
                            </div>
                            <div class="card-body">
                                <div class="exam-details">
                                    <div>
                                        <strong><i class="fas fa-calendar-alt me-1"></i> তারিখ:</strong>
                                        <?php echo date('d F Y', strtotime($exam['exam_date'])); ?>
                                    </div>
                                    <div>
                                        <strong><i class="fas fa-graduation-cap me-1"></i> শ্রেণী:</strong>
                                        <?php echo isset($exam['class_name']) ? $exam['class_name'] : (isset($exam['course_name']) ? $exam['course_name'] : 'N/A'); ?>
                                    </div>
                                    <div>
                                        <strong><i class="fas fa-book me-1"></i> বিষয়:</strong>
                                        <?php echo $exam['course_name']; ?>
                                    </div>
                                    <div>
                                        <strong><i class="fas fa-star me-1"></i> মোট নম্বর:</strong>
                                        <?php echo $exam['total_marks']; ?>
                                    </div>
                                    <?php if (isset($exam['exam_type']) && !empty($exam['exam_type'])): ?>
                                    <div>
                                        <strong><i class="fas fa-tags me-1"></i> ধরন:</strong>
                                        <?php echo $exam['exam_type']; ?>
                                    </div>
                                    <?php endif; ?>
                                    <?php if (isset($exam['start_time']) && isset($exam['end_time']) && !empty($exam['start_time']) && !empty($exam['end_time'])): ?>
                                    <div>
                                        <strong><i class="fas fa-clock me-1"></i> সময়:</strong>
                                        <?php echo date('h:i A', strtotime($exam['start_time'])); ?> - <?php echo date('h:i A', strtotime($exam['end_time'])); ?>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="action-buttons">
                                    <a href="edit_exam.php?id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i> এডিট করুন
                                    </a>
                                    <a href="exam_subject_assign.php?exam_id=<?php echo $exam['id']; ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-book"></i> বিষয় সংযোজন
                                    </a>
                                    <a href="#" class="btn btn-sm btn-danger" onclick="confirmDelete(<?php echo $exam['id']; ?>, '<?php echo addslashes($exam['exam_name']); ?>')">
                                        <i class="fas fa-trash-alt"></i> মুছে ফেলুন
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="no-exams">
                        <i class="fas fa-search"></i>
                        <p>কোন পরীক্ষা খুঁজে পাওয়া যায়নি।</p>
                        <a href="create_exam.php" class="btn btn-primary mt-3">
                            <i class="fas fa-plus"></i> নতুন পরীক্ষা যোগ করুন
                        </a>
                    </div>
                <?php endif; ?>
                
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(examId, examName) {
            if (confirm('আপনি কি নিশ্চিত যে আপনি "' + examName + '" পরীক্ষা মুছে ফেলতে চান?')) {
                window.location.href = 'manage_exams.php?delete_id=' + examId;
            }
        }
        
        $(document).ready(function() {
            // Auto-hide success message after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
    </script>
</body>
</html> 