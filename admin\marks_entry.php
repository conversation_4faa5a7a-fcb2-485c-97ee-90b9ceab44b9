<?php
session_start();
require_once('../includes/dbh.inc.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create the marks table if it doesn't exist
$create_marks_table_sql = "CREATE TABLE IF NOT EXISTS marks (
    id INT AUTO_INCREMENT PRIMARY KEY, 
    exam_id INT NOT NULL,
    subject_id INT NOT NULL,
    student_id INT NOT NULL,
    cq_marks DECIMAL(10,2) DEFAULT 0,
    mcq_marks DECIMAL(10,2) DEFAULT 0,
    practical_marks DECIMAL(10,2) DEFAULT 0,
    total_marks DECIMAL(10,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX(exam_id),
    INDEX(subject_id),
    INDEX(student_id)
)";
$conn->query($create_marks_table_sql);

// Create student_subjects table if it doesn't exist
$create_student_subjects_sql = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    is_optional TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY student_subject_unique (student_id, subject_id),
    INDEX(student_id),
    INDEX(subject_id)
)";
$conn->query($create_student_subjects_sql);

// Get all exams with class join
// First check if status column exists in exams table
$check_status = $conn->query("SHOW COLUMNS FROM exams LIKE 'status'");
// Check if class_id column exists
$check_class_id = $conn->query("SHOW COLUMNS FROM exams LIKE 'class_id'");

if ($check_class_id->num_rows > 0) {
    // If class_id exists, join with classes table
    if ($check_status->num_rows > 0) {
        // If status column exists, include it in the query
        $exams_sql = "SELECT e.*, c.class_name 
                    FROM exams e 
                    JOIN classes c ON e.class_id = c.id 
                    WHERE e.status = 'active'
                    ORDER BY e.exam_date DESC";
    } else {
        // If status column doesn't exist, don't include it in the query
        $exams_sql = "SELECT e.*, c.class_name 
                    FROM exams e 
                    JOIN classes c ON e.class_id = c.id 
                    ORDER BY e.exam_date DESC";
    }
} else {
    // If class_id doesn't exist, don't join with classes table
    if ($check_status->num_rows > 0) {
        // If status column exists, include it in the query
        $exams_sql = "SELECT e.* 
                    FROM exams e 
                    WHERE e.status = 'active'
                    ORDER BY e.exam_date DESC";
    } else {
        // If status column doesn't exist, don't include it in the query
        $exams_sql = "SELECT e.* 
                    FROM exams e 
                    ORDER BY e.exam_date DESC";
    }
}
$exams_result = $conn->query($exams_sql);

// Get all classes
$classSql = "SELECT * FROM classes ORDER BY class_name";
$classResult = $conn->query($classSql);

// Get selected exam and subject information
$subjects = [];
$exam_id = isset($_GET['exam_id']) ? $_GET['exam_id'] : 0;
$subject_id = isset($_GET['subject_id']) ? $_GET['subject_id'] : 0;
    $students = [];
$exam_details = null;
$subject_details = null;
$marks_distribution = null;

// If exam is selected, get assigned subjects for that exam
if ($exam_id > 0) {
    $subject_sql = "SELECT es.*, s.subject_name, s.subject_code 
                  FROM exam_subjects es 
                  JOIN subjects s ON es.subject_id = s.id 
                  WHERE es.exam_id = ?
                  ORDER BY s.subject_name";
    $subject_stmt = $conn->prepare($subject_sql);
    $subject_stmt->bind_param("i", $exam_id);
    $subject_stmt->execute();
    $subjects_result = $subject_stmt->get_result();
    
    while ($row = $subjects_result->fetch_assoc()) {
        $subjects[] = $row;
    }
    
    // Get exam details
    if ($check_class_id->num_rows > 0) {
        // If class_id exists, join with classes table
        $exam_sql = "SELECT e.*, c.class_name 
                    FROM exams e 
                    JOIN classes c ON e.class_id = c.id 
                    WHERE e.id = ?";
        $exam_stmt = $conn->prepare($exam_sql);
        $exam_stmt->bind_param("i", $exam_id);
    } else {
        // If class_id doesn't exist, don't join with classes table
        $exam_sql = "SELECT e.* 
                    FROM exams e 
                    WHERE e.id = ?";
        $exam_stmt = $conn->prepare($exam_sql);
        $exam_stmt->bind_param("i", $exam_id);
    }
    
    $exam_stmt->execute();
    $exam_details = $exam_stmt->get_result()->fetch_assoc();
}

// If subject is also selected, get marks distribution and students
if ($exam_id > 0 && $subject_id > 0) {
    // Get marks distribution details
    $marks_sql = "SELECT * FROM exam_subjects WHERE exam_id = ? AND subject_id = ?";
    $marks_stmt = $conn->prepare($marks_sql);
    $marks_stmt->bind_param("ii", $exam_id, $subject_id);
    $marks_stmt->execute();
    $marks_distribution = $marks_stmt->get_result()->fetch_assoc();
    
    // Get subject details
    $subject_info_sql = "SELECT * FROM subjects WHERE id = ?";
    $subject_info_stmt = $conn->prepare($subject_info_sql);
    $subject_info_stmt->bind_param("i", $subject_id);
    $subject_info_stmt->execute();
    $subject_details = $subject_info_stmt->get_result()->fetch_assoc();
    
    // Get students from the class of the selected exam who are assigned to this subject
    if (isset($exam_details['class_id'])) {
        $students_sql = "SELECT s.*, 
                        s.id as student_id, 
                        CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                        COALESCE(s.roll_number, 'N/A') as roll_number
                        FROM students s 
                        JOIN student_subjects ss ON s.id = ss.student_id
                        WHERE s.class_id = ? 
                        AND ss.subject_id = ?
                        ORDER BY s.roll_number, s.first_name, s.last_name";
        $students_stmt = $conn->prepare($students_sql);
        $students_stmt->bind_param("ii", $exam_details['class_id'], $subject_id);
        $students_stmt->execute();
        $students_result = $students_stmt->get_result();
        
        // If no students are directly assigned, fall back to all class students
        if ($students_result->num_rows == 0) {
            $all_students_sql = "SELECT s.*, 
                               s.id as student_id, 
                               CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                               COALESCE(s.roll_number, 'N/A') as roll_number
                               FROM students s 
                               WHERE s.class_id = ? 
                               ORDER BY s.roll_number, s.first_name, s.last_name";
            $all_students_stmt = $conn->prepare($all_students_sql);
            $all_students_stmt->bind_param("i", $exam_details['class_id']);
            $all_students_stmt->execute();
            $students_result = $all_students_stmt->get_result();
        }
        
        while ($row = $students_result->fetch_assoc()) {
            $students[] = $row;
        }
    } else {
        // If class_id doesn't exist, show a warning message later
    }
}

// Handle marks submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['submit_marks'])) {
    $exam_id = $_POST['exam_id'];
    $subject_id = $_POST['subject_id'];
    $student_ids = $_POST['student_id'];
    $cq_marks = $_POST['cq_marks'];
    $mcq_marks = isset($_POST['mcq_marks']) ? $_POST['mcq_marks'] : [];
    $practical_marks = isset($_POST['practical_marks']) ? $_POST['practical_marks'] : [];
    $total_marks = $_POST['total_marks'];
    
    // Delete existing marks for this exam and subject
    $delete_sql = "DELETE FROM marks WHERE exam_id = ? AND subject_id = ?";
    $delete_stmt = $conn->prepare($delete_sql);
    $delete_stmt->bind_param("ii", $exam_id, $subject_id);
    $delete_stmt->execute();
    
    // Insert new marks
    $insert_sql = "INSERT INTO marks (exam_id, subject_id, student_id, cq_marks, mcq_marks, practical_marks, total_marks) 
                  VALUES (?, ?, ?, ?, ?, ?, ?)";
    $insert_stmt = $conn->prepare($insert_sql);
    
    foreach ($student_ids as $index => $student_id) {
        $cq = isset($cq_marks[$index]) ? floatval($cq_marks[$index]) : 0;
        $mcq = isset($mcq_marks[$index]) ? floatval($mcq_marks[$index]) : 0;
        $practical = isset($practical_marks[$index]) ? floatval($practical_marks[$index]) : 0;
        $total = isset($total_marks[$index]) ? floatval($total_marks[$index]) : ($cq + $mcq + $practical);
        
        $insert_stmt->bind_param("iiidddd", $exam_id, $subject_id, $student_id, $cq, $mcq, $practical, $total);
        $insert_stmt->execute();
    }
    
    $success_message = "বিষয়টির নম্বর সফলভাবে সংরক্ষণ করা হয়েছে";
    
    // Redirect to avoid form resubmission
    header("Location: marks_entry.php?exam_id=$exam_id&subject_id=$subject_id&success=1");
    exit();
}

// Show success message on redirect
$success = isset($_GET['success']) ? $_GET['success'] : 0;

// Get pattern type from marks distribution
function getPatternType($distribution) {
    if ($distribution['mcq_marks'] > 0 && $distribution['practical_marks'] > 0) {
        return 'all';
    } elseif ($distribution['mcq_marks'] > 0) {
        return 'cq_mcq';
    } elseif ($distribution['practical_marks'] > 0) {
        return 'cq_practical';
    } else {
        return 'cq';
    }
}

// Load existing marks if any
$existing_marks = [];
if ($exam_id > 0 && $subject_id > 0) {
    $marks_sql = "SELECT * FROM marks WHERE exam_id = ? AND subject_id = ?";
    $marks_stmt = $conn->prepare($marks_sql);
    $marks_stmt->bind_param("ii", $exam_id, $subject_id);
    $marks_stmt->execute();
    $marks_result = $marks_stmt->get_result();
    
    while ($mark = $marks_result->fetch_assoc()) {
        $existing_marks[$mark['student_id']] = $mark;
    }
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নম্বর এন্ট্রি - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-size: .875rem;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
        }
        .sidebar-sticky {
            position: relative;
            top: 0;
            height: calc(100vh - 48px);
            padding-top: .5rem;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            font-weight: 500;
            color: #333;
        }
        .nav-link.active {
            color: #007bff;
        }
        .main-content {
            margin-left: 240px;
            padding: 20px;
        }
        @media (max-width: 767.98px) {
            .main-content {
                margin-left: 0;
            }
        }
        .marks-input {
            width: 80px;
            display: inline-block;
        }
        .student-row {
            background-color: #fff;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }
        .student-row:hover {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }
        .marks-distribution {
            background-color: #f0f8ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 3px solid #007bff;
        }
        .total-column {
            background-color: #f8f9fa;
            border-left: 1px solid #dee2e6;
            border-radius: 0 4px 4px 0;
        }
        .cell-header {
            font-weight: 500;
            margin-bottom: 5px;
            color: #495057;
        }
        .readonly-input {
            background-color: #e9ecef;
        }
        .distribution-header {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 15px;
            color: #212529;
        }
        .distribution-item {
            padding: 8px 12px;
            background-color: #e7f3ff;
            border-radius: 4px;
            margin-bottom: 8px;
            font-weight: 500;
        }
        .pattern-badge {
            font-size: 0.85rem;
            padding: 4px 8px;
            border-radius: 20px;
        }
    </style>
</head>
<body>
    <?php include('includes/header.php'); ?>
    
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">নম্বর এন্ট্রি</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="manage_exams.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tasks"></i> পরীক্ষা সম্পাদনা
                            </a>
                            <a href="exam_schedule.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-calendar-alt"></i> পরীক্ষার সময়সূচি
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php if (isset($success_message) || $success == 1) : ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo isset($success_message) ? $success_message : "বিষয়টির নম্বর সফলভাবে সংরক্ষণ করা হয়েছে"; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Exam and Subject Selection Form -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>পরীক্ষা এবং বিষয় নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row">
                            <div class="col-md-5 mb-3">
                                <label for="exam_id" class="form-label">পরীক্ষা নির্বাচন করুন</label>
                                <select class="form-select" id="exam_id" name="exam_id" required onchange="this.form.submit()">
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php if ($exams_result && $exams_result->num_rows > 0): 
                                        $exams_result->data_seek(0);
                                        while ($exam = $exams_result->fetch_assoc()): 
                                    ?>
                                        <option value="<?php echo $exam['id']; ?>" <?php echo ($exam_id == $exam['id']) ? 'selected' : ''; ?>>
                                            <?php echo $exam['exam_name'] . (isset($exam['class_name']) ? ' - ' . $exam['class_name'] : '') . ' (' . date('d/m/Y', strtotime($exam['exam_date'])) . ')'; ?>
                                        </option>
                                    <?php endwhile; endif; ?>
                                </select>
                            </div>
                            
                            <?php if ($exam_id > 0): ?>
                            <div class="col-md-5 mb-3">
                                <label for="subject_id" class="form-label">বিষয় নির্বাচন করুন</label>
                                <select class="form-select" id="subject_id" name="subject_id" required onchange="this.form.submit()">
                                    <option value="">বিষয় নির্বাচন করুন</option>
                                    <?php foreach ($subjects as $subject): ?>
                                        <option value="<?php echo $subject['subject_id']; ?>" <?php echo ($subject_id == $subject['subject_id']) ? 'selected' : ''; ?>>
                                            <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php endif; ?>
                        </form>
                                        </div>
                                    </div>
                
                <?php if ($exam_id > 0 && $subject_id > 0 && $marks_distribution && $subject_details): 
                    $pattern_type = getPatternType($marks_distribution);
                    $using_all_students = isset($all_students_sql);
                ?>
                <!-- Marks Distribution Info -->
                <div class="marks-distribution">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="distribution-header">
                                <?php echo $subject_details['subject_name']; ?> - মার্কস বিভাজন
                                <?php
                                $pattern_text = '';
                                $pattern_class = '';
                                if ($pattern_type === 'cq') {
                                    $pattern_text = 'শুধু CQ';
                                    $pattern_class = 'bg-primary';
                                } elseif ($pattern_type === 'cq_mcq') {
                                    $pattern_text = 'CQ + MCQ';
                                    $pattern_class = 'bg-success';
                                } elseif ($pattern_type === 'cq_practical') {
                                    $pattern_text = 'CQ + Practical';
                                    $pattern_class = 'bg-info';
                                } elseif ($pattern_type === 'all') {
                                    $pattern_text = 'CQ + MCQ + Practical';
                                    $pattern_class = 'bg-warning text-dark';
                                }
                                ?>
                                <span class="badge <?php echo $pattern_class; ?> pattern-badge"><?php echo $pattern_text; ?></span>
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="distribution-item">
                                        <i class="fas fa-pen me-2"></i> CQ: <?php echo $marks_distribution['cq_marks']; ?> নম্বর
                                    </div>
                                </div>
                                
                                <?php if ($marks_distribution['mcq_marks'] > 0): ?>
                                <div class="col-md-4">
                                    <div class="distribution-item">
                                        <i class="fas fa-check-circle me-2"></i> MCQ: <?php echo $marks_distribution['mcq_marks']; ?> নম্বর
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if ($marks_distribution['practical_marks'] > 0): ?>
                                <div class="col-md-4">
                                    <div class="distribution-item">
                                        <i class="fas fa-flask me-2"></i> Practical: <?php echo $marks_distribution['practical_marks']; ?> নম্বর
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-center justify-content-end">
                            <div class="text-end">
                                <h5 class="mb-2">মোট: <?php echo $marks_distribution['total_marks']; ?> নম্বর</h5>
                                <p class="mb-0 text-muted"><?php echo isset($exam_details['class_name']) ? $exam_details['class_name'] : 'N/A'; ?> - <?php echo date('d F Y', strtotime($exam_details['exam_date'])); ?></p>
                            </div>
                                        </div>
                                        </div>
                                    </div>
                
                <?php if ($using_all_students): ?>
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i> <strong>সতর্কতা:</strong> এই বিষয়ের জন্য কোন শিক্ষার্থী নির্দিষ্ট করা নেই। ক্লাসের সমস্ত শিক্ষার্থীদের তালিকা দেখানো হচ্ছে। 
                    <a href="assign_subjects.php?class_id=<?php echo isset($exam_details['class_id']) ? $exam_details['class_id'] : '0'; ?>&subject_id=<?php echo $subject_id; ?>" class="btn btn-sm btn-warning ms-2">
                        <i class="fas fa-link"></i> শিক্ষার্থী বরাদ্দ করুন
                    </a>
                                    </div>
                <?php endif; ?>
                
                <?php if (count($students) > 0): ?>
                <!-- Marks Entry Form -->
                <form method="post" action="" id="marksForm">
                    <input type="hidden" name="exam_id" value="<?php echo $exam_id; ?>">
                    <input type="hidden" name="subject_id" value="<?php echo $subject_id; ?>">
                    
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>শিক্ষার্থী নম্বর এন্ট্রি</h5>
                            <div>
                                <button type="submit" name="submit_marks" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> নম্বর সংরক্ষণ করুন
                                </button>
                                            </div>
                                        </div>
                        <div class="card-body">
                            <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
                            <div class="alert alert-success mb-4">
                                <i class="fas fa-check-circle me-2"></i> নম্বর সফলভাবে সংরক্ষণ করা হয়েছে
                            </div>
                            <?php endif; ?>
                            
                            <?php if ($exam_id > 0 && $subject_id > 0 && !isset($exam_details['class_id'])): ?>
                            <div class="alert alert-danger mb-4">
                                <i class="fas fa-exclamation-circle me-2"></i> <strong>সমস্যা:</strong> এই পরীক্ষার জন্য কোন শ্রেণী নির্ধারিত নেই। অনুগ্রহ করে পরীক্ষা সেটআপ আপডেট করুন।
                            </div>
                            <?php endif; ?>
                            
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i> নম্বর এন্ট্রি করার সময় সবশেষে "নম্বর সংরক্ষণ করুন" বাটনে ক্লিক করতে ভুলবেন না।
                                    </div>
                                    
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                            <thead class="table-light">
                                                <tr>
                                            <th style="width: 60px;">ক্রম</th>
                                            <th style="width: 80px;">রোল</th>
                                            <th>শিক্ষার্থীর নাম</th>
                                            <th style="width: 120px;">CQ (<?php echo $marks_distribution['cq_marks']; ?>)</th>
                                            <?php if ($marks_distribution['mcq_marks'] > 0): ?>
                                            <th style="width: 120px;">MCQ (<?php echo $marks_distribution['mcq_marks']; ?>)</th>
                                            <?php endif; ?>
                                            <?php if ($marks_distribution['practical_marks'] > 0): ?>
                                            <th style="width: 120px;">Practical (<?php echo $marks_distribution['practical_marks']; ?>)</th>
                                            <?php endif; ?>
                                            <th style="width: 120px;">মোট (<?php echo $marks_distribution['total_marks']; ?>)</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                        <?php foreach ($students as $index => $student): 
                                            $student_id = $student['student_id'];
                                            $existing_cq = isset($existing_marks[$student_id]) ? $existing_marks[$student_id]['cq_marks'] : '';
                                            $existing_mcq = isset($existing_marks[$student_id]) ? $existing_marks[$student_id]['mcq_marks'] : '';
                                            $existing_practical = isset($existing_marks[$student_id]) ? $existing_marks[$student_id]['practical_marks'] : '';
                                            $existing_total = isset($existing_marks[$student_id]) ? $existing_marks[$student_id]['total_marks'] : '';
                                        ?>
                                        <tr class="marks-row" data-cq-max="<?php echo $marks_distribution['cq_marks']; ?>" 
                                            data-mcq-max="<?php echo $marks_distribution['mcq_marks']; ?>" 
                                            data-practical-max="<?php echo $marks_distribution['practical_marks']; ?>" 
                                            data-total-max="<?php echo $marks_distribution['total_marks']; ?>">
                                            <td class="text-center"><?php echo $index + 1; ?></td>
                                            <td class="text-center"><?php echo $student['roll_number']; ?></td>
                                            <td>
                                                <?php echo $student['student_name']; ?>
                                                <input type="hidden" name="student_id[]" value="<?php echo $student['student_id']; ?>">
                                            </td>
                                            <td>
                                                <input type="number" class="form-control cq-marks" name="cq_marks[]" 
                                                       value="<?php echo $existing_cq; ?>" min="0" max="<?php echo $marks_distribution['cq_marks']; ?>" 
                                                       step="0.01" required>
                                            </td>
                                            <?php if ($marks_distribution['mcq_marks'] > 0): ?>
                                            <td>
                                                <input type="number" class="form-control mcq-marks" name="mcq_marks[]" 
                                                       value="<?php echo $existing_mcq; ?>" min="0" max="<?php echo $marks_distribution['mcq_marks']; ?>" 
                                                       step="0.01" required>
                                            </td>
                                            <?php endif; ?>
                                            <?php if ($marks_distribution['practical_marks'] > 0): ?>
                                            <td>
                                                <input type="number" class="form-control practical-marks" name="practical_marks[]" 
                                                       value="<?php echo $existing_practical; ?>" min="0" max="<?php echo $marks_distribution['practical_marks']; ?>" 
                                                       step="0.01" required>
                                                    </td>
                                            <?php endif; ?>
                                            <td>
                                                <input type="number" class="form-control total-marks" name="total_marks[]" 
                                                       value="<?php echo $existing_total; ?>" min="0" max="<?php echo $marks_distribution['total_marks']; ?>" 
                                                       step="0.01" readonly>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                            </div>
                        </div>
                    </div>
                </form>
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> এই ক্লাসে কোন শিক্ষার্থী পাওয়া যায়নি।
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Auto-calculate total marks
            function calculateTotal(row) {
                const cq = parseFloat($(row).find('.cq-marks').val()) || 0;
                const mcq = parseFloat($(row).find('.mcq-marks').val()) || 0;
                const practical = parseFloat($(row).find('.practical-marks').val()) || 0;
                
                // Calculate sum and round to 2 decimal places
                const total = Math.round((cq + mcq + practical) * 100) / 100;
                
                $(row).find('.total-marks').val(total);
                
                // Validate against max limits
                validateInput($(row).find('.cq-marks'), parseFloat($(row).data('cq-max')));
                validateInput($(row).find('.mcq-marks'), parseFloat($(row).data('mcq-max')));
                validateInput($(row).find('.practical-marks'), parseFloat($(row).data('practical-max')));
                
                // Validate total
                const totalMax = parseFloat($(row).data('total-max'));
                if (total > totalMax) {
                    $(row).find('.total-marks').addClass('is-invalid');
                } else {
                    $(row).find('.total-marks').removeClass('is-invalid');
                }
            }
            
            // Validate input against max value
            function validateInput(input, maxVal) {
                const val = parseFloat(input.val()) || 0;
                if (val > maxVal) {
                    input.addClass('is-invalid');
                } else {
                    input.removeClass('is-invalid');
                }
            }
            
            // Calculate total on input change
            $('.marks-row').on('input', '.cq-marks, .mcq-marks, .practical-marks', function() {
                calculateTotal($(this).closest('.marks-row'));
            });
            
            // Form validation
            $('#marksForm').on('submit', function(e) {
                let isValid = true;
                
                $('.marks-row').each(function() {
                    calculateTotal(this);
                    
                    if ($(this).find('.is-invalid').length > 0) {
                        isValid = false;
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    alert('সতর্কতা: কিছু নম্বর সর্বোচ্চ সীমার চেয়ে বেশি। দয়া করে সঠিক মান দিন।');
                }
            });
            
            // Initialize calculations
            $('.marks-row').each(function() {
                calculateTotal(this);
            });
            
            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
    </script>
</body>
</html> 