<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$success_messages = [];
$error_messages = [];

// Step 1: Check if both department and department_id columns exist in students table
$checkDeptColumnQuery = "SHOW COLUMNS FROM students LIKE 'department'";
$deptColumnResult = $conn->query($checkDeptColumnQuery);

$checkDeptIdColumnQuery = "SHOW COLUMNS FROM students LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);

// If department column doesn't exist, exit
if ($deptColumnResult->num_rows == 0) {
    $error_messages[] = "The 'department' column does not exist in the students table. Migration cannot proceed.";
}

// If department_id column doesn't exist, create it
if ($deptIdColumnResult->num_rows == 0) {
    $createDeptIdColumnQuery = "ALTER TABLE students ADD COLUMN department_id INT(11) NULL";
    if ($conn->query($createDeptIdColumnQuery)) {
        $success_messages[] = "Successfully created 'department_id' column in students table.";
    } else {
        $error_messages[] = "Failed to create 'department_id' column: " . $conn->error;
    }
}

// Process migration if the form was submitted
if (isset($_POST['migrate'])) {
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        // Get distinct department values from students
        $distinctDeptsQuery = "SELECT DISTINCT department FROM students WHERE department IS NOT NULL AND department != ''";
        $distinctDepts = $conn->query($distinctDeptsQuery);
        
        $migrated_count = 0;
        
        if ($distinctDepts && $distinctDepts->num_rows > 0) {
            while ($dept = $distinctDepts->fetch_assoc()) {
                $deptName = $dept['department'];
                
                // Check if department already exists in departments table
                $checkExistingQuery = "SELECT id FROM departments WHERE department_name = ?";
                $stmt = $conn->prepare($checkExistingQuery);
                $stmt->bind_param("s", $deptName);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    // Department exists, get its ID
                    $deptId = $result->fetch_assoc()['id'];
                } else {
                    // Insert department into departments table
                    $insertDeptQuery = "INSERT INTO departments (department_name) VALUES (?)";
                    $stmt = $conn->prepare($insertDeptQuery);
                    $stmt->bind_param("s", $deptName);
                    $stmt->execute();
                    $deptId = $conn->insert_id;
                }
                
                // Update all students with this department
                $updateStudentsQuery = "UPDATE students SET department_id = ? WHERE department = ?";
                $stmt = $conn->prepare($updateStudentsQuery);
                $stmt->bind_param("is", $deptId, $deptName);
                $stmt->execute();
                $migrated_count += $stmt->affected_rows;
            }
        }
        
        // Commit transaction
        $conn->commit();
        
        $success_messages[] = "Successfully migrated $migrated_count student records to use department_id.";
        
        // Optionally remove the old department column
        if (isset($_POST['remove_old_column']) && $_POST['remove_old_column'] == 1) {
            $dropColumnQuery = "ALTER TABLE students DROP COLUMN department";
            if ($conn->query($dropColumnQuery)) {
                $success_messages[] = "Successfully removed the old 'department' column.";
            } else {
                $error_messages[] = "Failed to remove old 'department' column: " . $conn->error;
            }
        }
        
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        $error_messages[] = "Migration failed: " . $e->getMessage();
    }
}

// Get migration status
$studentsWithDeptIdQuery = "SELECT COUNT(*) as count FROM students WHERE department_id IS NOT NULL";
$studentsWithDeptId = $conn->query($studentsWithDeptIdQuery)->fetch_assoc()['count'];

$totalStudentsQuery = "SELECT COUNT(*) as count FROM students";
$totalStudents = $conn->query($totalStudentsQuery)->fetch_assoc()['count'];

$migrationPercentage = ($totalStudents > 0) ? round(($studentsWithDeptId / $totalStudents) * 100) : 0;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrate Departments - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Admin Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> Teachers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> Staff
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="departments.php">
                            <i class="fas fa-building me-2"></i> Departments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> Classes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>Department Data Migration</h2>
                        <p class="text-muted">Migrate from the old 'department' field to the new 'department_id' field</p>
                    </div>
                    <div class="col-auto">
                        <a href="departments.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Departments
                        </a>
                    </div>
                </div>
                
                <!-- Success and Error Messages -->
                <?php foreach ($success_messages as $msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endforeach; ?>
                
                <?php foreach ($error_messages as $msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endforeach; ?>
                
                <!-- Migration Status -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Migration Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $migrationPercentage; ?>%" 
                                 aria-valuenow="<?php echo $migrationPercentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo $migrationPercentage; ?>%
                            </div>
                        </div>
                        <p class="mb-0">
                            <strong><?php echo $studentsWithDeptId; ?></strong> out of <strong><?php echo $totalStudents; ?></strong> 
                            students have been migrated to use the department_id field.
                        </p>
                    </div>
                </div>
                
                <!-- Migration Form -->
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Run Migration</h5>
                    </div>
                    <div class="card-body">
                        <p>
                            This will migrate all student records from using the text-based 'department' field to 
                            using the numeric 'department_id' field that references the departments table.
                        </p>
                        
                        <form method="POST" action="">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="remove_old_column" name="remove_old_column" value="1">
                                <label class="form-check-label" for="remove_old_column">
                                    Remove old 'department' column after migration (not recommended until all systems are updated)
                                </label>
                            </div>
                            
                            <button type="submit" name="migrate" class="btn btn-primary">
                                <i class="fas fa-sync me-2"></i>Run Migration
                            </button>
                        </form>
                        
                        <div class="alert alert-warning mt-3">
                            <h5 class="alert-heading">Warning!</h5>
                            <p>
                                Before removing the old 'department' column, make sure all your code has been updated to use the new 'department_id' field.
                                This includes any reports, views, or other queries that might be using the old field.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 