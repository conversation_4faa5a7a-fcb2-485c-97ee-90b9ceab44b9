<?php
session_start();

// Modified session check - allows access without redirecting
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    $isAdmin = false;
} else {
    $isAdmin = true;
}

require_once "../includes/dbh.inc.php";

// Create uploads directory if it doesn't exist
$uploads_dir = "../uploads/notices";
if (!file_exists($uploads_dir)) {
    mkdir($uploads_dir, 0777, true);
}

// Handle add notice form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["add_notice"])) {
    $title = $_POST["title"];
    $content = $_POST["content"];
    $date = $_POST["date"];
    $added_by = isset($_SESSION["username"]) ? $_SESSION["username"] : "admin";
    
    // Handle file uploads
    $attachment_path = null;
    $attachment_type = null;
    
    if (!empty($_FILES['attachment']['name'])) {
        $file_name = $_FILES['attachment']['name'];
        $file_tmp = $_FILES['attachment']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check if file type is allowed
        $allowed_ext = array("pdf", "jpg", "jpeg", "png", "gif");
        
        if (in_array($file_ext, $allowed_ext)) {
            // Generate a unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $upload_path = $uploads_dir . '/' . $new_file_name;
            
            if (move_uploaded_file($file_tmp, $upload_path)) {
                $attachment_path = "uploads/notices/" . $new_file_name;
                $attachment_type = $file_ext;
            } else {
                echo "<script>alert('ফাইল আপলোড করতে সমস্যা হয়েছে।');</script>";
            }
        } else {
            echo "<script>alert('অনুমোদিত ফাইল টাইপ: PDF, JPG, JPEG, PNG, GIF');</script>";
        }
    }
    
    // Insert notice with attachment information
    $sql = "INSERT INTO notices (title, content, date, added_by, attachment_path, attachment_type) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssssss", $title, $content, $date, $added_by, $attachment_path, $attachment_type);
    
    if ($stmt->execute()) {
        echo "<script>alert('নোটিশ সফলভাবে যোগ করা হয়েছে।');</script>";
    } else {
        echo "<script>alert('নোটিশ যোগ করা যায়নি। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Handle update notice
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["update_notice"])) {
    $id = $_POST["notice_id"];
    $title = $_POST["edit_title"];
    $content = $_POST["edit_content"];
    $date = $_POST["edit_date"];
    
    // Get current attachment info
    $get_attachment = "SELECT attachment_path FROM notices WHERE id = ?";
    $stmt = $conn->prepare($get_attachment);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($current_attachment);
    $stmt->fetch();
    $stmt->close();
    
    // Handle file uploads for update
    $attachment_path = $current_attachment;
    $attachment_type = null;
    
    if (!empty($_FILES['edit_attachment']['name'])) {
        $file_name = $_FILES['edit_attachment']['name'];
        $file_tmp = $_FILES['edit_attachment']['tmp_name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        // Check if file type is allowed
        $allowed_ext = array("pdf", "jpg", "jpeg", "png", "gif");
        
        if (in_array($file_ext, $allowed_ext)) {
            // Delete old file if exists
            if (!empty($current_attachment)) {
                $old_file_path = "../" . $current_attachment;
                if (file_exists($old_file_path)) {
                    unlink($old_file_path);
                }
            }
            
            // Generate a unique filename
            $new_file_name = uniqid() . '.' . $file_ext;
            $upload_path = $uploads_dir . '/' . $new_file_name;
            
            if (move_uploaded_file($file_tmp, $upload_path)) {
                $attachment_path = "uploads/notices/" . $new_file_name;
                $attachment_type = $file_ext;
            } else {
                echo "<script>alert('ফাইল আপডেট করতে সমস্যা হয়েছে।');</script>";
            }
        } else {
            echo "<script>alert('অনুমোদিত ফাইল টাইপ: PDF, JPG, JPEG, PNG, GIF');</script>";
        }
    }
    
    // Update notice with attachment information
    $sql = "UPDATE notices SET title = ?, content = ?, date = ?, attachment_path = ?, attachment_type = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssssi", $title, $content, $date, $attachment_path, $attachment_type, $id);
    
    if ($stmt->execute()) {
        echo "<script>alert('নোটিশ সফলভাবে আপডেট করা হয়েছে।');</script>";
    } else {
        echo "<script>alert('নোটিশ আপডেট করা যায়নি। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Handle delete notice
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["delete_notice"])) {
    $id = $_POST["delete_id"];
    
    // Get attachment path before deleting
    $get_attachment = "SELECT attachment_path FROM notices WHERE id = ?";
    $stmt = $conn->prepare($get_attachment);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($attachment_path);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the notice
    $sql = "DELETE FROM notices WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the attachment file if exists
        if (!empty($attachment_path)) {
            $file_path = "../" . $attachment_path;
            if (file_exists($file_path)) {
                unlink($file_path);
            }
        }
        echo "<script>alert('নোটিশ সফলভাবে মুছে ফেলা হয়েছে।');</script>";
    } else {
        echo "<script>alert('নোটিশ মুছতে ব্যর্থ হয়েছে। সমস্যা: " . $stmt->error . "');</script>";
    }
    
    $stmt->close();
}

// Check if notices table exists
$sql = "SHOW TABLES LIKE 'notices'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    // Create notices table with attachment fields
    $sql = "CREATE TABLE notices (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        date DATE NOT NULL,
        added_by VARCHAR(50),
        attachment_path VARCHAR(255),
        attachment_type VARCHAR(10),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<div class='alert alert-success'>নোটিশ টেবিল সফলভাবে তৈরি করা হয়েছে।</div>";
        
        // Add sample notice
        $sql = "INSERT INTO notices (title, content, date, added_by) VALUES 
        ('স্বাগতম', 'কলেজ ম্যানেজমেন্ট সিস্টেমে স্বাগতম। এটি একটি নমুনা নোটিশ।', CURDATE(), 'admin')";
        
        if ($conn->query($sql) === TRUE) {
            echo "<div class='alert alert-success'>নমুনা নোটিশ যোগ করা হয়েছে।</div>";
        }
    }
} else {
    // Check if attachment columns exist in the notices table
    $result = $conn->query("SHOW COLUMNS FROM notices LIKE 'attachment_path'");
    if ($result->num_rows == 0) {
        // Add the attachment columns
        $sql = "ALTER TABLE notices 
                ADD COLUMN attachment_path VARCHAR(255) AFTER added_by,
                ADD COLUMN attachment_type VARCHAR(10) AFTER attachment_path";
        $conn->query($sql);
    }
}

// Get all notices
$sql = "SELECT * FROM notices ORDER BY date DESC";
$result = $conn->query($sql);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>নোটিশ ম্যানেজমেন্ট</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .action-buttons .btn {
            margin-right: 5px;
        }
        .attachment-preview {
            max-width: 100%;
            max-height: 300px;
            margin-top: 15px;
        }
        .pdf-preview {
            display: inline-block;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #ddd;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar placeholder - you would ideally include this from a common file -->
            <div class="col-md-3 col-lg-2 d-none d-md-block bg-light sidebar">
                <div class="position-sticky">
                    <h3 class="text-center my-4">অ্যাডমিন প্যানেল</h3>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="students.php">
                                <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="notices.php">
                                <i class="fas fa-bullhorn me-2"></i> নোটিশ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../includes/logout.inc.php">
                                <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <h1 class="mb-4">নোটিশ ম্যানেজমেন্ট</h1>
                
                <!-- Add Notice Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">নতুন নোটিশ যোগ করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="title" class="form-label">শিরোনাম</label>
                                <input type="text" class="form-control" id="title" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label for="content" class="form-label">বিবরণ</label>
                                <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="date" class="form-label">তারিখ</label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label for="attachment" class="form-label">PDF বা ছবি সংযুক্ত করুন (ঐচ্ছিক)</label>
                                <input type="file" class="form-control" id="attachment" name="attachment">
                                <div class="form-text">অনুমোদিত ফাইল: PDF, JPG, PNG, GIF (সর্বোচ্চ 5MB)</div>
                            </div>
                            <button type="submit" name="add_notice" class="btn btn-primary">যোগ করুন</button>
                        </form>
                    </div>
                </div>
                
                <!-- Notices List -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">সকল নোটিশ</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($result && $result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>আইডি</th>
                                            <th>শিরোনাম</th>
                                            <th>তারিখ</th>
                                            <th>এডিট করেছেন</th>
                                            <th>ফাইল</th>
                                            <th>পদক্ষেপ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($row = $result->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $row["id"]; ?></td>
                                                <td><?php echo htmlspecialchars($row["title"]); ?></td>
                                                <td><?php echo $row["date"]; ?></td>
                                                <td><?php echo $row["added_by"] ?? 'অজানা'; ?></td>
                                                <td>
                                                    <?php if (!empty($row["attachment_path"])): ?>
                                                        <a href="../<?php echo $row["attachment_path"]; ?>" target="_blank" class="btn btn-sm btn-outline-secondary">
                                                            <i class="fas fa-file-download"></i> ফাইল ডাউনলোড
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">নেই</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="action-buttons">
                                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-eye"></i> দেখুন
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-edit"></i> সম্পাদনা
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $row["id"]; ?>">
                                                        <i class="fas fa-trash"></i> মুছুন
                                                    </button>
                                                </td>
                                            </tr>
                                            
                                            <!-- View Modal -->
                                            <div class="modal fade" id="viewModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="viewModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="viewModalLabel<?php echo $row["id"]; ?>"><?php echo htmlspecialchars($row["title"]); ?></h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p><strong>তারিখ:</strong> <?php echo $row["date"]; ?></p>
                                                            <p><strong>বিবরণ:</strong></p>
                                                            <div class="p-3 bg-light rounded"><?php echo nl2br(htmlspecialchars($row["content"])); ?></div>
                                                            
                                                            <?php if (!empty($row["attachment_path"])): ?>
                                                                <div class="mt-3">
                                                                    <p><strong>সংযুক্ত ফাইল:</strong></p>
                                                                    <?php 
                                                                    $ext = strtolower(pathinfo($row["attachment_path"], PATHINFO_EXTENSION)); 
                                                                    if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif'])): 
                                                                    ?>
                                                                        <img src="../<?php echo $row["attachment_path"]; ?>" class="attachment-preview" alt="Attached image">
                                                                    <?php elseif ($ext == 'pdf'): ?>
                                                                        <div class="pdf-preview mb-2">
                                                                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                                                                            <span class="ms-2">PDF ফাইল</span>
                                                                        </div>
                                                                        
                                                                        <!-- Inline PDF Viewer -->
                                                                        <div class="pdf-container" style="width: 100%; height: 500px; border: 1px solid #ddd; margin-bottom: 10px;">
                                                                            <object data="../<?php echo $row["attachment_path"]; ?>" type="application/pdf" width="100%" height="100%">
                                                                                <p>আপনার ব্রাউজারে PDF দেখা যাচ্ছে না। 
                                                                                <a href="../<?php echo $row["attachment_path"]; ?>" target="_blank">PDF ডাউনলোড করুন</a>.</p>
                                                                            </object>
                                                                        </div>
                                                                        
                                                                        <div class="mt-2 d-flex gap-2">
                                                                            <a href="../<?php echo $row["attachment_path"]; ?>" class="btn btn-sm btn-outline-primary" target="_blank">
                                                                                <i class="fas fa-external-link-alt"></i> নতুন ট্যাবে খুলুন
                                                                            </a>
                                                                            <a href="../<?php echo $row["attachment_path"]; ?>" class="btn btn-sm btn-outline-success" download>
                                                                                <i class="fas fa-download"></i> ডাউনলোড করুন
                                                                            </a>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বন্ধ করুন</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Edit Modal -->
                                            <div class="modal fade" id="editModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="editModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="editModalLabel<?php echo $row["id"]; ?>">নোটিশ সম্পাদনা করুন</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <form method="post" action="" enctype="multipart/form-data">
                                                                <input type="hidden" name="notice_id" value="<?php echo $row["id"]; ?>">
                                                                <div class="mb-3">
                                                                    <label for="edit_title<?php echo $row["id"]; ?>" class="form-label">শিরোনাম</label>
                                                                    <input type="text" class="form-control" id="edit_title<?php echo $row["id"]; ?>" name="edit_title" value="<?php echo htmlspecialchars($row["title"]); ?>" required>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label for="edit_content<?php echo $row["id"]; ?>" class="form-label">বিবরণ</label>
                                                                    <textarea class="form-control" id="edit_content<?php echo $row["id"]; ?>" name="edit_content" rows="6" required><?php echo htmlspecialchars($row["content"]); ?></textarea>
                                                                </div>
                                                                <div class="mb-3">
                                                                    <label for="edit_date<?php echo $row["id"]; ?>" class="form-label">তারিখ</label>
                                                                    <input type="date" class="form-control" id="edit_date<?php echo $row["id"]; ?>" name="edit_date" value="<?php echo $row["date"]; ?>" required>
                                                                </div>
                                                                
                                                                <div class="mb-3">
                                                                    <label for="edit_attachment<?php echo $row["id"]; ?>" class="form-label">PDF বা ছবি পরিবর্তন করুন (ঐচ্ছিক)</label>
                                                                    <input type="file" class="form-control" id="edit_attachment<?php echo $row["id"]; ?>" name="edit_attachment">
                                                                    <div class="form-text">অনুমোদিত ফাইল: PDF, JPG, PNG, GIF (সর্বোচ্চ 5MB)</div>
                                                                    
                                                                    <?php if (!empty($row["attachment_path"])): ?>
                                                                        <div class="mt-2">
                                                                            <span class="badge bg-info">বর্তমান ফাইল:</span> 
                                                                            <a href="../<?php echo $row["attachment_path"]; ?>" target="_blank">
                                                                                <?php 
                                                                                $filename = basename($row["attachment_path"]); 
                                                                                echo $filename;
                                                                                ?>
                                                                            </a>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                                
                                                                <button type="submit" name="update_notice" class="btn btn-primary">আপডেট করুন</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="deleteModal<?php echo $row["id"]; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $row["id"]; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo $row["id"]; ?>">নোটিশ মুছুন</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>আপনি কি নিশ্চিত যে আপনি এই নোটিশটি মুছতে চান?</p>
                                                            <p><strong>শিরোনাম:</strong> <?php echo htmlspecialchars($row["title"]); ?></p>
                                                            <?php if (!empty($row["attachment_path"])): ?>
                                                                <p class="text-danger"><strong>সতর্কতা:</strong> এর সাথে সংযুক্ত ফাইলও মুছে যাবে।</p>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <form method="post" action="">
                                                                <input type="hidden" name="delete_id" value="<?php echo $row["id"]; ?>">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                                                                <button type="submit" name="delete_notice" class="btn btn-danger">মুছুন</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">কোন নোটিশ পাওয়া যায়নি।</div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>