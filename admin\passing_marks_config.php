<?php
session_start();
require_once('../includes/dbh.inc.php');
require_once('includes/functions.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Create the passing_marks_config table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS passing_marks_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NULL,
    min_percentage DECIMAL(5,2) NOT NULL,
    max_percentage DECIMAL(5,2) NOT NULL,
    passing_mark DECIMAL(5,2) NOT NULL,
    cq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    mcq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    practical_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    grade VARCHAR(5) NOT NULL,
    grade_point DECIMAL(3,2) NOT NULL,
    description VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id, min_percentage, max_percentage)
)";
$conn->query($create_table_sql);

// Check if subject_id column exists
$check_column_sql = "SHOW COLUMNS FROM passing_marks_config LIKE 'subject_id'";
$column_result = $conn->query($check_column_sql);

if ($column_result->num_rows == 0) {
    // subject_id column doesn't exist, add it
    try {
        $alter_table_sql = "ALTER TABLE passing_marks_config 
                           ADD COLUMN subject_id INT NULL AFTER id,
                           ADD COLUMN cq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER passing_mark,
                           ADD COLUMN mcq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER cq_passing_percent,
                           ADD COLUMN practical_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER mcq_passing_percent,
                           DROP INDEX IF EXISTS subject_id,
                           ADD UNIQUE KEY(subject_id, min_percentage, max_percentage)";
        $conn->query($alter_table_sql);
        
        // Update existing records to have NULL for subject_id
        $update_sql = "UPDATE passing_marks_config SET subject_id = NULL";
        $conn->query($update_sql);
    } catch (Exception $e) {
        // Log error but continue
        error_log("Error updating passing_marks_config table: " . $e->getMessage());
    }
}

// Get all subjects
$subjects_sql = "SELECT * FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_sql);
$subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[$subject['id']] = $subject;
}

// Check if we need to populate default values
$check_sql = "SELECT COUNT(*) as count FROM passing_marks_config";
$count = $conn->query($check_sql)->fetch_assoc()['count'];

if ($count == 0) {
    // Insert default values with component passing marks
    $default_values = [
        [NULL, '80.00', '100.00', '80.00', '40.00', '40.00', '40.00', 'A+', '5.00', 'শ্রেষ্ঠত্ব (Excellence)'],
        [NULL, '70.00', '79.99', '70.00', '35.00', '35.00', '35.00', 'A', '4.00', 'অতি উত্তম (Very Good)'],
        [NULL, '60.00', '69.99', '60.00', '33.00', '33.00', '33.00', 'A-', '3.50', 'উত্তম (Good)'],
        [NULL, '50.00', '59.99', '50.00', '33.00', '33.00', '33.00', 'B', '3.00', 'ভালো (Satisfactory)'],
        [NULL, '40.00', '49.99', '40.00', '33.00', '33.00', '33.00', 'C', '2.00', 'মোটামুটি (Average)'],
        [NULL, '33.00', '39.99', '33.00', '33.00', '33.00', '33.00', 'D', '1.00', 'নিম্নমান (Poor)'],
        [NULL, '0.00', '32.99', '0.00', '0.00', '0.00', '0.00', 'F', '0.00', 'অকৃতকার্য (Fail)']
    ];

    $insert_sql = "INSERT INTO passing_marks_config 
                 (subject_id, min_percentage, max_percentage, passing_mark, cq_passing_percent, mcq_passing_percent, practical_passing_percent, grade, grade_point, description) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_sql);

    foreach ($default_values as $value) {
        $stmt->bind_param("iddddddsds", $value[0], $value[1], $value[2], $value[3], $value[4], $value[5], $value[6], $value[7], $value[8], $value[9]);
        $stmt->execute();
    }
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['save_config'])) {
        // Get form data
        $ids = isset($_POST['id']) ? $_POST['id'] : [];
        $subject_ids = isset($_POST['subject_id']) ? $_POST['subject_id'] : [];
        $min_percentages = isset($_POST['min_percentage']) ? $_POST['min_percentage'] : [];
        $max_percentages = isset($_POST['max_percentage']) ? $_POST['max_percentage'] : [];
        $passing_marks = isset($_POST['passing_mark']) ? $_POST['passing_mark'] : [];
        $cq_passing_percents = isset($_POST['cq_passing_percent']) ? $_POST['cq_passing_percent'] : [];
        $mcq_passing_percents = isset($_POST['mcq_passing_percent']) ? $_POST['mcq_passing_percent'] : [];
        $practical_passing_percents = isset($_POST['practical_passing_percent']) ? $_POST['practical_passing_percent'] : [];
        $grades = isset($_POST['grade']) ? $_POST['grade'] : [];
        $grade_points = isset($_POST['grade_point']) ? $_POST['grade_point'] : [];
        $descriptions = isset($_POST['description']) ? $_POST['description'] : [];

        // Begin transaction
        $conn->begin_transaction();

        try {
            // Update existing records
            foreach ($ids as $index => $id) {
                if (!empty($id)) {
                    // Define and cast variables explicitly
                    $subject_id = empty($subject_ids[$index]) ? "NULL" : intval($subject_ids[$index]);
                    $min_percentage = floatval($min_percentages[$index]);
                    $max_percentage = floatval($max_percentages[$index]);
                    $passing_mark = floatval($passing_marks[$index]);
                    $cq_passing_percent = floatval($cq_passing_percents[$index]);
                    $mcq_passing_percent = floatval($mcq_passing_percents[$index]);
                    $practical_passing_percent = floatval($practical_passing_percents[$index]);
                    $grade = $conn->real_escape_string($grades[$index]);
                    $grade_point = floatval($grade_points[$index]);
                    $description = $conn->real_escape_string($descriptions[$index]);
                    $record_id = intval($id);
                    
                    // Use direct SQL instead of bind_param to avoid type string issues
                    $direct_sql = "UPDATE passing_marks_config 
                        SET subject_id = $subject_id, 
                            min_percentage = $min_percentage, 
                            max_percentage = $max_percentage, 
                            passing_mark = $passing_mark, 
                            cq_passing_percent = $cq_passing_percent, 
                            mcq_passing_percent = $mcq_passing_percent, 
                            practical_passing_percent = $practical_passing_percent, 
                            grade = '$grade', 
                            grade_point = $grade_point, 
                            description = '$description' 
                        WHERE id = $record_id";
                    
                    $conn->query($direct_sql);
                }
            }

            // Add new record if needed
            if (isset($_POST['new_min_percentage']) && !empty($_POST['new_min_percentage'])) {
                $new_subject_id = empty($_POST['new_subject_id']) ? "NULL" : intval($_POST['new_subject_id']);
                $new_min = floatval($_POST['new_min_percentage']);
                $new_max = floatval($_POST['new_max_percentage']);
                $new_passing = floatval($_POST['new_passing_mark']);
                $new_cq_percent = floatval($_POST['new_cq_passing_percent']);
                $new_mcq_percent = floatval($_POST['new_mcq_passing_percent']);
                $new_practical_percent = floatval($_POST['new_practical_passing_percent']);
                $new_grade = $conn->real_escape_string($_POST['new_grade']);
                $new_grade_point = floatval($_POST['new_grade_point']);
                $new_description = $conn->real_escape_string($_POST['new_description']);

                // Use direct SQL instead of bind_param
                $direct_insert_sql = "INSERT INTO passing_marks_config 
                                    (subject_id, min_percentage, max_percentage, passing_mark, 
                                     cq_passing_percent, mcq_passing_percent, practical_passing_percent, 
                                     grade, grade_point, description) 
                                    VALUES ($new_subject_id, $new_min, $new_max, $new_passing, 
                                            $new_cq_percent, $new_mcq_percent, $new_practical_percent, 
                                            '$new_grade', $new_grade_point, '$new_description')";
                $conn->query($direct_insert_sql);
            }

            // Delete record if requested
            if (isset($_POST['delete_id']) && !empty($_POST['delete_id'])) {
                $delete_id = intval($_POST['delete_id']);
                // Use direct SQL for delete operation
                $direct_delete_sql = "DELETE FROM passing_marks_config WHERE id = $delete_id";
                $conn->query($direct_delete_sql);
            }

            // Commit transaction
            $conn->commit();
            $success_message = "পাসিং মার্কস কনফিগারেশন সফলভাবে সংরক্ষণ করা হয়েছে!";
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error_message = "ত্রুটি: " . $e->getMessage();
        }
    }
}

// Get selected subject
$selected_subject = isset($_GET['subject_id']) ? intval($_GET['subject_id']) : 0;

// Check if subject_id column exists
$check_column_sql = "SHOW COLUMNS FROM passing_marks_config LIKE 'subject_id'";
$column_result = $conn->query($check_column_sql);
$subject_id_exists = ($column_result && $column_result->num_rows > 0);

// Get current configuration
if ($subject_id_exists) {
    $config_sql = "SELECT * FROM passing_marks_config WHERE ";
    if ($selected_subject > 0) {
        $config_sql .= "subject_id = $selected_subject ";
    } else {
        $config_sql .= "subject_id IS NULL ";
    }
    $config_sql .= "ORDER BY min_percentage DESC";
} else {
    // Fallback query if subject_id column doesn't exist
    $config_sql = "SELECT * FROM passing_marks_config ORDER BY min_percentage DESC";
}

$config_result = $conn->query($config_sql);
$configurations = [];

while ($row = $config_result->fetch_assoc()) {
    $configurations[] = $row;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পাসিং মার্কস কনফিগারেশন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .config-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">পাসিং মার্কস কনফিগারেশন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="results.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-clipboard-list"></i> ফলাফল তালিকা
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <!-- Subject selection form -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5><i class="fas fa-filter me-2"></i>বিষয় নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row">
                            <div class="col-md-6 mb-3">
                                <label for="subject_id" class="form-label">বিষয় নির্বাচন করুন</label>
                                <select class="form-select" id="subject_id" name="subject_id" onchange="this.form.submit()">
                                    <option value="0" <?php echo ($selected_subject == 0) ? 'selected' : ''; ?>>সাধারণ গ্রেডিং (সকল বিষয়)</option>
                                    <?php foreach ($subjects as $id => $subject): ?>
                                        <option value="<?php echo $id; ?>" <?php echo ($selected_subject == $id) ? 'selected' : ''; ?>>
                                            <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 text-end align-self-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="config-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cog me-2"></i>
                            <?php if ($selected_subject > 0): ?>
                                <?php echo $subjects[$selected_subject]['subject_name']; ?> - পাসিং মার্কস ম্যাপিং
                            <?php else: ?>
                                সাধারণ পাসিং মার্কস ম্যাপিং (সকল বিষয়)
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>বিষয়</th>
                                            <th>ন্যূনতম শতাংশ</th>
                                            <th>সর্বোচ্চ শতাংশ</th>
                                            <th>পাসিং মার্কস</th>
                                            <th>কিউ পাসিং শতাংশ</th>
                                            <th>মিউ পাসিং শতাংশ</th>
                                            <th>প্রাক্টিকাল পাসিং শতাংশ</th>
                                            <th>গ্রেড</th>
                                            <th>গ্রেড পয়েন্ট</th>
                                            <th>বিবরণ</th>
                                            <th>একশন</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($configurations as $config): ?>
                                        <tr>
                                            <input type="hidden" name="id[]" value="<?php echo $config['id']; ?>">
                                            <td>
                                                <select class="form-select" name="subject_id[]">
                                                    <option value="" <?php echo (is_null($config['subject_id']) ? 'selected' : ''); ?>>সকল বিষয়</option>
                                                    <?php foreach ($subjects as $id => $subject): ?>
                                                        <option value="<?php echo $id; ?>" <?php echo ($config['subject_id'] == $id) ? 'selected' : ''; ?>>
                                                            <?php echo $subject['subject_name']; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="min_percentage[]" value="<?php echo $config['min_percentage']; ?>" required>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="max_percentage[]" value="<?php echo $config['max_percentage']; ?>" required>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="passing_mark[]" value="<?php echo $config['passing_mark']; ?>" required>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="cq_passing_percent[]" value="<?php echo $config['cq_passing_percent']; ?>">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="mcq_passing_percent[]" value="<?php echo $config['mcq_passing_percent']; ?>">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="practical_passing_percent[]" value="<?php echo $config['practical_passing_percent']; ?>">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="grade[]" 
                                                    value="<?php echo $config['grade']; ?>" required>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="5" class="form-control" 
                                                    name="grade_point[]" value="<?php echo $config['grade_point']; ?>" required>
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="description[]" 
                                                    value="<?php echo $config['description']; ?>">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm delete-btn" 
                                                    data-id="<?php echo $config['id']; ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        
                                        <!-- New Record Form -->
                                        <tr class="table-secondary">
                                            <td>
                                                <select class="form-select" name="new_subject_id">
                                                    <option value="">সকল বিষয়</option>
                                                    <?php foreach ($subjects as $id => $subject): ?>
                                                        <option value="<?php echo $id; ?>" <?php echo ($selected_subject == $id) ? 'selected' : ''; ?>>
                                                            <?php echo $subject['subject_name']; ?>
                                                        </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_min_percentage" placeholder="ন্যূনতম %">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_max_percentage" placeholder="সর্বোচ্চ %">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_passing_mark" placeholder="পাসিং মার্কস">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_cq_passing_percent" placeholder="কিউ পাসিং শতাংশ">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_mcq_passing_percent" placeholder="মিউ পাসিং শতাংশ">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="100" class="form-control" 
                                                    name="new_practical_passing_percent" placeholder="প্রাক্টিকাল পাসিং শতাংশ">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="new_grade" 
                                                    placeholder="গ্রেড">
                                            </td>
                                            <td>
                                                <input type="number" step="0.01" min="0" max="5" class="form-control" 
                                                    name="new_grade_point" placeholder="পয়েন্ট">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="new_description" 
                                                    placeholder="বিবরণ">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-success btn-sm" id="add-row-btn">
                                                    <i class="fas fa-plus"></i> নতুন
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <input type="hidden" name="delete_id" id="delete_id" value="">
                            
                            <div class="d-flex justify-content-end mt-3">
                                <button type="submit" name="save_config" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>সেভ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4 mb-5 p-4 bg-white rounded shadow-sm">
                    <h4>মার্কশিট ও গ্রেডিং ব্যবস্থা</h4>
                    <p>পাসিং মার্কস এবং গ্রেড ম্যাপিং নিম্নোক্ত রেজাল্ট পরিচালনা সিস্টেমে ব্যবহৃত হবে:</p>
                    <ul>
                        <li>ছাত্র/ছাত্রীর মার্কশিট</li>
                        <li>টেবুলেশন শিট</li>
                        <li>ফলাফল তালিকা</li>
                        <li>মোট ফলাফল প্রকাশ</li>
                    </ul>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        একজন ছাত্র/ছাত্রীর পাস করার জন্য, প্রতিটি বিষয়ে নির্ধারিত পাসিং মার্কস অর্জন করতে হবে।
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Handle delete button click
            $('.delete-btn').click(function() {
                if (confirm('আপনি কি নিশ্চিত যে আপনি এই রেকর্ডটি মুছতে চান?')) {
                    $('#delete_id').val($(this).data('id'));
                    $('form').submit();
                }
            });
            
            // Add row button functionality
            $('#add-row-btn').click(function() {
                var hasEmptyFields = false;
                
                $('input[name^="new_"]').each(function() {
                    if ($(this).val() === '') {
                        hasEmptyFields = true;
                    }
                });
                
                if (hasEmptyFields) {
                    alert('দয়া করে সকল ঘর পূরণ করুন।');
                } else {
                    $('form').submit();
                }
            });
            
            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
    </script>
</body>
</html> 