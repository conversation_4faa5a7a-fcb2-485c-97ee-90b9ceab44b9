<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Create fee_payments table if it doesn't exist
$createPaymentsTableQuery = "CREATE TABLE IF NOT EXISTS fee_payments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    fee_id INT(11) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL DEFAULT CURRENT_DATE,
    payment_method VARCHAR(50) DEFAULT 'cash',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (fee_id) REFERENCES fees(id) ON DELETE CASCADE
)";
$conn->query($createPaymentsTableQuery);

// Handle date filters
$fromDate = isset($_GET['from_date']) ? $_GET['from_date'] : date('Y-m-01'); // First day of current month
$toDate = isset($_GET['to_date']) ? $_GET['to_date'] : date('Y-m-d'); // Today

// Handle payment method filter
$paymentMethod = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';

// Handle class filter
$classFilter = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;

// Get classes for filter dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get payment methods for filter dropdown
$paymentMethodsQuery = "SELECT DISTINCT payment_method FROM fee_payments";
$paymentMethods = $conn->query($paymentMethodsQuery);

// Pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$itemsPerPage = 20;
$offset = ($page - 1) * $itemsPerPage;

// Build WHERE clause for filtering
$whereClause = "WHERE fp.payment_date BETWEEN ? AND ?";
$params = [$fromDate, $toDate];
$types = "ss";

if (!empty($paymentMethod)) {
    $whereClause .= " AND fp.payment_method = ?";
    $params[] = $paymentMethod;
    $types .= "s";
}

if ($classFilter > 0) {
    $whereClause .= " AND c.id = ?";
    $params[] = $classFilter;
    $types .= "i";
}

// Create copies of the params and types for the statistic queries
$statsParams = $params;
$statsTypes = $types;

// Count total records for pagination
$countQuery = "SELECT COUNT(*) as total FROM fee_payments fp
              JOIN fees f ON fp.fee_id = f.id
              JOIN students s ON f.student_id = s.id
              JOIN classes c ON s.class_id = c.id
              $whereClause";
              
$stmt = $conn->prepare($countQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$totalResult = $stmt->get_result();
$totalRows = $totalResult->fetch_assoc()['total'];
$totalPages = ceil($totalRows / $itemsPerPage);

// Add pagination parameters to the params array
$params[] = $itemsPerPage;
$params[] = $offset;
$types .= "ii";

// Get payments for the current page
$paymentsQuery = "SELECT fp.id, fp.fee_id, fp.payment_date, fp.payment_method, fp.notes, fp.amount,
                 f.fee_type, f.student_id, s.first_name, s.last_name, s.student_id as roll,
                 c.class_name
                 FROM fee_payments fp
                 JOIN fees f ON fp.fee_id = f.id
                 JOIN students s ON f.student_id = s.id
                 JOIN classes c ON s.class_id = c.id
                 $whereClause
                 ORDER BY fp.payment_date DESC, fp.id DESC
                 LIMIT ? OFFSET ?";

$stmt = $conn->prepare($paymentsQuery);
$stmt->bind_param($types, ...$params);
$stmt->execute();
$payments = $stmt->get_result();

// Get payment statistics
$statsQuery = "SELECT 
                COUNT(*) as total_transactions,
                SUM(fp.amount) as total_amount,
                COUNT(DISTINCT fee_id) as unique_fees,
                COUNT(DISTINCT f.student_id) as unique_students
              FROM fee_payments fp
              JOIN fees f ON fp.fee_id = f.id
              $whereClause";
              
$stmt = $conn->prepare($statsQuery);
if (!empty($statsParams)) {
    $stmt->bind_param($statsTypes, ...$statsParams);
}
$stmt->execute();
$stats = $stmt->get_result()->fetch_assoc();

// Get payment methods breakdown
$methodsBreakdownQuery = "SELECT 
                          payment_method,
                          COUNT(*) as count,
                          SUM(fp.amount) as total
                        FROM fee_payments fp
                        JOIN fees f ON fp.fee_id = f.id
                        $whereClause
                        GROUP BY payment_method
                        ORDER BY total DESC";
                        
$stmt = $conn->prepare($methodsBreakdownQuery);
if (!empty($statsParams)) {
    $stmt->bind_param($statsTypes, ...$statsParams);
}
$stmt->execute();
$methodsBreakdown = $stmt->get_result();

// Get daily payment totals for chart
$dailyTotalsQuery = "SELECT 
                      DATE(fp.payment_date) as date,
                      SUM(fp.amount) as total
                    FROM fee_payments fp
                    JOIN fees f ON fp.fee_id = f.id
                    $whereClause
                    GROUP BY DATE(fp.payment_date)
                    ORDER BY date";
                    
$stmt = $conn->prepare($dailyTotalsQuery);
if (!empty($statsParams)) {
    $stmt->bind_param($statsTypes, ...$statsParams);
}
$stmt->execute();
$dailyTotals = $stmt->get_result();

// Format chart data
$chartDates = [];
$chartAmounts = [];
while ($row = $dailyTotals->fetch_assoc()) {
    $chartDates[] = date('d/m/Y', strtotime($row['date']));
    $chartAmounts[] = $row['total'];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট ড্যাশবোর্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stats-card {
            text-align: center;
            padding: 15px;
        }
        .stats-card .number {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        .stats-card .label {
            color: #6c757d;
        }
        .payment-method-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .cash {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .bank {
            background-color: #cfe2ff;
            color: #084298;
        }
        .bkash {
            background-color: #e8d4f7;
            color: #6f42c1;
        }
        .nagad {
            background-color: #f8d7da;
            color: #842029;
        }
        .rocket {
            background-color: #fff3cd;
            color: #664d03;
        }
        .other {
            background-color: #e2e3e5;
            color: #41464b;
        }
        .date-filter {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="payment_dashboard.php">
                            <i class="fas fa-chart-line me-2"></i> পেমেন্ট ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i> পেমেন্ট ড্যাশবোর্ড</h5>
                            <a href="fees.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i> ফি পেজে ফিরে যান
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Success/Error Messages -->
                        <?php if (isset($_SESSION['success'])): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?= $_SESSION['success'] ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <?php unset($_SESSION['success']); ?>
                        <?php endif; ?>
                        
                        <?php if (isset($_SESSION['error'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?= $_SESSION['error'] ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            <?php unset($_SESSION['error']); ?>
                        <?php endif; ?>
                        
                        <!-- Date Filter -->
                        <div class="date-filter">
                            <form method="GET" action="" class="row g-3">
                                <div class="col-md-3">
                                    <label for="from_date" class="form-label">শুরুর তারিখ</label>
                                    <input type="date" class="form-control" id="from_date" name="from_date" value="<?= $fromDate ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="to_date" class="form-label">শেষ তারিখ</label>
                                    <input type="date" class="form-control" id="to_date" name="to_date" value="<?= $toDate ?>">
                                </div>
                                <div class="col-md-2">
                                    <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                                    <select class="form-select" id="payment_method" name="payment_method">
                                        <option value="">সব</option>
                                        <?php if ($paymentMethods && $paymentMethods->num_rows > 0): ?>
                                            <?php while ($method = $paymentMethods->fetch_assoc()): ?>
                                                <option value="<?= $method['payment_method'] ?>" <?= ($method['payment_method'] === $paymentMethod) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars(ucfirst($method['payment_method'])) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="class_id" class="form-label">ক্লাস</label>
                                    <select class="form-select" id="class_id" name="class_id">
                                        <option value="0">সব</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?= $class['id'] ?>" <?= ($class['id'] == $classFilter) ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($class['class_name']) ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-filter me-2"></i> ফিল্টার করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                        
                        <!-- Stats Cards -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card stats-card bg-primary text-white">
                                    <div class="label">মোট পেমেন্ট</div>
                                    <div class="number"><?= number_format($stats['total_transactions']) ?></div>
                                    <div class="small">ট্রানজেকশন</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card bg-success text-white">
                                    <div class="label">মোট পরিমাণ</div>
                                    <div class="number">৳ <?= number_format($stats['total_amount'], 2) ?></div>
                                    <div class="small">এই সময়কালে</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card bg-info text-white">
                                    <div class="label">মোট শিক্ষার্থী</div>
                                    <div class="number"><?= number_format($stats['unique_students']) ?></div>
                                    <div class="small">যারা পেমেন্ট করেছে</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card stats-card bg-warning text-dark">
                                    <div class="label">মোট ফি আইটেম</div>
                                    <div class="number"><?= number_format($stats['unique_fees']) ?></div>
                                    <div class="small">পেমেন্ট করা হয়েছে</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Charts Row -->
                        <div class="row mb-4">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-chart-area me-2"></i> দৈনিক পেমেন্ট ট্রেন্ড</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="paymentTrendChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0"><i class="fas fa-chart-pie me-2"></i> পেমেন্ট পদ্ধতি বিভাজন</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>পদ্ধতি</th>
                                                        <th>ট্রানজেকশন</th>
                                                        <th>পরিমাণ</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php if ($methodsBreakdown && $methodsBreakdown->num_rows > 0): ?>
                                                        <?php while ($method = $methodsBreakdown->fetch_assoc()): ?>
                                                            <tr>
                                                                <td>
                                                                    <span class="payment-method-badge <?= strtolower($method['payment_method']) ?>">
                                                                        <?= htmlspecialchars(ucfirst($method['payment_method'])) ?>
                                                                    </span>
                                                                </td>
                                                                <td><?= number_format($method['count']) ?></td>
                                                                <td>৳ <?= number_format($method['total'], 2) ?></td>
                                                            </tr>
                                                        <?php endwhile; ?>
                                                    <?php else: ?>
                                                        <tr>
                                                            <td colspan="3" class="text-center text-muted">কোন ডেটা পাওয়া যায়নি</td>
                                                        </tr>
                                                    <?php endif; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payments Table -->
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-list me-2"></i> সাম্প্রতিক পেমেন্ট রেকর্ড</h6>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead class="table-light">
                                            <tr>
                                                <th>তারিখ</th>
                                                <th>শিক্ষার্থী</th>
                                                <th>ক্লাস</th>
                                                <th>ফি টাইপ</th>
                                                <th>পরিমাণ</th>
                                                <th>পেমেন্ট পদ্ধতি</th>
                                                <th>নোট</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($payments && $payments->num_rows > 0): ?>
                                                <?php while ($payment = $payments->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                                                        <td>
                                                            <a href="edit_student.php?id=<?= $payment['student_id'] ?>" class="text-decoration-none">
                                                                <?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?>
                                                                <small class="text-muted">(<?= $payment['roll'] ?>)</small>
                                                            </a>
                                                        </td>
                                                        <td><?= htmlspecialchars($payment['class_name']) ?></td>
                                                        <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                                                        <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                                        <td>
                                                            <span class="payment-method-badge <?= strtolower($payment['payment_method']) ?>">
                                                                <?= htmlspecialchars(ucfirst($payment['payment_method'])) ?>
                                                            </span>
                                                        </td>
                                                        <td><?= htmlspecialchars($payment['notes'] ?: '-') ?></td>
                                                        <td>
                                                            <a href="edit_fee.php?id=<?= $payment['fee_id'] ?>" class="btn btn-sm btn-primary" title="View">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-success collect-payment" 
                                                                data-fee-id="<?= $payment['fee_id'] ?>" 
                                                                data-student-name="<?= htmlspecialchars($payment['first_name'] . ' ' . $payment['last_name']) ?>"
                                                                title="Collect Payment">
                                                                <i class="fas fa-money-bill-wave"></i>
                                                            </button>
                                                            <a href="edit_payment.php?id=<?= $payment['id'] ?>" class="btn btn-sm btn-warning" title="Edit Payment">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-sm btn-danger delete-payment" 
                                                                data-id="<?= $payment['id'] ?>"
                                                                data-amount="<?= $payment['amount'] ?>"
                                                                data-date="<?= date('d/m/Y', strtotime($payment['payment_date'])) ?>"
                                                                title="Delete Payment">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="8" class="text-center text-muted">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <!-- Pagination -->
                                <?php if ($totalPages > 1): ?>
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination justify-content-center">
                                            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                                <a class="page-link" href="?page=<?= $page - 1 ?>&from_date=<?= $fromDate ?>&to_date=<?= $toDate ?>&payment_method=<?= $paymentMethod ?>&class_id=<?= $classFilter ?>">আগের</a>
                                            </li>
                                            
                                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                                <li class="page-item <?= ($page == $i) ? 'active' : '' ?>">
                                                    <a class="page-link" href="?page=<?= $i ?>&from_date=<?= $fromDate ?>&to_date=<?= $toDate ?>&payment_method=<?= $paymentMethod ?>&class_id=<?= $classFilter ?>"><?= $i ?></a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <li class="page-item <?= ($page >= $totalPages) ? 'disabled' : '' ?>">
                                                <a class="page-link" href="?page=<?= $page + 1 ?>&from_date=<?= $fromDate ?>&to_date=<?= $toDate ?>&payment_method=<?= $paymentMethod ?>&class_id=<?= $classFilter ?>">পরের</a>
                                            </li>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS & jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Payment Collection Modal -->
    <div class="modal fade" id="collectPaymentModal" tabindex="-1" aria-labelledby="collectPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="collectPaymentModalLabel"><i class="fas fa-money-bill-wave me-2"></i> পেমেন্ট সংগ্রহ করুন</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="collectPaymentForm" action="collect_payment.php" method="POST">
                    <div class="modal-body">
                        <input type="hidden" id="fee_id" name="fee_id">
                        <div class="mb-3">
                            <label for="student_name" class="form-label">শিক্ষার্থী</label>
                            <input type="text" class="form-control" id="student_name" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="amount" class="form-label">পরিমাণ (৳)</label>
                            <input type="number" class="form-control" id="amount" name="amount" min="1" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                            <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?= date('Y-m-d') ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                            <select class="form-select" id="payment_method" name="payment_method" required>
                                <option value="cash">Cash</option>
                                <option value="bank">Bank</option>
                                <option value="bkash">bKash</option>
                                <option value="nagad">Nagad</option>
                                <option value="rocket">Rocket</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">নোট</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" class="btn btn-success">পেমেন্ট সংরক্ষণ করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Payment Modal -->
    <div class="modal fade" id="deletePaymentModal" tabindex="-1" aria-labelledby="deletePaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deletePaymentModalLabel"><i class="fas fa-trash me-2"></i> পেমেন্ট মুছে ফেলুন</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি এই পেমেন্টটি মুছে ফেলতে চান?</p>
                    <div class="alert alert-warning">
                        <p class="mb-1"><strong>পেমেন্ট আইডি:</strong> <span id="delete_payment_id"></span></p>
                        <p class="mb-1"><strong>পরিমাণ:</strong> ৳ <span id="delete_payment_amount"></span></p>
                        <p class="mb-0"><strong>তারিখ:</strong> <span id="delete_payment_date"></span></p>
                    </div>
                    <p class="text-danger"><strong>সতর্কতা:</strong> এই অ্যাকশনটি অপরিবর্তনীয়!</p>
                </div>
                <form id="deletePaymentForm" action="delete_payment.php" method="POST">
                    <input type="hidden" id="delete_id" name="id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল করুন</button>
                        <button type="submit" class="btn btn-danger">মুছে ফেলুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        $(document).ready(function() {
            // Payment Trend Chart
            var ctx = document.getElementById('paymentTrendChart').getContext('2d');
            var paymentTrendChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: <?= json_encode($chartDates) ?>,
                    datasets: [{
                        label: 'দৈনিক পেমেন্ট (টাকা)',
                        data: <?= json_encode($chartAmounts) ?>,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 2,
                        tension: 0.1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '৳ ' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'পেমেন্ট: ৳ ' + context.raw.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            
            // Handle Collect Payment Button
            $('.collect-payment').click(function() {
                var feeId = $(this).data('fee-id');
                var studentName = $(this).data('student-name');
                
                $('#fee_id').val(feeId);
                $('#student_name').val(studentName);
                
                $('#collectPaymentModal').modal('show');
            });
            
            // Handle Delete Payment Button
            $('.delete-payment').click(function() {
                var id = $(this).data('id');
                var amount = $(this).data('amount');
                var date = $(this).data('date');
                
                $('#delete_id').val(id);
                $('#delete_payment_id').text(id);
                $('#delete_payment_amount').text(amount);
                $('#delete_payment_date').text(date);
                
                $('#deletePaymentModal').modal('show');
            });
        });
    </script>
</body>
</html> 