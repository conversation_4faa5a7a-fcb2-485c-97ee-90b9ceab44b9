<?php
session_start();
require_once 'includes/db_connection.php';
require_once 'includes/functions.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Get exam ID from URL if provided
$exam_id = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;

// Get all exams for dropdown
$exams_query = "SELECT id, exam_name FROM exams ORDER BY exam_name";
$exams_result = $conn->query($exams_query);

// Prepare schedules query
if ($exam_id > 0) {
    // Get schedules for specific exam
    $schedules_query = "SELECT es.id, e.exam_name, s.subject_name, es.exam_date, es.start_time, es.end_time, es.room_no 
                    FROM exam_schedule es
                    JOIN exams e ON es.exam_id = e.id
                    JOIN subjects s ON es.subject_id = s.id
                    WHERE es.exam_id = ?
                    ORDER BY es.exam_date, es.start_time, s.subject_name";
    $schedule_stmt = $conn->prepare($schedules_query);
    $schedule_stmt->bind_param("i", $exam_id);
    $schedule_stmt->execute();
    $schedules_result = $schedule_stmt->get_result();
    
    // Get exam name
    $exam_name_query = "SELECT exam_name FROM exams WHERE id = ?";
    $exam_name_stmt = $conn->prepare($exam_name_query);
    $exam_name_stmt->bind_param("i", $exam_id);
    $exam_name_stmt->execute();
    $exam_result = $exam_name_stmt->get_result();
    $exam_data = $exam_result->fetch_assoc();
    $current_exam_name = $exam_data ? $exam_data['exam_name'] : 'অজানা পরীক্ষা';
} else {
    // Get all schedules
    $schedules_query = "SELECT es.id, e.exam_name, s.subject_name, es.exam_date, es.start_time, es.end_time, es.room_no 
                    FROM exam_schedule es
                    JOIN exams e ON es.exam_id = e.id
                    JOIN subjects s ON es.subject_id = s.id
                    ORDER BY e.exam_name, es.exam_date, es.start_time, s.subject_name";
    $schedules_result = $conn->query($schedules_query);
    $current_exam_name = 'সমস্ত পরীক্ষা';
}

// Group schedules by date
$grouped_schedules = [];
while ($schedule = $schedules_result->fetch_assoc()) {
    $date = $schedule['exam_date'];
    $exam_name = $schedule['exam_name'];
    
    if (!isset($grouped_schedules[$exam_name])) {
        $grouped_schedules[$exam_name] = [];
    }
    
    if (!isset($grouped_schedules[$exam_name][$date])) {
        $grouped_schedules[$exam_name][$date] = [];
    }
    
    $grouped_schedules[$exam_name][$date][] = $schedule;
}
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরীক্ষার সময়সূচি প্রিন্ট - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                margin: 0;
                padding: 0;
                font-size: 12pt;
            }
            .no-print {
                display: none !important;
            }
            .page-header, .page-footer {
                display: block;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                page-break-inside: auto;
            }
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            thead {
                display: table-header-group;
            }
            tfoot {
                display: table-footer-group;
            }
            .new-exam {
                page-break-before: always;
            }
            .table-bordered th, .table-bordered td {
                border: 1px solid #000 !important;
            }
        }
        
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            line-height: 1.5;
        }
        .page-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .print-container {
            max-width: 210mm; /* A4 width */
            margin: 0 auto;
            padding: 15px;
        }
        .schedule-table th, .schedule-table td {
            padding: 8px;
            border: 1px solid #333;
        }
        .schedule-table th {
            background-color: #f2f2f2;
        }
        .school-info {
            text-align: center;
            margin-bottom: 15px;
        }
        .school-name {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .school-address {
            font-size: 12pt;
            margin-bottom: 5px;
        }
        .exam-name {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin: 15px 0;
        }
        .date-generated {
            text-align: right;
            font-size: 10pt;
            margin-top: 10px;
            font-style: italic;
        }
        .action-buttons {
            margin: 20px 0;
        }
        .schedule-date {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .exam-section {
            margin-bottom: 30px;
        }
        .exam-title {
            font-size: 16pt;
            font-weight: bold;
            margin: 20px 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="action-buttons no-print">
            <div class="d-flex justify-content-between mb-3">
                <a href="exam_schedule.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> ফিরে যান
                </a>
                <div>
                    <button type="button" class="btn btn-primary me-2" onclick="window.print()">
                        <i class="fas fa-print"></i> প্রিন্ট করুন
                    </button>
                    <button type="button" class="btn btn-success" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> PDF হিসেবে সংরক্ষণ করুন
                    </button>
                </div>
            </div>
            
            <div class="btn-group mb-3">
                <a href="exam_dashboard.php" class="btn btn-outline-secondary">
                    <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                </a>
                <a href="exam_schedule.php" class="btn btn-outline-secondary">
                    <i class="fas fa-calendar-alt"></i> সময়সূচি ব্যবস্থাপনা
                </a>
                <a href="manage_exams.php" class="btn btn-outline-secondary">
                    <i class="fas fa-tasks"></i> পরীক্ষা সম্পাদনা
                </a>
            </div>
            
            <form method="GET" class="mb-4 no-print">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="exam_filter" class="form-label">পরীক্ষা নির্বাচন করুন:</label>
                        <select class="form-select" id="exam_filter" name="exam_id" onchange="this.form.submit()">
                            <option value="0">সমস্ত পরীক্ষা</option>
                            <?php 
                            $exams_result->data_seek(0);
                            while ($exam = $exams_result->fetch_assoc()) : 
                                $selected = ($exam_id == $exam['id']) ? 'selected' : '';
                            ?>
                            <option value="<?php echo $exam['id']; ?>" <?php echo $selected; ?>>
                                <?php echo $exam['exam_name']; ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="page-header">
            <div class="school-info">
                <div class="school-name"><?php echo SITE_NAME; ?></div>
                <div class="school-address">বাংলাদেশ</div>
            </div>
            <div class="exam-name"><?php echo $current_exam_name; ?> - সময়সূচি</div>
        </div>
        
        <?php if (!empty($grouped_schedules)) : ?>
            <?php 
            $exam_counter = 0;
            foreach ($grouped_schedules as $exam_name => $dates) : 
                $exam_counter++;
                $first_page = ($exam_counter == 1) ? '' : 'new-exam';
            ?>
                <?php if ($exam_id == 0) : // Only show exam titles in "all exams" view ?>
                <div class="exam-section <?php echo $first_page; ?>">
                    <div class="exam-title"><?php echo $exam_name; ?></div>
                <?php endif; ?>
                
                <table class="table table-bordered schedule-table mb-4">
                    <thead>
                        <tr>
                            <th width="20%">তারিখ</th>
                            <th width="40%">বিষয়</th>
                            <th width="20%">সময়</th>
                            <th width="20%">কক্ষ নম্বর</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        foreach ($dates as $date => $schedules) : 
                            $formatted_date = date('d/m/Y (l)', strtotime($date));
                            $subjects = [];
                            $times = [];
                            $rooms = [];
                            
                            foreach ($schedules as $schedule) {
                                $subjects[] = $schedule['subject_name'];
                                $time = date('h:i A', strtotime($schedule['start_time'])) . ' - ' . 
                                        date('h:i A', strtotime($schedule['end_time']));
                                $times[$time] = $time;
                                $rooms[] = $schedule['room_no'];
                            }
                            
                            // Remove duplicates and join with comma
                            $subjects_str = implode(', ', $subjects);
                            $times_str = implode('<br>', array_values($times));
                            $rooms_str = implode(', ', array_unique($rooms));
                        ?>
                        <tr>
                            <td class="schedule-date"><?php echo $formatted_date; ?></td>
                            <td><?php echo $subjects_str; ?></td>
                            <td class="text-center"><?php echo $times_str; ?></td>
                            <td class="text-center"><?php echo $rooms_str; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if ($exam_id == 0) : ?>
                </div>
                <?php endif; ?>
            <?php endforeach; ?>
            
            <div class="date-generated">
                প্রিন্ট করার তারিখ: <?php echo date('d/m/Y h:i A'); ?>
            </div>
        <?php else : ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> কোন পরীক্ষার সময়সূচি পাওয়া যায়নি।
            </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        function exportToPDF() {
            const element = document.querySelector('.print-container');
            const buttons = document.querySelector('.action-buttons');
            
            // Temporarily hide buttons
            buttons.style.display = 'none';
            
            const opt = {
                margin: [10, 10, 10, 10],
                filename: '<?php echo $current_exam_name; ?>_সময়সূচি.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { scale: 2 },
                jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
            };
            
            // Generate PDF
            html2pdf().set(opt).from(element).save().then(() => {
                // Show buttons again
                buttons.style.display = 'block';
            });
        }
    </script>
</body>
</html> 