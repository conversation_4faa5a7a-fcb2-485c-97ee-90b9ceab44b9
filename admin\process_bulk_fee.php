<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if required fields are provided
    if (isset($_POST['fee_type']) && !empty($_POST['fee_type']) && 
        isset($_POST['amount']) && !empty($_POST['amount']) && 
        isset($_POST['due_date']) && !empty($_POST['due_date']) && 
        isset($_POST['action']) && $_POST['action'] === 'add_bulk_fee') {
        
        $feeType = $_POST['fee_type'];
        $amount = $_POST['amount'];
        $dueDate = $_POST['due_date'];
        $sessionId = $_POST['session_id'] ?? null;
        $classId = $_POST['class_id'] ?? null;
        
        // Check if student_ids array is provided
        if (isset($_POST['student_ids']) && !empty($_POST['student_ids'])) {
            $studentIds = $_POST['student_ids'];
            
            // Prepare insert statement for fees
            $insertQuery = "INSERT INTO fees (student_id, fee_type, amount, due_date, payment_status) VALUES (?, ?, ?, ?, 'due')";
            $insertStmt = $conn->prepare($insertQuery);
            
            $successCount = 0;
            $errorCount = 0;
            
            // Insert fees for each student
            foreach ($studentIds as $studentId) {
                $insertStmt->bind_param("isds", $studentId, $feeType, $amount, $dueDate);
                
                if ($insertStmt->execute()) {
                    $successCount++;
                } else {
                    $errorCount++;
                }
            }
            
            if ($successCount > 0) {
                $_SESSION['successMessage'] = "$successCount জন শিক্ষার্থীর ফি সফলভাবে যোগ করা হয়েছে!";
                
                if ($errorCount > 0) {
                    $_SESSION['errorMessage'] = "$errorCount জন শিক্ষার্থীর ফি যোগ করতে সমস্যা হয়েছে!";
                }
            } else {
                $_SESSION['errorMessage'] = "কোন ফি যোগ করা হয়নি! সমস্যা হয়েছে।";
            }
            
            $insertStmt->close();
        } else {
            $_SESSION['errorMessage'] = "কোন শিক্ষার্থী নির্বাচন করা হয়নি!";
        }
    } else if (isset($_POST['action']) && $_POST['action'] === 'get_classes_by_session') {
        // Return classes for the selected session
        if (isset($_POST['session_id']) && !empty($_POST['session_id'])) {
            $sessionId = $_POST['session_id'];
            
            $classesQuery = "SELECT DISTINCT c.id, c.class_name 
                            FROM classes c 
                            INNER JOIN students s ON s.class_id = c.id 
                            WHERE s.session_id = ? 
                            ORDER BY c.class_name";
            
            $stmt = $conn->prepare($classesQuery);
            $stmt->bind_param("i", $sessionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $classes = [];
            
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $classes[] = $row;
                }
            }
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode($classes);
            exit();
        }
    } else if (isset($_POST['action']) && $_POST['action'] === 'get_students_by_class_session') {
        // Return students for the selected class and session
        if (isset($_POST['class_id']) && !empty($_POST['class_id']) && 
            isset($_POST['session_id']) && !empty($_POST['session_id'])) {
            
            $classId = $_POST['class_id'];
            $sessionId = $_POST['session_id'];
            
            $studentsQuery = "SELECT s.id, s.student_id as roll, s.first_name, s.last_name  
                             FROM students s 
                             WHERE s.class_id = ? AND s.session_id = ? 
                             ORDER BY s.first_name, s.last_name";
            
            $stmt = $conn->prepare($studentsQuery);
            $stmt->bind_param("ii", $classId, $sessionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $students = [];
            
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $students[] = $row;
                }
            }
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode($students);
            exit();
        }
    } else {
        $_SESSION['errorMessage'] = "সকল প্রয়োজনীয় তথ্য দিন!";
    }
    
    header("Location: fees.php");
    exit();
}

// If not a POST request, redirect to fees page
header("Location: fees.php");
exit();
?> 