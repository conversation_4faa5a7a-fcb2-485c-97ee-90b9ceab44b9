<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$successMessage = "";
$errorMessage = "";
$totalDuplicates = 0;
$removedDuplicates = 0;

// Function to remove duplicate fees
function removeDuplicateFees($conn) {
    // Start a transaction for safe removal
    $conn->begin_transaction();
    
    try {
        // First, get a list of all duplicate fees
        $findDuplicatesQuery = "
            SELECT student_id, fee_type, COUNT(*) as count, GROUP_CONCAT(id ORDER BY id) as ids
            FROM fees 
            GROUP BY student_id, fee_type
            HAVING COUNT(*) > 1
        ";
        
        $duplicateResult = $conn->query($findDuplicatesQuery);
        $totalDuplicates = $duplicateResult->num_rows;
        $removedDuplicates = 0;
        
        if ($totalDuplicates > 0) {
            while ($row = $duplicateResult->fetch_assoc()) {
                $studentId = $row['student_id'];
                $feeType = $row['fee_type'];
                $ids = explode(',', $row['ids']);
                
                // Keep the first ID (oldest entry)
                $keepId = $ids[0];
                
                // Set the maximum paid amount and appropriate status
                $updateQuery = "
                    UPDATE fees 
                    SET paid = (
                        SELECT MAX(total_paid) 
                        FROM (
                            SELECT SUM(paid) as total_paid 
                            FROM fees 
                            WHERE student_id = ? AND fee_type = ?
                        ) as tmp
                    ),
                    payment_status = CASE 
                        WHEN paid = 0 THEN 'due'
                        WHEN paid >= amount THEN 'paid'
                        ELSE 'partial'
                    END
                    WHERE id = ?
                ";
                
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("isi", $studentId, $feeType, $keepId);
                $updateStmt->execute();
                
                // Remove all others
                array_shift($ids); // Remove the first element that we're keeping
                
                if (!empty($ids)) {
                    $placeholders = str_repeat('?,', count($ids) - 1) . '?';
                    $deleteQuery = "DELETE FROM fees WHERE id IN ($placeholders)";
                    
                    $deleteStmt = $conn->prepare($deleteQuery);
                    
                    $types = str_repeat('i', count($ids));
                    $deleteStmt->bind_param($types, ...$ids);
                    $deleteStmt->execute();
                    
                    $removedDuplicates += $deleteStmt->affected_rows;
                }
            }
            
            // Commit the transaction
            $conn->commit();
        }
        
        return ['total' => $totalDuplicates, 'removed' => $removedDuplicates];
    } catch (Exception $e) {
        // Roll back in case of error
        $conn->rollback();
        throw $e;
    }
}

// Process the fee removal if submitted
if (isset($_POST['remove_duplicates'])) {
    try {
        $result = removeDuplicateFees($conn);
        $totalDuplicates = $result['total'];
        $removedDuplicates = $result['removed'];
        
        if ($totalDuplicates > 0) {
            $successMessage = "$removedDuplicates টি ডুপ্লিকেট ফি সফলভাবে অপসারণ করা হয়েছে।";
        } else {
            $successMessage = "কোন ডুপ্লিকেট ফি পাওয়া যায়নি।";
        }
    } catch (Exception $e) {
        $errorMessage = "ত্রুটি: " . $e->getMessage();
    }
}

// Count remaining duplicates for display
$countQuery = "
    SELECT COUNT(*) as remaining_duplicates
    FROM (
        SELECT student_id, fee_type, COUNT(*) as count
        FROM fees 
        GROUP BY student_id, fee_type
        HAVING COUNT(*) > 1
    ) as duplicates
";

$countResult = $conn->query($countQuery);
$remainingDuplicates = 0;

if ($countResult && $countResult->num_rows > 0) {
    $remainingRow = $countResult->fetch_assoc();
    $remainingDuplicates = $remainingRow['remaining_duplicates'];
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ডুপ্লিকেট ফি অপসারণ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="container-fluid px-4">
                    <h1 class="mt-4">ডুপ্লিকেট ফি অপসারণ</h1>
                    <ol class="breadcrumb mb-4">
                        <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                        <li class="breadcrumb-item"><a href="fees.php">ফি ম্যানেজমেন্ট</a></li>
                        <li class="breadcrumb-item active">ডুপ্লিকেট ফি অপসারণ</li>
                    </ol>
                    
                    <?php if (!empty($successMessage)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i> <?php echo $successMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($errorMessage)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card mb-4">
                        <div class="card-header">
                            <i class="fas fa-trash-alt me-1"></i> ডুপ্লিকেট ফি অপসারণ
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> এই পৃষ্ঠা ব্যবহার করে আপনি সিস্টেম থেকে ডুপ্লিকেট ফি রেকর্ডগুলি অপসারণ করতে পারেন। এটি একই শিক্ষার্থীর একই ধরনের ফির জন্য একাধিক এন্ট্রি খুঁজে বের করবে এবং অপসারণ করবে।
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-light mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">ডুপ্লিকেট ফি এর অবস্থাঃ</h5>
                                            <p class="card-text">
                                                <?php if ($remainingDuplicates > 0): ?>
                                                <span class="text-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i> সিস্টেমে <?php echo $remainingDuplicates; ?> টি ডুপ্লিকেট ফি রেকর্ড পাওয়া গেছে।
                                                </span>
                                                <?php else: ?>
                                                <span class="text-success">
                                                    <i class="fas fa-check-circle me-1"></i> সিস্টেমে কোন ডুপ্লিকেট ফি রেকর্ড পাওয়া যায়নি।
                                                </span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card border-primary mb-3">
                                        <div class="card-body">
                                            <h5 class="card-title">অপসারণ প্রক্রিয়া</h5>
                                            <p class="card-text">ডুপ্লিকেট অপসারণের সময় সিস্টেম সবচেয়ে পুরোনো রেকর্ড রাখবে এবং অন্যগুলো মুছে ফেলবে।</p>
                                            <form method="post" action="">
                                                <?php if ($remainingDuplicates > 0): ?>
                                                <button type="submit" name="remove_duplicates" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সমস্ত ডুপ্লিকেট ফি অপসারণ করতে চান?');">
                                                    <i class="fas fa-trash-alt me-1"></i> ডুপ্লিকেট ফি অপসারণ করুন
                                                </button>
                                                <?php else: ?>
                                                <button type="submit" name="remove_duplicates" class="btn btn-secondary" disabled>
                                                    <i class="fas fa-check-circle me-1"></i> কোন ডুপ্লিকেট ফি নেই
                                                </button>
                                                <?php endif; ?>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <a href="fees.php" class="btn btn-primary">
                                    <i class="fas fa-arrow-left me-1"></i> ফি পেজে ফিরে যান
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 