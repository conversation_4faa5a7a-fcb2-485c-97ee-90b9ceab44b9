<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Initialize message variables
$success = false;
$message = '';

// Process reset request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_reset'])) {
    // Begin transaction to ensure all operations succeed or fail together
    $conn->begin_transaction();
    
    try {
        // Temporarily disable foreign key checks
        $conn->query("SET FOREIGN_KEY_CHECKS = 0");
        
        // Truncate fee_payments table if it exists
        $checkPaymentsTable = "SHOW TABLES LIKE 'fee_payments'";
        $result = $conn->query($checkPaymentsTable);
        if ($result && $result->num_rows > 0) {
            $truncatePayments = "TRUNCATE TABLE fee_payments";
            $conn->query($truncatePayments);
            error_log("Truncated fee_payments table");
        }
        
        // Truncate payment_receipts table if it exists
        $checkReceiptsTable = "SHOW TABLES LIKE 'payment_receipts'";
        $result = $conn->query($checkReceiptsTable);
        if ($result && $result->num_rows > 0) {
            $truncateReceipts = "TRUNCATE TABLE payment_receipts";
            $conn->query($truncateReceipts);
            error_log("Truncated payment_receipts table");
        }
        
        // Truncate fees table
        $truncateFees = "TRUNCATE TABLE fees";
        $conn->query($truncateFees);
        error_log("Truncated fees table");
        
        // Re-enable foreign key checks
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        
        // Commit the transaction
        $conn->commit();
        
        $success = true;
        $message = "সমস্ত ফি ডাটা সফলভাবে মুছে ফেলা হয়েছে!";
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        
        // Make sure to re-enable foreign key checks even on error
        $conn->query("SET FOREIGN_KEY_CHECKS = 1");
        
        error_log("Error resetting fee data: " . $e->getMessage());
        $message = "ডাটা রিসেট করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি ডাটা রিসেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .danger-zone {
            border: 2px dashed #dc3545;
            background-color: #f8d7da;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .danger-icon {
            font-size: 48px;
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-danger text-white">
                        <h3 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>ফি ডাটা রিসেট</h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($message)): ?>
                            <div class="alert alert-<?php echo $success ? 'success' : 'danger'; ?>">
                                <?php echo $message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="danger-zone text-center">
                            <div class="mb-3">
                                <i class="fas fa-skull-crossbones danger-icon"></i>
                            </div>
                            <h4 class="text-danger">সতর্কতা: বিপদজনক অঞ্চল</h4>
                            <p class="mb-0">এই অপারেশন সমস্ত ফি ডাটা মুছে ফেলবে। এটি একটি স্থায়ী পদক্ষেপ যা পূর্বাবস্থায় ফেরানো যাবে না।</p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-circle me-2"></i>এই পদক্ষেপটি নিম্নলিখিত তথ্য মুছে ফেলবে:</h5>
                            <ul>
                                <li>সমস্ত ফি রেকর্ড</li>
                                <li>সমস্ত পেমেন্ট রেকর্ড</li>
                                <li>সমস্ত রসিদ রেকর্ড</li>
                            </ul>
                            <p><strong>এটি শিক্ষার্থী তথ্য বা অন্যান্য সিস্টেম সেটিংস প্রভাবিত করবে না।</strong></p>
                        </div>
                        
                        <form method="POST" action="">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="confirm_checkbox" required>
                                <label class="form-check-label" for="confirm_checkbox">
                                    আমি নিশ্চিত করছি যে আমি সমস্ত ফি ডাটা মুছে ফেলতে চাই এবং বুঝতে পারি যে এই পদক্ষেপটি পূর্বাবস্থায় ফেরানো যাবে না।
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="fees.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                                </a>
                                <button type="submit" name="confirm_reset" class="btn btn-danger" id="reset_btn" disabled>
                                    <i class="fas fa-trash-alt me-1"></i> সমস্ত ফি ডাটা মুছে ফেলুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        $(document).ready(function() {
            // Enable reset button only when checkbox is checked
            $('#confirm_checkbox').change(function() {
                $('#reset_btn').prop('disabled', !$(this).is(':checked'));
            });
        });
    </script>
</body>
</html> 