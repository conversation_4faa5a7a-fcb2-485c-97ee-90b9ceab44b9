<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Ensure necessary tables exist
$feesTableQuery = "CREATE TABLE IF NOT EXISTS fees (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    fee_type VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    paid DECIMAL(10,2) DEFAULT 0,
    due_date DATE NOT NULL,
    payment_status ENUM('due', 'partial', 'paid', 'overpaid') DEFAULT 'due',
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
)";
$conn->query($feesTableQuery);

// Process reset request
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Determine reset type
    $resetType = $_POST['reset_type'] ?? '';
    
    if ($resetType === 'all') {
        // Reset all unpaid fees
        $deleteQuery = "DELETE FROM fees WHERE payment_status IN ('due', 'partial')";
        if ($conn->query($deleteQuery)) {
            $affected_rows = $conn->affected_rows;
            $success_message = "সফলভাবে $affected_rows টি অপরিশোধিত ফি রিসেট করা হয়েছে।";
        } else {
            $error_message = "ফি রিসেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    } elseif ($resetType === 'class') {
        // Reset by class and session
        $sessionId = $_POST['session_id'] ?? 0;
        $classId = $_POST['class_id'] ?? 0;
        
        if (empty($sessionId) || empty($classId)) {
            $error_message = "দয়া করে ক্লাস এবং সেশন নির্বাচন করুন।";
        } else {
            // Get students in this class and session
            $studentsQuery = "SELECT id FROM students WHERE class_id = ? AND session_id = ?";
            $stmt = $conn->prepare($studentsQuery);
            $stmt->bind_param("ii", $classId, $sessionId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $studentIds = [];
                while ($row = $result->fetch_assoc()) {
                    $studentIds[] = $row['id'];
                }
                
                // Delete unpaid fees for these students
                if (!empty($studentIds)) {
                    $studentIdsStr = implode(',', $studentIds);
                    $deleteQuery = "DELETE FROM fees WHERE student_id IN ($studentIdsStr) AND payment_status IN ('due', 'partial')";
                    
                    if ($conn->query($deleteQuery)) {
                        $affected_rows = $conn->affected_rows;
                        $success_message = "সফলভাবে $affected_rows টি অপরিশোধিত ফি রিসেট করা হয়েছে।";
                    } else {
                        $error_message = "ফি রিসেট করতে সমস্যা হয়েছে: " . $conn->error;
                    }
                } else {
                    $error_message = "কোনো ফি রিসেট করা হয়নি।";
                }
            } else {
                $error_message = "এই ক্লাসে এবং সেশনে কোন শিক্ষার্থী পাওয়া যায়নি।";
            }
        }
    } elseif ($resetType === 'student') {
        // Reset by student ID
        $studentId = $_POST['student_id'] ?? 0;
        
        if (empty($studentId)) {
            $error_message = "দয়া করে একজন শিক্ষার্থী নির্বাচন করুন।";
        } else {
            // Delete unpaid fees for this student
            $deleteQuery = "DELETE FROM fees WHERE student_id = ? AND payment_status IN ('due', 'partial')";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $studentId);
            
            if ($stmt->execute()) {
                $affected_rows = $stmt->affected_rows;
                $success_message = "সফলভাবে $affected_rows টি অপরিশোধিত ফি রিসেট করা হয়েছে।";
            } else {
                $error_message = "ফি রিসেট করতে সমস্যা হয়েছে: " . $stmt->error;
            }
        }
    } elseif ($resetType === 'fee_type') {
        // Reset by fee type
        $feeType = $_POST['fee_type'] ?? '';
        
        if (empty($feeType)) {
            $error_message = "দয়া করে একটি ফি টাইপ নির্বাচন করুন।";
        } else {
            // Delete unpaid fees of this type
            $deleteQuery = "DELETE FROM fees WHERE fee_type = ? AND payment_status IN ('due', 'partial')";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("s", $feeType);
            
            if ($stmt->execute()) {
                $affected_rows = $stmt->affected_rows;
                $success_message = "সফলভাবে $affected_rows টি '$feeType' টাইপের অপরিশোধিত ফি রিসেট করা হয়েছে।";
            } else {
                $error_message = "ফি রিসেট করতে সমস্যা হয়েছে: " . $stmt->error;
            }
        }
    } else {
        $error_message = "দয়া করে একটি রিসেট পদ্ধতি নির্বাচন করুন।";
    }
}

// Get sessions
$sessionsQuery = "SELECT * FROM sessions ORDER BY start_date DESC";
$sessions = $conn->query($sessionsQuery);

// Get fee types 
$feeTypesQuery = "SELECT * FROM fee_types ORDER BY name";
$feeTypes = $conn->query($feeTypesQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি এসাইন রিসেট - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        .sidebar .nav-link {
            color: #f8f9fa;
            font-weight: 500;
        }
        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .reset-option {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }
        .reset-option-title {
            font-weight: 600;
            color: #dc3545;
            margin-bottom: 10px;
        }
        .warning-text {
            color: #dc3545;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include('includes/sidebar.php'); ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-undo-alt me-2"></i>ফি এসাইন রিসেট</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="fee_assign.php" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-plus-circle"></i> ফি এসাইন
                        </a>
                        <a href="fees.php" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-list"></i> ফি তালিকা
                        </a>
                    </div>
                </div>
                
                <!-- Success or Error Message -->
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong><i class="fas fa-check-circle me-2"></i>সফল!</strong> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <strong><i class="fas fa-exclamation-circle me-2"></i>ত্রুটি!</strong> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0"><i class="fas fa-exclamation-triangle me-2"></i>সতর্কতা</h5>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">ফি রিসেট একটি স্থায়ী পদক্ষেপ। একবার রিসেট করলে, শিক্ষার্থীদের বকেয়া ফি তথ্য স্থায়ীভাবে মুছে যাবে। শুধুমাত্র অপরিশোধিত (বকেয়া বা আংশিক পরিশোধিত) ফি রিসেট করা হবে। ইতিমধ্যে পরিশোধ করা ফি অক্ষত থাকবে।</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="reset-option">
                            <h5 class="reset-option-title"><i class="fas fa-user me-2"></i>শিক্ষার্থী অনুযায়ী রিসেট</h5>
                            <form method="POST" action="">
                                <input type="hidden" name="reset_type" value="student">
                                <div class="mb-3">
                                    <label for="session_id_student" class="form-label">সেশন</label>
                                    <select class="form-select" id="session_id_student" name="session_id">
                                        <option value="">সেশন নির্বাচন করুন</option>
                                        <?php if ($sessions && $sessions->num_rows > 0): ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>"><?php echo $session['session_name']; ?></option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="class_id_student" class="form-label">ক্লাস</label>
                                    <select class="form-select" id="class_id_student" name="class_id" disabled>
                                        <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                    <select class="form-select" id="student_id" name="student_id" required disabled>
                                        <option value="">প্রথমে ক্লাস নির্বাচন করুন</option>
                                    </select>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই শিক্ষার্থীর অপরিশোধিত ফি রিসেট করতে চান?')">
                                        <i class="fas fa-trash me-2"></i>শিক্ষার্থীর বকেয়া ফি রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="reset-option">
                            <h5 class="reset-option-title"><i class="fas fa-users me-2"></i>ক্লাস অনুযায়ী রিসেট</h5>
                            <form method="POST" action="">
                                <input type="hidden" name="reset_type" value="class">
                                <div class="mb-3">
                                    <label for="session_id_class" class="form-label">সেশন</label>
                                    <select class="form-select" id="session_id_class" name="session_id" required>
                                        <option value="">সেশন নির্বাচন করুন</option>
                                        <?php 
                                        // Reset sessions result pointer
                                        $sessions->data_seek(0);
                                        if ($sessions && $sessions->num_rows > 0): 
                                        ?>
                                            <?php while ($session = $sessions->fetch_assoc()): ?>
                                                <option value="<?php echo $session['id']; ?>"><?php echo $session['session_name']; ?></option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="class_id_class" class="form-label">ক্লাস</label>
                                    <select class="form-select" id="class_id_class" name="class_id" required disabled>
                                        <option value="">প্রথমে সেশন নির্বাচন করুন</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>এই ক্লাসের সকল শিক্ষার্থীর অপরিশোধিত ফি রিসেট করা হবে।
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ক্লাসের সকল শিক্ষার্থীর অপরিশোধিত ফি রিসেট করতে চান?')">
                                        <i class="fas fa-trash me-2"></i>ক্লাসের সকল বকেয়া ফি রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="reset-option">
                            <h5 class="reset-option-title"><i class="fas fa-tag me-2"></i>ফি টাইপ অনুযায়ী রিসেট</h5>
                            <form method="POST" action="">
                                <input type="hidden" name="reset_type" value="fee_type">
                                <div class="mb-3">
                                    <label for="fee_type" class="form-label">ফি টাইপ</label>
                                    <select class="form-select" id="fee_type" name="fee_type" required>
                                        <option value="">ফি টাইপ নির্বাচন করুন</option>
                                        <?php if ($feeTypes && $feeTypes->num_rows > 0): ?>
                                            <?php while ($type = $feeTypes->fetch_assoc()): ?>
                                                <option value="<?php echo $type['name']; ?>"><?php echo $type['name']; ?></option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>এই ফি টাইপের সকল অপরিশোধিত ফি রিসেট করা হবে।
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই ফি টাইপের সকল অপরিশোধিত ফি রিসেট করতে চান?')">
                                        <i class="fas fa-trash me-2"></i>ফি টাইপের সকল বকেয়া রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="reset-option">
                            <h5 class="reset-option-title"><i class="fas fa-times-circle me-2"></i>সকল অপরিশোধিত ফি রিসেট</h5>
                            <form method="POST" action="">
                                <input type="hidden" name="reset_type" value="all">
                                <div class="mb-4">
                                    <div class="alert alert-danger">
                                        <i class="fas fa-skull-crossbones me-2"></i><strong>সতর্কতা:</strong> এটি সিস্টেমের সকল শিক্ষার্থীর সকল অপরিশোধিত ফি স্থায়ীভাবে মুছে ফেলবে। এই পদক্ষেপ ফিরিয়ে নেওয়া যাবে না!
                                    </div>
                                </div>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি সিস্টেমের সকল অপরিশোধিত ফি রিসেট করতে চান? এটি একটি স্থায়ী পদক্ষেপ!')">
                                        <i class="fas fa-exclamation-triangle me-2"></i>সকল অপরিশোধিত ফি রিসেট করুন
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between mt-4 mb-5">
                    <a href="fees.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                    </a>
                    <a href="fee_assign.php" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-1"></i> ফি এসাইন করুন
                    </a>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Handle session selection for student view
            $('#session_id_student').change(function() {
                const sessionId = $(this).val();
                
                // Enable/disable class dropdown
                if(sessionId) {
                    $('#class_id_student').html('<option value="">লোড হচ্ছে...</option>');
                    $('#class_id_student').prop('disabled', true);
                    
                    // AJAX call to get classes for this session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_classes_by_session',
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let options = '<option value="">ক্লাস নির্বাচন করুন</option>';
                                response.classes.forEach(function(cls) {
                                    options += `<option value="${cls.id}">${cls.class_name}</option>`;
                                });
                                $('#class_id_student').html(options);
                                $('#class_id_student').prop('disabled', false);
                            } else {
                                $('#class_id_student').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                            }
                        },
                        error: function() {
                            $('#class_id_student').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                        }
                    });
                } else {
                    // Reset class dropdown
                    $('#class_id_student').html('<option value="">প্রথমে সেশন নির্বাচন করুন</option>');
                    $('#class_id_student').prop('disabled', true);
                    
                    // Reset student selection
                    $('#student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#student_id').prop('disabled', true);
                }
            });
            
            // Handle class selection for student view
            $('#class_id_student').change(function() {
                const classId = $(this).val();
                const sessionId = $('#session_id_student').val();
                
                if(classId && sessionId) {
                    $('#student_id').html('<option value="">লোড হচ্ছে...</option>');
                    $('#student_id').prop('disabled', true);
                    
                    // AJAX call to get students for this class
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_students_by_class_session',
                            class_id: classId,
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let options = '<option value="">শিক্ষার্থী নির্বাচন করুন</option>';
                                response.students.forEach(function(student) {
                                    options += `<option value="${student.id}">${student.student_id} - ${student.first_name} ${student.last_name}</option>`;
                                });
                                $('#student_id').html(options);
                                $('#student_id').prop('disabled', false);
                            } else {
                                $('#student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                            }
                        },
                        error: function() {
                            $('#student_id').html('<option value="">শিক্ষার্থী লোড করতে সমস্যা হয়েছে</option>');
                        }
                    });
                } else {
                    $('#student_id').html('<option value="">প্রথমে ক্লাস নির্বাচন করুন</option>');
                    $('#student_id').prop('disabled', true);
                }
            });
            
            // Handle session selection for class view
            $('#session_id_class').change(function() {
                const sessionId = $(this).val();
                
                // Enable/disable class dropdown
                if(sessionId) {
                    $('#class_id_class').html('<option value="">লোড হচ্ছে...</option>');
                    $('#class_id_class').prop('disabled', true);
                    
                    // AJAX call to get classes for this session
                    $.ajax({
                        url: 'ajax_handler.php',
                        type: 'POST',
                        data: {
                            action: 'get_classes_by_session',
                            session_id: sessionId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if(response.status === 'success') {
                                let options = '<option value="">ক্লাস নির্বাচন করুন</option>';
                                response.classes.forEach(function(cls) {
                                    options += `<option value="${cls.id}">${cls.class_name}</option>`;
                                });
                                $('#class_id_class').html(options);
                                $('#class_id_class').prop('disabled', false);
                            } else {
                                $('#class_id_class').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                            }
                        },
                        error: function() {
                            $('#class_id_class').html('<option value="">ক্লাস লোড করতে সমস্যা হয়েছে</option>');
                        }
                    });
                } else {
                    // Reset class dropdown
                    $('#class_id_class').html('<option value="">প্রথমে সেশন নির্বাচন করুন</option>');
                    $('#class_id_class').prop('disabled', true);
                }
            });
        });
    </script>
</body>
</html> 