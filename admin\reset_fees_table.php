<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

echo "<h2>Fixing Fees Table Structure</h2>";

// Check if 'paid' column exists and its type
$checkQuery = "SHOW COLUMNS FROM fees LIKE 'paid'";
$checkResult = $conn->query($checkQuery);

if ($checkResult->num_rows > 0) {
    $columnInfo = $checkResult->fetch_assoc();
    
    // If the column type is not DECIMAL, alter it
    if (strpos($columnInfo['Type'], 'decimal') === false) {
        echo "<p>Incorrect 'paid' column found. Altering column...</p>";
        
        $alterQuery = "ALTER TABLE fees MODIFY COLUMN paid DECIMAL(10,2) DEFAULT 0";
        if ($conn->query($alterQuery)) {
            echo "<p>Successfully altered 'paid' column to DECIMAL(10,2).</p>";
        } else {
            echo "<p>Error altering column: " . $conn->error . "</p>";
        }
    } else {
        echo "<p>'paid' column already exists with correct type: " . $columnInfo['Type'] . "</p>";
    }
} else {
    echo "<p>'paid' column not found. Adding column...</p>";
    
    $addColumnQuery = "ALTER TABLE fees ADD COLUMN paid DECIMAL(10,2) DEFAULT 0";
    if ($conn->query($addColumnQuery)) {
        echo "<p>Successfully added 'paid' column as DECIMAL(10,2).</p>";
    } else {
        echo "<p>Error adding column: " . $conn->error . "</p>";
    }
}

// Update payment_status based on amount and paid values
echo "<p>Updating payment status for all fees...</p>";
$updateStatusQuery = "UPDATE fees SET 
                     payment_status = CASE 
                        WHEN paid >= amount THEN 'paid' 
                        WHEN paid > 0 THEN 'partial' 
                        ELSE 'due' 
                     END";

if ($conn->query($updateStatusQuery)) {
    echo "<p>Successfully updated payment status for all fees.</p>";
} else {
    echo "<p>Error updating payment status: " . $conn->error . "</p>";
}

echo "<p>Fees table structure fix completed.</p>";
echo "<p><a href='fees.php'>Return to Fees Management</a></p>";
?> 