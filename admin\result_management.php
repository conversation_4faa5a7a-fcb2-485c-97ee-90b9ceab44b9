<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create results table if not exists
$sql = "CREATE TABLE IF NOT EXISTS results (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    exam_id INT(11) NOT NULL,
    marks_obtained FLOAT NOT NULL,
    grade VARCHAR(5) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE
)";
$conn->query($sql);

// Handle form submissions
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add new result
    if (isset($_POST['add_result'])) {
        $student_id = $_POST['student_id'];
        $exam_id = $_POST['exam_id'];
        $marks_obtained = $_POST['marks_obtained'];
        
        // Get total marks for the exam
        $examQuery = "SELECT total_marks FROM exams WHERE id = ?";
        $stmt = $conn->prepare($examQuery);
        $stmt->bind_param("i", $exam_id);
        $stmt->execute();
        $examResult = $stmt->get_result();
        $exam = $examResult->fetch_assoc();
        $total_marks = $exam['total_marks'];
        
        // Calculate grade based on percentage
        $percentage = ($marks_obtained / $total_marks) * 100;
        $grade = '';
        
        if ($percentage >= 80) {
            $grade = 'A+';
        } elseif ($percentage >= 70) {
            $grade = 'A';
        } elseif ($percentage >= 60) {
            $grade = 'A-';
        } elseif ($percentage >= 50) {
            $grade = 'B';
        } elseif ($percentage >= 40) {
            $grade = 'C';
        } elseif ($percentage >= 33) {
            $grade = 'D';
        } else {
            $grade = 'F';
        }
        
        // Check if result already exists
        $checkQuery = "SELECT id FROM results WHERE student_id = ? AND exam_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ii", $student_id, $exam_id);
        $stmt->execute();
        $checkResult = $stmt->get_result();
        
        if ($checkResult->num_rows > 0) {
            $errorMessage = "এই শিক্ষার্থীর জন্য এই পরীক্ষার ফলাফল ইতিমধ্যে যোগ করা হয়েছে!";
        } else {
            // Insert new result
            $insertQuery = "INSERT INTO results (student_id, exam_id, marks_obtained, grade) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("iisd", $student_id, $exam_id, $marks_obtained, $grade);
            
            if ($stmt->execute()) {
                $successMessage = "ফলাফল সফলভাবে যোগ করা হয়েছে!";
            } else {
                $errorMessage = "ফলাফল যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    }
    
    // Update result
    if (isset($_POST['update_result'])) {
        $result_id = $_POST['result_id'];
        $marks_obtained = $_POST['marks_obtained'];
        
        // Get exam ID and student ID from result
        $resultQuery = "SELECT exam_id, student_id FROM results WHERE id = ?";
        $stmt = $conn->prepare($resultQuery);
        $stmt->bind_param("i", $result_id);
        $stmt->execute();
        $resultData = $stmt->get_result()->fetch_assoc();
        $exam_id = $resultData['exam_id'];
        
        // Get total marks for the exam
        $examQuery = "SELECT total_marks FROM exams WHERE id = ?";
        $stmt = $conn->prepare($examQuery);
        $stmt->bind_param("i", $exam_id);
        $stmt->execute();
        $examResult = $stmt->get_result();
        $exam = $examResult->fetch_assoc();
        $total_marks = $exam['total_marks'];
        
        // Calculate grade based on percentage
        $percentage = ($marks_obtained / $total_marks) * 100;
        $grade = '';
        
        if ($percentage >= 80) {
            $grade = 'A+';
        } elseif ($percentage >= 70) {
            $grade = 'A';
        } elseif ($percentage >= 60) {
            $grade = 'A-';
        } elseif ($percentage >= 50) {
            $grade = 'B';
        } elseif ($percentage >= 40) {
            $grade = 'C';
        } elseif ($percentage >= 33) {
            $grade = 'D';
        } else {
            $grade = 'F';
        }
        
        // Update result
        $updateQuery = "UPDATE results SET marks_obtained = ?, grade = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("dsi", $marks_obtained, $grade, $result_id);
        
        if ($stmt->execute()) {
            $successMessage = "ফলাফল সফলভাবে আপডেট করা হয়েছে!";
        } else {
            $errorMessage = "ফলাফল আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
    
    // Delete result
    if (isset($_POST['delete_result'])) {
        $result_id = $_POST['result_id'];
        
        // Delete result
        $deleteQuery = "DELETE FROM results WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $result_id);
        
        if ($stmt->execute()) {
            $successMessage = "ফলাফল সফলভাবে মুছে ফেলা হয়েছে!";
        } else {
            $errorMessage = "ফলাফল মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get all students for dropdown
$studentsQuery = "SELECT s.id, s.student_id as student_code, s.first_name, s.last_name, c.class_name 
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                ORDER BY s.first_name, s.last_name";
$students = $conn->query($studentsQuery);

// Get all exams for dropdown
$examsQuery = "SELECT e.id, e.exam_name, e.total_marks, c.class_name 
              FROM exams e
              LEFT JOIN classes c ON e.class_id = c.id
              ORDER BY e.exam_name";
$exams = $conn->query($examsQuery);

// Get all results with student and exam details
$resultsQuery = "SELECT r.id, r.marks_obtained, r.grade, e.exam_name, e.total_marks, 
                s.first_name, s.last_name, s.student_id as student_code, c.class_name
                FROM results r
                JOIN students s ON r.student_id = s.id
                JOIN exams e ON r.exam_id = e.id
                LEFT JOIN classes c ON s.class_id = c.id
                ORDER BY r.id DESC";
$results = $conn->query($resultsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফলাফল ব্যবস্থাপনা - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>ফলাফল ব্যবস্থাপনা</h2>
                        <p class="text-muted">শিক্ষার্থীদের পরীক্ষার ফলাফল যোগ করুন, আপডেট করুন এবং দেখুন</p>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Add Result Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">নতুন ফলাফল যোগ করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                    <select name="student_id" id="student_id" class="form-select" required>
                                        <option value="">শিক্ষার্থী নির্বাচন করুন</option>
                                        <?php if ($students && $students->num_rows > 0): ?>
                                            <?php while ($student = $students->fetch_assoc()): ?>
                                                <option value="<?php echo $student['id']; ?>">
                                                    <?php echo $student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_code'] . ')' . (($student['class_name']) ? ' - ' . $student['class_name'] : ''); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="exam_id" class="form-label">পরীক্ষা</label>
                                    <select name="exam_id" id="exam_id" class="form-select" required>
                                        <option value="">পরীক্ষা নির্বাচন করুন</option>
                                        <?php 
                                        if ($exams) {
                                            $exams->data_seek(0);
                                            while ($exam = $exams->fetch_assoc()): 
                                        ?>
                                                <option value="<?php echo $exam['id']; ?>">
                                                    <?php echo $exam['exam_name'] . ' (' . $exam['total_marks'] . ' মার্কস)' . (($exam['class_name']) ? ' - ' . $exam['class_name'] : ''); ?>
                                                </option>
                                        <?php 
                                            endwhile;
                                        } 
                                        ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="marks_obtained" class="form-label">প্রাপ্ত মার্কস</label>
                                    <input type="number" class="form-control" id="marks_obtained" name="marks_obtained" min="0" step="0.01" required>
                                </div>
                            </div>
                            <button type="submit" name="add_result" class="btn btn-success">
                                <i class="fas fa-plus-circle me-2"></i>ফলাফল যোগ করুন
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Results Table -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ফলাফল তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>শিক্ষার্থী</th>
                                        <th>আইডি</th>
                                        <th>ক্লাস</th>
                                        <th>পরীক্ষা</th>
                                        <th>মোট মার্কস</th>
                                        <th>প্রাপ্ত মার্কস</th>
                                        <th>গ্রেড</th>
                                        <th>একশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($results && $results->num_rows > 0): ?>
                                        <?php while ($result = $results->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $result['first_name'] . ' ' . $result['last_name']; ?></td>
                                                <td><?php echo $result['student_code']; ?></td>
                                                <td><?php echo $result['class_name']; ?></td>
                                                <td><?php echo $result['exam_name']; ?></td>
                                                <td><?php echo $result['total_marks']; ?></td>
                                                <td><?php echo $result['marks_obtained']; ?></td>
                                                <td>
                                                    <span class="badge <?php echo ($result['grade'] == 'F') ? 'bg-danger' : 'bg-success'; ?>">
                                                        <?php echo $result['grade']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-warning edit-result" 
                                                        data-id="<?php echo $result['id']; ?>"
                                                        data-marks="<?php echo $result['marks_obtained']; ?>"
                                                        data-bs-toggle="modal" data-bs-target="#editResultModal">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger delete-result"
                                                        data-id="<?php echo $result['id']; ?>"
                                                        data-name="<?php echo $result['first_name'] . ' ' . $result['last_name']; ?>"
                                                        data-exam="<?php echo $result['exam_name']; ?>"
                                                        data-bs-toggle="modal" data-bs-target="#deleteResultModal">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center">কোন ফলাফল পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Result Modal -->
    <div class="modal fade" id="editResultModal" tabindex="-1" aria-labelledby="editResultModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editResultModalLabel">ফলাফল সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="result_id" id="edit_result_id">
                        <div class="mb-3">
                            <label for="edit_marks_obtained" class="form-label">প্রাপ্ত মার্কস</label>
                            <input type="number" class="form-control" id="edit_marks_obtained" name="marks_obtained" min="0" step="0.01" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" name="update_result" class="btn btn-warning">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Result Modal -->
    <div class="modal fade" id="deleteResultModal" tabindex="-1" aria-labelledby="deleteResultModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteResultModalLabel">ফলাফল মুছে ফেলুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>আপনি কি নিশ্চিত যে আপনি <span id="delete_student_name"></span> এর <span id="delete_exam_name"></span> পরীক্ষার ফলাফল মুছতে চান?</p>
                    <p class="text-danger">এই কাজটি ফিরিয়ে নেওয়া যাবে না!</p>
                </div>
                <form method="POST" action="">
                    <input type="hidden" name="result_id" id="delete_result_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" name="delete_result" class="btn btn-danger">মুছে ফেলুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Edit result modal
            const editButtons = document.querySelectorAll('.edit-result');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const marks = this.getAttribute('data-marks');
                    
                    document.getElementById('edit_result_id').value = id;
                    document.getElementById('edit_marks_obtained').value = marks;
                });
            });
            
            // Delete result modal
            const deleteButtons = document.querySelectorAll('.delete-result');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const exam = this.getAttribute('data-exam');
                    
                    document.getElementById('delete_result_id').value = id;
                    document.getElementById('delete_student_name').textContent = name;
                    document.getElementById('delete_exam_name').textContent = exam;
                });
            });
        });
    </script>
</body>
</html> 