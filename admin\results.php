<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get filter values from GET request
$class_id = isset($_GET['class_id']) ? $_GET['class_id'] : '';
$exam_id = isset($_GET['exam_id']) ? $_GET['exam_id'] : '';
$student_id = isset($_GET['student_id']) ? $_GET['student_id'] : '';

// Build query for results with filters
$resultsQuery = "SELECT r.id, r.marks_obtained, r.grade, e.exam_name, e.total_marks, 
                s.first_name, s.last_name, s.student_id as student_code, c.class_name
                FROM results r
                JOIN students s ON r.student_id = s.id
                JOIN exams e ON r.exam_id = e.id
                LEFT JOIN classes c ON s.class_id = c.id
                WHERE 1=1";

$queryParams = [];

if (!empty($class_id)) {
    $resultsQuery .= " AND s.class_id = ?";
    $queryParams[] = $class_id;
}

if (!empty($exam_id)) {
    $resultsQuery .= " AND r.exam_id = ?";
    $queryParams[] = $exam_id;
}

if (!empty($student_id)) {
    $resultsQuery .= " AND r.student_id = ?";
    $queryParams[] = $student_id;
}

$resultsQuery .= " ORDER BY r.id DESC";

// Prepare and execute the query
$stmt = $conn->prepare($resultsQuery);

if (!empty($queryParams)) {
    $types = str_repeat("i", count($queryParams));
    $stmt->bind_param($types, ...$queryParams);
}

$stmt->execute();
$results = $stmt->get_result();

// Get all classes for filter dropdown
$classesQuery = "SELECT * FROM classes ORDER BY class_name";
$classes = $conn->query($classesQuery);

// Get all exams for filter dropdown
$examsQuery = "SELECT * FROM exams ORDER BY exam_name";
$exams = $conn->query($examsQuery);

// Get all students for filter dropdown
$studentsQuery = "SELECT id, student_id as student_code, first_name, last_name FROM students ORDER BY first_name, last_name";
$students = $conn->query($studentsQuery);

// Check if created_at column exists in results table and add it if needed
$check_created_column = "SHOW COLUMNS FROM results LIKE 'created_at'";
$created_column_result = $conn->query($check_created_column);
if ($created_column_result->num_rows == 0) {
    // Column doesn't exist, add it
    $add_created_column = "ALTER TABLE results ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
    $conn->query($add_created_column);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী ফলাফল - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-8">
                        <h2>শিক্ষার্থী ফলাফল</h2>
                        <p class="text-muted">সকল শিক্ষার্থীদের পরীক্ষার ফলাফল দেখুন</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="result_management.php" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>ফলাফল যোগ করুন
                        </a>
                    </div>
                </div>

                <!-- Filter Form -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">ফলাফল ফিল্টার করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="class_id" class="form-label">ক্লাস</label>
                                    <select name="class_id" id="class_id" class="form-select">
                                        <option value="">সকল ক্লাস</option>
                                        <?php if ($classes && $classes->num_rows > 0): ?>
                                            <?php while ($class = $classes->fetch_assoc()): ?>
                                                <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $class['class_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="exam_id" class="form-label">পরীক্ষা</label>
                                    <select name="exam_id" id="exam_id" class="form-select">
                                        <option value="">সকল পরীক্ষা</option>
                                        <?php if ($exams && $exams->num_rows > 0): ?>
                                            <?php while ($exam = $exams->fetch_assoc()): ?>
                                                <option value="<?php echo $exam['id']; ?>" <?php echo ($exam_id == $exam['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $exam['exam_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="student_id" class="form-label">শিক্ষার্থী</label>
                                    <select name="student_id" id="student_id" class="form-select">
                                        <option value="">সকল শিক্ষার্থী</option>
                                        <?php if ($students && $students->num_rows > 0): ?>
                                            <?php while ($student = $students->fetch_assoc()): ?>
                                                <option value="<?php echo $student['id']; ?>" <?php echo ($student_id == $student['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $student['first_name'] . ' ' . $student['last_name'] . ' (' . $student['student_code'] . ')'; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="d-flex">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-filter me-2"></i>ফিল্টার করুন
                                </button>
                                <a href="results.php" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt me-2"></i>রিসেট
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Results Table -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">ফলাফল তালিকা</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>শিক্ষার্থী</th>
                                        <th>আইডি</th>
                                        <th>ক্লাস</th>
                                        <th>পরীক্ষা</th>
                                        <th>মোট মার্কস</th>
                                        <th>প্রাপ্ত মার্কস</th>
                                        <th>শতকরা (%)</th>
                                        <th>গ্রেড</th>
                                        <th>একশন</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($results && $results->num_rows > 0): ?>
                                        <?php while ($result = $results->fetch_assoc()): ?>
                                            <tr>
                                                <td><?php echo $result['first_name'] . ' ' . $result['last_name']; ?></td>
                                                <td><?php echo $result['student_code']; ?></td>
                                                <td><?php echo $result['class_name']; ?></td>
                                                <td><?php echo $result['exam_name']; ?></td>
                                                <td><?php echo $result['total_marks']; ?></td>
                                                <td><?php echo $result['marks_obtained']; ?></td>
                                                <td>
                                                    <?php
                                                    $percentage = ($result['marks_obtained'] / $result['total_marks']) * 100;
                                                    echo number_format($percentage, 2) . '%';
                                                    ?>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo ($result['grade'] == 'F') ? 'bg-danger' : 'bg-success'; ?>">
                                                        <?php echo $result['grade']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="view_student.php?id=<?php echo $result['id']; ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="result_management.php?edit=<?php echo $result['id']; ?>" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center">কোন ফলাফল পাওয়া যায়নি</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 