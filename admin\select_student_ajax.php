<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Unauthorized access']);
    exit();
}

require_once '../includes/dbh.inc.php';

// Get filter values
$session_id = isset($_GET['session_id']) ? intval($_GET['session_id']) : 0;
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$department_id = isset($_GET['department_id']) ? intval($_GET['department_id']) : 0;
$search_term = isset($_GET['search_term']) ? $_GET['search_term'] : '';

// Build the query
$query = "SELECT s.id, s.student_id, s.first_name, s.last_name, 
          c.class_name, d.department_name, ss.session_name 
          FROM students s
          LEFT JOIN classes c ON s.class_id = c.id
          LEFT JOIN departments d ON s.department_id = d.id
          LEFT JOIN sessions ss ON s.session_id = ss.id
          WHERE 1=1";

$params = [];
$types = "";

// Add filters
if ($session_id > 0) {
    $query .= " AND s.session_id = ?";
    $params[] = $session_id;
    $types .= "i";
}

if ($class_id > 0) {
    $query .= " AND s.class_id = ?";
    $params[] = $class_id;
    $types .= "i";
}

if ($department_id > 0) {
    $query .= " AND s.department_id = ?";
    $params[] = $department_id;
    $types .= "i";
}

if (!empty($search_term)) {
    $query .= " AND (s.first_name LIKE ? OR s.last_name LIKE ? OR s.student_id LIKE ? OR s.phone LIKE ?)";
    $search_param = "%" . $search_term . "%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $types .= "ssss";
}

// Order by
$query .= " ORDER BY s.first_name, s.last_name";

// Prepare and execute the query
$stmt = $conn->prepare($query);

if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}

$stmt->execute();
$result = $stmt->get_result();

// Format the response
$students = [];
while ($row = $result->fetch_assoc()) {
    $students[] = [
        'id' => $row['id'],
        'student_id' => $row['student_id'],
        'name' => $row['first_name'] . ' ' . $row['last_name'],
        'class_name' => $row['class_name'] ?? 'অজানা',
        'department_name' => $row['department_name'] ?? 'অজানা',
        'session_name' => $row['session_name'] ?? 'অজানা'
    ];
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'count' => count($students),
    'students' => $students
]); 