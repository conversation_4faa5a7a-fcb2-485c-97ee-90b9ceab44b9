<?php
session_start();

// Check if user is logged in
// Modify the session check to match the actual session variable used in your system
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';
// require_once 'includes/functions.php';  // Commented out until we verify this file exists

// Define minimal functions if needed
if (!function_exists('sanitize_input')) {
    function sanitize_input($data) {
        global $conn;
        return $conn->real_escape_string(trim($data));
    }
}

// Create uploads directory if it doesn't exist
$uploadDir = '../uploads/logos/';
if (!file_exists($uploadDir)) {
    mkdir($uploadDir, 0777, true);
}

$currentPage = basename($_SERVER['PHP_SELF']);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_settings'])) {
        // Example settings update functionality
        $school_name = $conn->real_escape_string($_POST['school_name']);
        $school_address = $conn->real_escape_string($_POST['school_address']);
        $school_phone = $conn->real_escape_string($_POST['school_phone']);
        $school_email = $conn->real_escape_string($_POST['school_email']);
        
        // Handle logo upload
        $logo_path = isset($settings['logo_path']) ? $settings['logo_path'] : '';
        
        if (!empty($_FILES['school_logo']['name'])) {
            $file_name = time() . '_' . $_FILES['school_logo']['name'];
            $file_tmp = $_FILES['school_logo']['tmp_name'];
            $file_type = $_FILES['school_logo']['type'];
            $file_ext = strtolower(pathinfo($_FILES['school_logo']['name'], PATHINFO_EXTENSION));
            
            $allowed_extensions = array("jpg", "jpeg", "png", "gif");
            
            if (in_array($file_ext, $allowed_extensions)) {
                $upload_path = $uploadDir . $file_name;
                
                if (move_uploaded_file($file_tmp, $upload_path)) {
                    // Delete old logo if exists
                    if (!empty($logo_path) && file_exists('../' . $logo_path)) {
                        unlink('../' . $logo_path);
                    }
                    
                    $logo_path = 'uploads/logos/' . $file_name;
                } else {
                    $error_message = "Error uploading logo";
                }
            } else {
                $error_message = "Invalid file format. Only JPG, JPEG, PNG and GIF are allowed.";
            }
        }
        
        // Check if record exists
        $checkQuery = "SELECT COUNT(*) as count FROM school_settings";
        $checkResult = $conn->query($checkQuery);
        $row = $checkResult->fetch_assoc();
        
        // Check if logo_path column exists
        $checkLogoColumn = "SHOW COLUMNS FROM school_settings LIKE 'logo_path'";
        $logoColumnResult = $conn->query($checkLogoColumn);
        if ($logoColumnResult->num_rows == 0) {
            // Add logo_path column if it doesn't exist
            $addColumnQuery = "ALTER TABLE school_settings ADD COLUMN logo_path VARCHAR(255) DEFAULT NULL";
            $conn->query($addColumnQuery);
        }
        
        if ($row['count'] == 0) {
            // Insert new record
            $query = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email, logo_path) 
                      VALUES ('$school_name', '$school_address', '$school_phone', '$school_email', '$logo_path')";
        } else {
            // Update existing record
            $query = "UPDATE school_settings SET 
                      school_name = '$school_name',
                      school_address = '$school_address',
                      school_phone = '$school_phone',
                      school_email = '$school_email',
                      logo_path = '$logo_path'
                      WHERE id = 1";
        }
                  
        if ($conn->query($query)) {
            $success_message = "Settings updated successfully!";
        } else {
            $error_message = "Error updating settings: " . $conn->error;
        }
    }
}

// Fetch current settings
$query = "SELECT * FROM school_settings LIMIT 1";
$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    $settings = $result->fetch_assoc();
} else {
    // Create default settings if none exist
    $insertQuery = "INSERT INTO school_settings (school_name, school_address, school_phone, school_email) 
                   VALUES ('Default School Name', 'Default School Address', '************', '<EMAIL>')";
    if ($conn->query($insertQuery)) {
        $settings = [
            'school_name' => 'Default School Name',
            'school_address' => 'Default School Address',
            'school_phone' => '************',
            'school_email' => '<EMAIL>',
            'logo_path' => ''
        ];
    } else {
        $error_message = "Error creating default settings: " . $conn->error;
        $settings = [
            'school_name' => '',
            'school_address' => '',
            'school_phone' => '',
            'school_email' => '',
            'logo_path' => ''
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings | Admin Panel</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .logo-preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
            margin-top: 10px;
        }
        .logo-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php 
            if (file_exists('includes/sidebar.php')) {
                include 'includes/sidebar.php';
            } else {
                echo '<div class="col-md-3 col-lg-2 sidebar bg-dark text-light p-3">
                    <h4>Admin Menu</h4>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-light" href="dashboard.php">Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-light" href="students.php">Students</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-light active" href="settings.php">Settings</a>
                        </li>
                    </ul>
                </div>';
            }
            ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settings</h1>
                </div>
                
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h5>School Settings</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="school_name" class="form-label">School Name</label>
                                        <input type="text" class="form-control" id="school_name" name="school_name" value="<?php echo $settings['school_name']; ?>" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="school_address" class="form-label">School Address</label>
                                        <textarea class="form-control" id="school_address" name="school_address" rows="3" required><?php echo $settings['school_address']; ?></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="school_phone" class="form-label">Phone Number</label>
                                        <input type="text" class="form-control" id="school_phone" name="school_phone" value="<?php echo $settings['school_phone']; ?>">
                                    </div>
                                    <div class="mb-3">
                                        <label for="school_email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="school_email" name="school_email" value="<?php echo $settings['school_email']; ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="school_logo" class="form-label">School Logo</label>
                                        <input type="file" class="form-control" id="school_logo" name="school_logo" accept="image/*" onchange="previewLogo(this)">
                                        <small class="form-text text-muted">Recommended size: 200x200 pixels. Supported formats: JPG, PNG, GIF.</small>
                                        
                                        <?php if (!empty($settings['logo_path']) && file_exists('../' . $settings['logo_path'])): ?>
                                            <div class="mt-3">
                                                <img src="../<?php echo $settings['logo_path']; ?>" alt="School Logo" class="logo-preview" id="logoPreview">
                                            </div>
                                        <?php else: ?>
                                            <div class="logo-placeholder" id="logoPlaceholder">
                                                <i class="fas fa-image fa-3x"></i>
                                            </div>
                                            <img src="" alt="School Logo" class="logo-preview d-none" id="logoPreview">
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" name="update_settings" class="btn btn-primary">Save Settings</button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        function previewLogo(input) {
            const logoPreview = document.getElementById('logoPreview');
            const logoPlaceholder = document.getElementById('logoPlaceholder');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    logoPreview.src = e.target.result;
                    logoPreview.classList.remove('d-none');
                    if (logoPlaceholder) {
                        logoPlaceholder.classList.add('d-none');
                    }
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html> 