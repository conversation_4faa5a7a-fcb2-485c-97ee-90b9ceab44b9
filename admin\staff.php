<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create staff table if it doesn't exist
$staffTableQuery = "CREATE TABLE IF NOT EXISTS staff (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    staff_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    joining_date DATE NULL,
    role VARCHAR(100) NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    user_id INT(11) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($staffTableQuery);

// Add department_id column if it doesn't exist
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM staff LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE staff ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Add role column if it doesn't exist
$checkRoleColumnQuery = "SHOW COLUMNS FROM staff LIKE 'role'";
$roleColumnResult = $conn->query($checkRoleColumnQuery);
if ($roleColumnResult->num_rows == 0) {
    $addRoleColumnQuery = "ALTER TABLE staff ADD COLUMN role VARCHAR(100) NULL";
    $conn->query($addRoleColumnQuery);
}

// Add joining_date column if it doesn't exist
$checkJoiningDateColumnQuery = "SHOW COLUMNS FROM staff LIKE 'joining_date'";
$joiningDateColumnResult = $conn->query($checkJoiningDateColumnQuery);
if ($joiningDateColumnResult->num_rows == 0) {
    $addJoiningDateColumnQuery = "ALTER TABLE staff ADD COLUMN joining_date DATE NULL";
    $conn->query($addJoiningDateColumnQuery);
}

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Handle staff addition
if (isset($_POST['add_staff'])) {
    $staffId = $conn->real_escape_string($_POST['staff_id']);
    $firstName = $conn->real_escape_string($_POST['first_name']);
    $lastName = $conn->real_escape_string($_POST['last_name']);
    $email = $conn->real_escape_string($_POST['email'] ?? '');
    $phone = $conn->real_escape_string($_POST['phone'] ?? '');
    $gender = $conn->real_escape_string($_POST['gender'] ?? 'male');
    $departmentId = $_POST['department_id'] ?? null;
    $role = $conn->real_escape_string($_POST['role'] ?? '');
    $joiningDate = $conn->real_escape_string($_POST['joining_date'] ?? '');
    
    if (empty($staffId) || empty($firstName) || empty($lastName)) {
        $error_msg = "স্টাফ আইডি, নাম এবং পদবি আবশ্যক";
    } else {
        $insertQuery = "INSERT INTO staff (staff_id, first_name, last_name, email, phone, gender, department_id, role, joining_date) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("ssssssiss", $staffId, $firstName, $lastName, $email, $phone, $gender, $departmentId, $role, $joiningDate);
        
        if ($stmt->execute()) {
            $success_msg = "স্টাফ সফলভাবে যোগ করা হয়েছে";
        } else {
            $error_msg = "স্টাফ যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Handle staff deletion
if (isset($_GET['delete'])) {
    $staffId = $_GET['delete'];
    $deleteQuery = "DELETE FROM staff WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $staffId);
    
    if ($stmt->execute()) {
        $success_msg = "স্টাফ সফলভাবে মুছে ফেলা হয়েছে";
    } else {
        $error_msg = "স্টাফ মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Get all staff with department information
$staffQuery = "SELECT s.*, d.department_name 
               FROM staff s 
               LEFT JOIN departments d ON s.department_id = d.id 
               ORDER BY s.first_name, s.last_name";
$staffMembers = $conn->query($staffQuery);

// Get all departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>স্টাফ ব্যবস্থাপনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <h2 class="text-white text-center mb-4">অ্যাডমিন প্যানেল</h2>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="teachers.php"><i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক</a>
                <a href="staff.php" class="active"><i class="fas fa-user-tie me-2"></i> স্টাফ</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 content">
                <h1 class="mb-4">স্টাফ ব্যবস্থাপনা</h1>
                
                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add Staff Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">নতুন স্টাফ যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="staff.php">
                                    <div class="mb-3">
                                        <label for="staff_id" class="form-label">স্টাফ আইডি*</label>
                                        <input type="text" class="form-control" id="staff_id" name="staff_id" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">নাম*</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">পদবি*</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <input type="email" class="form-control" id="email" name="email">
                                    </div>
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন নম্বর</label>
                                        <input type="text" class="form-control" id="phone" name="phone">
                                    </div>
                                    <div class="mb-3">
                                        <label for="gender" class="form-label">লিঙ্গ</label>
                                        <select class="form-select" id="gender" name="gender">
                                            <option value="male">পুরুষ</option>
                                            <option value="female">মহিলা</option>
                                            <option value="other">অন্যান্য</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select" id="department_id" name="department_id">
                                            <option value="">বিভাগ নির্বাচন করুন</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo $dept['id']; ?>">
                                                        <?php echo htmlspecialchars($dept['department_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="role" class="form-label">পদবি/ভূমিকা</label>
                                        <input type="text" class="form-control" id="role" name="role" placeholder="যেমন: অফিস সহকারী, আইটি সহায়ক">
                                    </div>
                                    <div class="mb-3">
                                        <label for="joining_date" class="form-label">যোগদানের তারিখ</label>
                                        <input type="date" class="form-control" id="joining_date" name="joining_date">
                                    </div>
                                    <button type="submit" name="add_staff" class="btn btn-primary">স্টাফ যোগ করুন</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Staff List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">স্টাফ তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>স্টাফ আইডি</th>
                                                <th>নাম</th>
                                                <th>বিভাগ</th>
                                                <th>পদবি</th>
                                                <th>ফোন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($staffMembers && $staffMembers->num_rows > 0): ?>
                                                <?php while ($staff = $staffMembers->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($staff['staff_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['first_name'] . ' ' . $staff['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['role'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($staff['phone'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <a href="edit_staff.php?id=<?php echo $staff['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="staff.php?delete=<?php echo $staff['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই স্টাফকে মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">কোন স্টাফ পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 