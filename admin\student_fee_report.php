<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$printMode = isset($_GET['print']) && $_GET['print'] == 'true';

if ($studentId <= 0) {
    echo "<div class='alert alert-danger'>Invalid student ID.</div>";
    exit();
}

// Get student details
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name 
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    echo "<div class='alert alert-danger'>Student not found.</div>";
    exit();
}

$student = $studentResult->fetch_assoc();

// Get all fees for the student
$feesQuery = "SELECT f.*, 
             (SELECT GROUP_CONCAT(CONCAT(fp.payment_date, ':', fp.amount, ':', COALESCE(pr.payment_method, 'N/A')) SEPARATOR '|') 
              FROM fee_payments fp 
              LEFT JOIN payment_receipts pr ON fp.receipt_id = pr.id 
              WHERE fp.fee_id = f.id) as payment_details
             FROM fees f
             WHERE f.student_id = ?
             ORDER BY f.due_date DESC";

$stmt = $conn->prepare($feesQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$feesResult = $stmt->get_result();

$fees = [];
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;

// Check if fee_payments table exists
$tableCheckQuery = "SHOW TABLES LIKE 'fee_payments'";
$feePaymentsTableExists = $conn->query($tableCheckQuery)->num_rows > 0;

while ($fee = $feesResult->fetch_assoc()) {
    $due = $fee['amount'] - $fee['paid'];
    $totalAmount += $fee['amount'];
    $totalPaid += $fee['paid'];
    $totalDue += $due;
    
    // Process payment details if available
    $paymentHistory = [];
    if ($feePaymentsTableExists && !empty($fee['payment_details'])) {
        $payments = explode('|', $fee['payment_details']);
        foreach ($payments as $payment) {
            $paymentData = explode(':', $payment);
            if (count($paymentData) >= 3) {
                $paymentHistory[] = [
                    'date' => $paymentData[0],
                    'amount' => $paymentData[1],
                    'method' => $paymentData[2]
                ];
            }
        }
    } elseif ($fee['paid'] > 0) {
        // If fee_payments table doesn't exist but fee is paid
        $paymentHistory[] = [
            'date' => $fee['payment_date'] ?? $fee['created_at'] ?? 'Unknown',
            'amount' => $fee['paid'],
            'method' => 'N/A'
        ];
    }
    
    $fee['payment_history'] = $paymentHistory;
    $fees[] = $fee;
}

// Group monthly fees for better reporting
$monthlyFees = [];
foreach ($fees as $fee) {
    if (strpos($fee['fee_type'], 'মাসিক বেতন') !== false) {
        $monthName = str_replace('মাসিক বেতন - ', '', $fee['fee_type']);
        if (!isset($monthlyFees[$monthName])) {
            $monthlyFees[$monthName] = [
                'amount' => 0,
                'paid' => 0,
                'due' => 0,
                'status' => 'due'
            ];
        }
        $monthlyFees[$monthName]['amount'] += $fee['amount'];
        $monthlyFees[$monthName]['paid'] += $fee['paid'];
        $monthlyFees[$monthName]['due'] += ($fee['amount'] - $fee['paid']);
        
        if ($fee['payment_status'] === 'paid') {
            $monthlyFees[$monthName]['status'] = 'paid';
        } elseif ($fee['payment_status'] === 'partial' && $monthlyFees[$monthName]['status'] !== 'paid') {
            $monthlyFees[$monthName]['status'] = 'partial';
        }
    }
}

// Month order for sorting
$monthOrder = [
    'জানুয়ারি' => 1, 'ফেব্রুয়ারি' => 2, 'মার্চ' => 3, 'এপ্রিল' => 4,
    'মে' => 5, 'জুন' => 6, 'জুলাই' => 7, 'আগস্ট' => 8,
    'সেপ্টেম্বর' => 9, 'অক্টোবর' => 10, 'নভেম্বর' => 11, 'ডিসেম্বর' => 12
];

// Sort monthly fees by month order
uksort($monthlyFees, function($a, $b) use ($monthOrder) {
    return $monthOrder[$a] - $monthOrder[$b];
});
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী ফি রিপোর্ট - <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'SolaimanLipi', Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .report-container {
            background-color: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .school-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #3498db;
        }
        .student-info {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .fee-status-paid {
            color: #198754;
            font-weight: bold;
        }
        .fee-status-partial {
            color: #ffc107;
            font-weight: bold;
        }
        .fee-status-due {
            color: #dc3545;
            font-weight: bold;
        }
        .monthly-status {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .payment-history {
            font-size: 0.9rem;
        }
        .report-footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 0.85rem;
            color: #6c757d;
        }
        @media print {
            body {
                background-color: #fff;
            }
            .no-print {
                display: none !important;
            }
            .report-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
            a {
                text-decoration: none;
                color: #000;
            }
        }
    </style>
</head>
<body<?= $printMode ? ' onload="window.print()"' : '' ?>>
    <div class="container my-4 report-container">
        <!-- Print Button -->
        <?php if (!$printMode): ?>
        <div class="d-flex justify-content-between mb-3 no-print">
            <a href="fees.php" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>ফিরে যান
            </a>
            <a href="student_fee_report.php?student_id=<?= $studentId ?>&print=true" class="btn btn-sm btn-primary">
                <i class="fas fa-print me-2"></i>প্রিন্ট করুন
            </a>
        </div>
        <?php endif; ?>
        
        <!-- School Header -->
        <div class="school-header">
            <h2>আজমগঞ্জ ফাজিল মাদ্রাসা</h2>
            <p>আজমগঞ্জ, সিলেট - শিক্ষার্থী ফি রিপোর্ট</p>
        </div>
        
        <!-- Student Info -->
        <div class="student-info">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>শিক্ষার্থী আইডি:</strong> <?= htmlspecialchars($student['student_id']) ?></p>
                    <p><strong>নাম:</strong> <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></p>
                    <p><strong>ক্লাস:</strong> <?= htmlspecialchars($student['class_name'] ?? 'N/A') ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>বিভাগ:</strong> <?= htmlspecialchars($student['department_name'] ?? 'N/A') ?></p>
                    <p><strong>সেশন:</strong> <?= htmlspecialchars($student['session_name'] ?? 'N/A') ?></p>
                    <p><strong>রিপোর্ট তারিখ:</strong> <?= date('d/m/Y') ?></p>
                </div>
            </div>
        </div>
        
        <!-- Fee Summary -->
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="p-3 bg-light rounded">
                    <h5>মোট ফি</h5>
                    <h3>৳ <?= number_format($totalAmount, 2) ?></h3>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="p-3 bg-light rounded">
                    <h5>পরিশোধিত</h5>
                    <h3 class="text-success">৳ <?= number_format($totalPaid, 2) ?></h3>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="p-3 bg-light rounded">
                    <h5>বকেয়া</h5>
                    <h3 class="<?= $totalDue > 0 ? 'text-danger' : 'text-success' ?>">
                        ৳ <?= number_format($totalDue, 2) ?>
                    </h3>
                </div>
            </div>
        </div>
        
        <!-- Monthly Fees Progress -->
        <?php if (!empty($monthlyFees)): ?>
        <div class="monthly-status">
            <h4 class="mb-3">মাসিক বেতন স্ট্যাটাস</h4>
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-light">
                        <tr>
                            <th>মাস</th>
                            <th>মোট</th>
                            <th>পরিশোধিত</th>
                            <th>বাকি</th>
                            <th>স্ট্যাটাস</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($monthlyFees as $month => $data): ?>
                        <tr>
                            <td><?= htmlspecialchars($month) ?></td>
                            <td>৳ <?= number_format($data['amount'], 2) ?></td>
                            <td>৳ <?= number_format($data['paid'], 2) ?></td>
                            <td>৳ <?= number_format($data['due'], 2) ?></td>
                            <td>
                                <?php
                                $statusClass = '';
                                $statusText = '';
                                
                                if ($data['status'] === 'paid') {
                                    $statusClass = 'fee-status-paid';
                                    $statusText = 'পরিশোধিত';
                                } elseif ($data['status'] === 'partial') {
                                    $statusClass = 'fee-status-partial';
                                    $statusText = 'আংশিক';
                                } else {
                                    $statusClass = 'fee-status-due';
                                    $statusText = 'বকেয়া';
                                }
                                ?>
                                <span class="<?= $statusClass ?>"><?= $statusText ?></span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- All Fees List -->
        <h4 class="mb-3">সকল ফি বিবরণ</h4>
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-light">
                    <tr>
                        <th>ফি টাইপ</th>
                        <th>মোট</th>
                        <th>পরিশোধিত</th>
                        <th>বাকি</th>
                        <th>শেষ তারিখ</th>
                        <th>স্ট্যাটাস</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fees as $fee): ?>
                    <?php
                    $due = $fee['amount'] - $fee['paid'];
                    $statusClass = '';
                    $statusText = '';
                    
                    if ($fee['payment_status'] === 'paid') {
                        $statusClass = 'fee-status-paid';
                        $statusText = 'পরিশোধিত';
                    } elseif ($fee['payment_status'] === 'partial') {
                        $statusClass = 'fee-status-partial';
                        $statusText = 'আংশিক';
                    } else {
                        $statusClass = 'fee-status-due';
                        $statusText = 'বকেয়া';
                    }
                    ?>
                    <tr>
                        <td><?= htmlspecialchars($fee['fee_type']) ?></td>
                        <td>৳ <?= number_format($fee['amount'], 2) ?></td>
                        <td>৳ <?= number_format($fee['paid'], 2) ?></td>
                        <td>৳ <?= number_format($due, 2) ?></td>
                        <td><?= date('d/m/Y', strtotime($fee['due_date'])) ?></td>
                        <td><span class="<?= $statusClass ?>"><?= $statusText ?></span></td>
                    </tr>
                    <?php if (!empty($fee['payment_history'])): ?>
                    <tr class="payment-history bg-light">
                        <td colspan="6">
                            <small><strong>পেমেন্ট ইতিহাস:</strong></small>
                            <ul class="list-unstyled mb-0">
                                <?php foreach ($fee['payment_history'] as $payment): ?>
                                <li>
                                    <small>
                                        <?= date('d/m/Y', strtotime($payment['date'])) ?> - 
                                        ৳ <?= number_format($payment['amount'], 2) ?> 
                                        (<?= htmlspecialchars($payment['method']) ?>)
                                    </small>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </td>
                    </tr>
                    <?php endif; ?>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="table-primary">
                    <tr>
                        <th>মোট</th>
                        <th>৳ <?= number_format($totalAmount, 2) ?></th>
                        <th>৳ <?= number_format($totalPaid, 2) ?></th>
                        <th>৳ <?= number_format($totalDue, 2) ?></th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <!-- Report Footer -->
        <div class="report-footer text-center">
            <p>এই রিপোর্টটি <?= date('d/m/Y h:i A') ?> তারিখে জেনারেট করা হয়েছে</p>
            <p>© <?= date('Y') ?> আজমগঞ্জ ফাজিল মাদ্রাসা - সর্বস্বত্ব সংরক্ষিত</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 