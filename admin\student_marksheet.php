<?php
session_start();
require_once('../includes/dbh.inc.php');
require_once('includes/functions.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Check if passing_marks_config table exists and create it if it doesn't
$check_table_sql = "SHOW TABLES LIKE 'passing_marks_config'";
$table_result = $conn->query($check_table_sql);

if ($table_result->num_rows == 0) {
    // Table doesn't exist, create it
    $create_table_sql = "CREATE TABLE IF NOT EXISTS passing_marks_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_id INT NULL,
        min_percentage DECIMAL(5,2) NOT NULL,
        max_percentage DECIMAL(5,2) NOT NULL,
        passing_mark DECIMAL(5,2) NOT NULL,
        cq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
        mcq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
        practical_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00,
        grade VARCHAR(5) NOT NULL,
        grade_point DECIMAL(3,2) NOT NULL,
        description VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY(subject_id, min_percentage, max_percentage)
    )";
    $conn->query($create_table_sql);
    
    // Insert default values
    $default_values = [
        [NULL, '80.00', '100.00', '80.00', '40.00', '40.00', '40.00', 'A+', '5.00', 'শ্রেষ্ঠত্ব (Excellence)'],
        [NULL, '70.00', '79.99', '70.00', '35.00', '35.00', '35.00', 'A', '4.00', 'অতি উত্তম (Very Good)'],
        [NULL, '60.00', '69.99', '60.00', '33.00', '33.00', '33.00', 'A-', '3.50', 'উত্তম (Good)'],
        [NULL, '50.00', '59.99', '50.00', '33.00', '33.00', '33.00', 'B', '3.00', 'ভালো (Satisfactory)'],
        [NULL, '40.00', '49.99', '40.00', '33.00', '33.00', '33.00', 'C', '2.00', 'মোটামুটি (Average)'],
        [NULL, '33.00', '39.99', '33.00', '33.00', '33.00', '33.00', 'D', '1.00', 'নিম্নমান (Poor)'],
        [NULL, '0.00', '32.99', '0.00', '0.00', '0.00', '0.00', 'F', '0.00', 'অকৃতকার্য (Fail)']
    ];

    $insert_sql = "INSERT INTO passing_marks_config 
                 (subject_id, min_percentage, max_percentage, passing_mark, cq_passing_percent, mcq_passing_percent, practical_passing_percent, grade, grade_point, description) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($insert_sql);

    foreach ($default_values as $value) {
        $stmt->bind_param("iddddddsds", $value[0], $value[1], $value[2], $value[3], $value[4], $value[5], $value[6], $value[7], $value[8], $value[9]);
        $stmt->execute();
    }
} else {
    // Check if subject_id column exists
    $check_column_sql = "SHOW COLUMNS FROM passing_marks_config LIKE 'subject_id'";
    $column_result = $conn->query($check_column_sql);
    
    if ($column_result->num_rows == 0) {
        // subject_id column doesn't exist, add it
        $alter_table_sql = "ALTER TABLE passing_marks_config 
                           ADD COLUMN subject_id INT NULL AFTER id,
                           ADD COLUMN cq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER passing_mark,
                           ADD COLUMN mcq_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER cq_passing_percent,
                           ADD COLUMN practical_passing_percent DECIMAL(5,2) NOT NULL DEFAULT 33.00 AFTER mcq_passing_percent,
                           DROP INDEX IF EXISTS subject_id,
                           ADD UNIQUE KEY(subject_id, min_percentage, max_percentage)";
        $conn->query($alter_table_sql);
    }
}

// Get all exams
// First check if class_id column exists in exams table
$check_class_id = $conn->query("SHOW COLUMNS FROM exams LIKE 'class_id'");
if ($check_class_id->num_rows > 0) {
    // If class_id exists, join with classes table
    $exams_sql = "SELECT e.*, c.class_name 
                FROM exams e 
                JOIN classes c ON e.class_id = c.id 
                ORDER BY e.exam_date DESC";
} else {
    // If class_id doesn't exist, don't join with classes table
    $exams_sql = "SELECT e.* 
                FROM exams e 
                ORDER BY e.exam_date DESC";
}
$exams_result = $conn->query($exams_sql);

// Get all classes
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);

// Get students based on selected class
$students = [];
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$exam_id = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;
$student_id = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;

// If class_id is provided, get students from that class
if ($class_id > 0) {
    $students_sql = "SELECT s.*, 
                    CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                    COALESCE(s.roll_number, 'N/A') as roll_number
                    FROM students s 
                    WHERE s.class_id = ? 
                    ORDER BY s.roll_number, s.first_name, s.last_name";
    $students_stmt = $conn->prepare($students_sql);
    $students_stmt->bind_param("i", $class_id);
    $students_stmt->execute();
    $students_result = $students_stmt->get_result();
    
    while ($row = $students_result->fetch_assoc()) {
        $students[] = $row;
    }
}

// Student details
$student_details = null;
$exam_details = null;
$marks_data = [];
$total_marks = 0;
$obtained_marks = 0;
$percentage = 0;
$gpa = 0;
$grade = '';
$result_status = '';
$gpa_without_fourth = 0; // GPA without 4th subject adjustment
$gpa_with_fourth = 0;    // GPA with 4th subject adjustment
$fourth_subject_data = null; // Store 4th subject data

// If student_id and exam_id are provided, get the marksheet
if ($student_id > 0 && $exam_id > 0) {
    // Get student details
    $student_sql = "SELECT s.*, 
                   CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                   COALESCE(s.roll_number, 'N/A') as roll_number,
                   c.class_name
                   FROM students s 
                   JOIN classes c ON s.class_id = c.id
                   WHERE s.id = ?";
    $student_stmt = $conn->prepare($student_sql);
    $student_stmt->bind_param("i", $student_id);
    $student_stmt->execute();
    $student_details = $student_stmt->get_result()->fetch_assoc();
    
    // Try to find image field - check various possible column names for image
    $possible_image_fields = ['profile_photo', 'image', 'photo', 'student_image', 'image_path', 'picture'];
    $image_path = '';
    
    foreach ($possible_image_fields as $field) {
        if (isset($student_details[$field]) && !empty($student_details[$field])) {
            $image_path = $student_details[$field];
            break;
        }
    }
    
    // If still no image found, try to query the database structure
    if (empty($image_path)) {
        $columns_query = "SHOW COLUMNS FROM students LIKE '%image%' OR LIKE '%photo%' OR LIKE '%picture%'";
        $columns_result = $conn->query($columns_query);
        
        if ($columns_result && $columns_result->num_rows > 0) {
            while ($column = $columns_result->fetch_assoc()) {
                $field_name = $column['Field'];
                
                // Get the specific field value for this student
                $image_query = "SELECT $field_name FROM students WHERE id = ?";
                $image_stmt = $conn->prepare($image_query);
                $image_stmt->bind_param("i", $student_id);
                $image_stmt->execute();
                $image_result = $image_stmt->get_result();
                
                if ($image_result && $image_result->num_rows > 0) {
                    $image_data = $image_result->fetch_assoc();
                    if (!empty($image_data[$field_name])) {
                        $image_path = $image_data[$field_name];
                        break;
                    }
                }
            }
        }
    }
    
    // Store image path
    $student_details['image_path'] = $image_path;
    
    // Get exam details
    if ($check_class_id->num_rows > 0) {
        // If class_id exists, join with classes table
        $exam_sql = "SELECT e.*, c.class_name 
                    FROM exams e 
                    JOIN classes c ON e.class_id = c.id 
                    WHERE e.id = ?";
    } else {
        // If class_id doesn't exist, don't join with classes table
        $exam_sql = "SELECT e.* 
                    FROM exams e 
                    WHERE e.id = ?";
    }
    $exam_stmt = $conn->prepare($exam_sql);
    $exam_stmt->bind_param("i", $exam_id);
    $exam_stmt->execute();
    $exam_details = $exam_stmt->get_result()->fetch_assoc();
    
    // Get marks for all subjects
    $marks_sql = "SELECT m.*, s.subject_name, s.subject_code, es.cq_marks as max_cq, 
                 es.mcq_marks as max_mcq, es.practical_marks as max_practical, 
                 es.total_marks as max_total, e.pass_marks,
                 COALESCE(ss.category, 'required') as subject_category
                 FROM marks m
                 JOIN subjects s ON m.subject_id = s.id
                 JOIN exam_subjects es ON (m.exam_id = es.exam_id AND m.subject_id = es.subject_id)
                 JOIN exams e ON m.exam_id = e.id
                 LEFT JOIN student_subjects ss ON (m.student_id = ss.student_id AND m.subject_id = ss.subject_id)
                 WHERE m.exam_id = ? AND m.student_id = ?
                 ORDER BY s.subject_name";
    $marks_stmt = $conn->prepare($marks_sql);
    $marks_stmt->bind_param("ii", $exam_id, $student_id);
    $marks_stmt->execute();
    $marks_result = $marks_stmt->get_result();
    
    $fail_count = 0;
    $total_subject_count = 0;
    $total_gpa_points = 0;
    $fourth_subject_marks = null;
    
    while ($mark = $marks_result->fetch_assoc()) {
        // Calculate total marks
        $total_marks += $mark['max_total'];
        $obtained_marks += $mark['total_marks'];
        
        // Check if this is a fourth subject
        $is_fourth_subject = ($mark['subject_category'] === 'fourth');
        $mark['is_fourth_subject'] = $is_fourth_subject;
        
        // Check if pass or fail - check all components (CQ, MCQ, Practical) and overall
        $cq_passed = isComponentPassed($mark['cq_marks'], $mark['max_cq'], 'cq', $mark['subject_id']);
        $mcq_passed = isComponentPassed($mark['mcq_marks'], $mark['max_mcq'], 'mcq', $mark['subject_id']);
        $practical_passed = isComponentPassed($mark['practical_marks'], $mark['max_practical'], 'practical', $mark['subject_id']);
        $overall_passed = isPassed($mark['total_marks'], $mark['max_total'], $mark['subject_id']);
        
        // Store component statuses in the mark array
        $mark['cq_passed'] = $cq_passed;
        $mark['mcq_passed'] = $mcq_passed;
        $mark['practical_passed'] = $practical_passed;
        $mark['overall_passed'] = $overall_passed;
        
        // Get GPA for this subject
        $subject_gpa = getGPA($mark['total_marks'], $mark['max_total'], $mark['subject_id']);
        
        // If failed in CQ or MCQ, set GPA to 0 regardless of total marks
        if ((!$cq_passed) || (!$mcq_passed)) {
            $subject_gpa = 0;
        }
        
        $mark['gpa'] = $subject_gpa;
        
        // Count total subjects and add GPA points - always count all subjects including 4th subject
        $total_subject_count++;
        $total_gpa_points += $subject_gpa;
        
        // Handle 4th subject separately for the extra points calculation
        if ($is_fourth_subject) {
            $fourth_subject_gpa = $subject_gpa;
            $fourth_subject_data = $mark;
            
            // Store fourth subject separately
            $fourth_subject_marks = $mark;
        }
        
        // Fail if any component is failed or overall is failed
        if (!$cq_passed || !$mcq_passed || !$practical_passed || !$overall_passed) {
            $fail_count++;
            
            // Build fail reason string
            $fail_reasons = [];
            if (!$cq_passed) $fail_reasons[] = 'CQ';
            if (!$mcq_passed) $fail_reasons[] = 'MCQ';
            if (!$practical_passed) $fail_reasons[] = 'Practical';
            $mark['fail_reasons'] = !empty($fail_reasons) ? implode(', ', $fail_reasons) : '';
        }
        
        // Add to marks_data array only if it's not a fourth subject
        if (!$is_fourth_subject) {
            $marks_data[] = $mark;
        }
    }
    
    // Add the fourth subject at the end
    if ($fourth_subject_marks) {
        $marks_data[] = $fourth_subject_marks;
    }
    
    // Calculate percentage and GPA
    if ($total_marks > 0) {
        $percentage = ($obtained_marks / $total_marks) * 100;
        
        // Calculate GPA without 4th subject
        $gpa_without_fourth = $total_gpa_points / $total_subject_count;
        
        // Calculate GPA with 4th subject contribution (add extra points from 4th subject)
        if (isset($fourth_subject_data)) {
            require_once('includes/functions.php');
            // Get excess points from 4th subject (if any)
            $fourth_subject_contribution = calculate_fourth_subject_contribution($fourth_subject_data['gpa']);
            
            // For GPA with 4th subject, add the contribution to the sum and divide by total subjects
            $gpa_with_fourth = $gpa_without_fourth + ($fourth_subject_contribution / $total_subject_count);
            
            // Cap GPA at 5.0
            if ($gpa_with_fourth > 5.0) {
                $gpa_with_fourth = 5.0;
            }
        } else {
            $gpa_with_fourth = $gpa_without_fourth;
        }
        
        // Set the final GPA to the one with 4th subject
        $gpa = $gpa_with_fourth;
        
        // Calculate grade based on final GPA
        $grade = get_letter_grade($gpa);
    }
    
    // Final result - Pass or Fail
    $result_status = ($fail_count > 0) ? 'অকৃতকার্য' : 'কৃতকার্য';
}

// Function to get letter grade based on marks
function getGrade($marks, $total, $subject_id = NULL) {
    global $conn;
    $percentage = ($marks / $total) * 100;
    
    // Try to get subject-specific config first
    if ($subject_id) {
        $sql = "SELECT grade FROM passing_marks_config 
                WHERE subject_id = ? AND ? BETWEEN min_percentage AND max_percentage 
                ORDER BY min_percentage DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("id", $subject_id, $percentage);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc()['grade'];
        }
    }
    
    // If no subject-specific config, get default config
    $sql = "SELECT grade FROM passing_marks_config 
            WHERE subject_id IS NULL AND ? BETWEEN min_percentage AND max_percentage 
            ORDER BY min_percentage DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("d", $percentage);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc()['grade'];
    }
    
    // Fallback to default grading if no config is found
    if ($percentage >= 80) return 'A+';
    elseif ($percentage >= 70) return 'A';
    elseif ($percentage >= 60) return 'A-';
    elseif ($percentage >= 50) return 'B';
    elseif ($percentage >= 40) return 'C';
    elseif ($percentage >= 33) return 'D';
    else return 'F';
}

// Function to get GPA based on marks
function getGPA($marks, $total, $subject_id = NULL) {
    global $conn;
    $percentage = ($marks / $total) * 100;
    
    // Try to get subject-specific config first
    if ($subject_id) {
        $sql = "SELECT grade_point FROM passing_marks_config 
                WHERE subject_id = ? AND ? BETWEEN min_percentage AND max_percentage 
                ORDER BY min_percentage DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("id", $subject_id, $percentage);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc()['grade_point'];
        }
    }
    
    // If no subject-specific config, get default config
    $sql = "SELECT grade_point FROM passing_marks_config 
            WHERE subject_id IS NULL AND ? BETWEEN min_percentage AND max_percentage 
            ORDER BY min_percentage DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("d", $percentage);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc()['grade_point'];
    }
    
    // Fallback to default grading if no config is found
    if ($percentage >= 80) return 5.0;
    elseif ($percentage >= 70) return 4.0;
    elseif ($percentage >= 60) return 3.5;
    elseif ($percentage >= 50) return 3.0;
    elseif ($percentage >= 40) return 2.0;
    elseif ($percentage >= 33) return 1.0;
    else return 0;
}

// Function to check if a student passed a subject based on marks
function isPassed($marks, $total, $subject_id = NULL) {
    global $conn;
    
    // First check if there was a component failure (CQ or MCQ)
    // This info should be passed from the calling context, but we'll just use a placeholder check
    // The actual component check should be done before calling this function
    
    $percentage = ($marks / $total) * 100;
    
    // First try to get overall passing percentage from subject_passing_config
    if ($subject_id) {
        $sql = "SELECT overall_pass_percentage FROM subject_passing_config 
                WHERE subject_id = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $passing_percentage = $result->fetch_assoc()['overall_pass_percentage'];
            return $percentage >= $passing_percentage;
        }
    }
    
    // If no subject-specific config in new table, try from the general passing_marks_config
    // Try to get subject-specific config first
    if ($subject_id) {
        $sql = "SELECT passing_mark FROM passing_marks_config 
                WHERE subject_id = ? AND ? BETWEEN min_percentage AND max_percentage 
                ORDER BY min_percentage DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("id", $subject_id, $percentage);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $passing_percentage = $result->fetch_assoc()['passing_mark'];
            return $percentage >= $passing_percentage;
        }
    }
    
    // If no subject-specific config, get default config
    $sql = "SELECT passing_mark FROM passing_marks_config 
            WHERE subject_id IS NULL AND ? BETWEEN min_percentage AND max_percentage 
            ORDER BY min_percentage DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("d", $percentage);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $passing_percentage = $result->fetch_assoc()['passing_mark'];
        return $percentage >= $passing_percentage;
    }
    
    // Fallback to default 33% passing mark
    return $percentage >= 33;
}

// Function to check if a student passed in each component (CQ, MCQ, Practical)
function isComponentPassed($component_marks, $max_component, $component_type, $subject_id = NULL) {
    if ($max_component <= 0) {
        return true; // If component doesn't exist, consider it passed
    }
    
    global $conn;
    
    // For CQ and MCQ, first check the subject_minimum_pass table for absolute minimum marks
    if ($component_type == 'cq' || $component_type == 'mcq') {
        if ($subject_id) {
            $column_name = $component_type . '_min_marks';
            $sql = "SELECT $column_name FROM subject_minimum_pass 
                    WHERE subject_id = ? AND is_active = 1";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("i", $subject_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $min_marks = $result->fetch_assoc()[$column_name];
                // If min_marks is defined and > 0, check if student achieved at least that many marks
                if ($min_marks > 0) {
                    return $component_marks >= $min_marks;
                }
                // Else, fall through to percentage-based checks
            }
        }
    }
    
    // If no absolute minimum marks are defined or for practical components,
    // proceed with percentage-based checks
    $percentage = ($component_marks / $max_component) * 100;
    
    $column_name = '';
    switch ($component_type) {
        case 'cq':
            $column_name = 'cq_pass_percentage';
            break;
        case 'mcq':
            $column_name = 'mcq_pass_percentage';
            break;
        case 'practical':
            $column_name = 'practical_pass_percentage';
            break;
        default:
            return true; // Unknown component type - consider it passed
    }
    
    // First try to get from subject_passing_config table
    if ($subject_id) {
        $sql = "SELECT $column_name FROM subject_passing_config 
                WHERE subject_id = ? AND is_active = 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("i", $subject_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $passing_percentage = $result->fetch_assoc()[$column_name];
            return $percentage >= $passing_percentage;
        }
    }
    
    // If no subject-specific config in new table, try from the general passing_marks_config
    // Try to get subject-specific config first
    if ($subject_id) {
        $old_column_name = str_replace('_pass_percentage', '_passing_percent', $column_name);
        $sql = "SELECT $old_column_name FROM passing_marks_config 
                WHERE subject_id = ? AND ? BETWEEN min_percentage AND max_percentage 
                ORDER BY min_percentage DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("id", $subject_id, $percentage);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $passing_percentage = $result->fetch_assoc()[$old_column_name];
            return $percentage >= $passing_percentage;
        }
    }
    
    // If no subject-specific config, get default config
    $old_column_name = str_replace('_pass_percentage', '_passing_percent', $column_name);
    $sql = "SELECT $old_column_name FROM passing_marks_config 
            WHERE subject_id IS NULL AND ? BETWEEN min_percentage AND max_percentage 
            ORDER BY min_percentage DESC LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("d", $percentage);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $passing_percentage = $result->fetch_assoc()[$old_column_name];
        return $percentage >= $passing_percentage;
    }
    
    // Fallback to default 33% passing mark
    return $percentage >= 33;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ছাত্র/ছাত্রীর মার্কশীট - <?php echo isset($student_details) ? $student_details['student_name'] : ''; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* General Styles */
        @media screen {
            body {
                font-family: 'Kalpurush', Arial, sans-serif;
                line-height: 1.5;
                background-color: #f8f9fa;
            }
            .marksheet-container {
                background-color: #fff;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                max-width: 210mm; /* A4 width */
                margin: 20px auto;
            }
        }
        
        /* Print Styles */
        @media print {
            @page {
                size: A4 portrait;
                margin: 0.5cm;
            }
            body {
                margin: 0;
                padding: 0;
                font-size: 10pt;
                background-color: #fff !important;
                width: 100%;
                height: 100%;
            }
            .col-md-3, .col-lg-2, .sidebar, .no-print, #layoutSidenav_nav,
            .navbar, .sb-sidenav, .breadcrumb, form, .card-header, .alert,
            footer, .btn, select, .navbar-brand, .navbar-toggler {
                display: none !important;
            }
            #layoutSidenav_content {
                margin-left: 0 !important;
                padding-left: 0 !important;
                width: 100% !important;
            }
            .main-content, .container-fluid {
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }
            .marksheet-container {
                width: 100% !important;
                max-width: 100% !important;
                margin: 0 !important;
                padding: 0.5cm !important;
                box-shadow: none !important;
                border: none !important;
                font-size: 10pt !important;
            }
            .school-info {
                font-size: 12pt !important;
            }
            .school-info h2 {
                font-size: 16pt !important;
                margin-bottom: 3pt !important;
            }
            .marks-table {
                width: 100% !important;
                font-size: 9pt !important;
                border-collapse: collapse !important;
            }
            .marks-table th, .marks-table td {
                padding: 3pt !important;
                border: 1px solid #000 !important;
            }
            .marks-table th {
                background-color: #f2f2f2 !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .student-info-container {
                font-size: 10pt !important;
            }
            .result-summary {
                font-size: 10pt !important;
                margin-top: 0.3cm !important;
            }
            .signature-section {
                margin-top: 0.5cm !important;
            }
            .signature-box {
                padding: 5pt 10pt 0 !important;
            }
            .signature-line {
                border-top: 1px solid #777 !important;
                padding-top: 2pt !important;
                margin-top: 30pt !important;
            }
            .fourth-subject-header {
                background-color: #e7f1ff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .fourth-subject td {
                border-top: 2px solid #007bff !important;
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
            .table-container {
                page-break-inside: avoid;
            }
            .student-photo {
                border: 1px solid #ddd !important;
            }
        }
        
        /* Common Styles for Both Screen and Print */
        .school-info {
            text-align: center;
            margin-bottom: 20px;
        }
        .school-info h2 {
            margin-bottom: 5px;
            font-weight: bold;
        }
        .school-info p {
            margin-bottom: 3px;
        }
        .marksheet-title {
            background-color: #f8f9fa;
            padding: 5px 10px;
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .student-info-container {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .student-info-left {
            flex: 2;
        }
        .student-info-right {
            flex: 1;
            text-align: right;
        }
        .student-photo {
            width: 90px;
            height: 110px;
            border: 1px solid #ddd;
            margin-left: 10px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
        }
        .student-photo img {
            max-width: 100%;
            max-height: 100%;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: 500;
            margin-right: 5px;
        }
        .marks-table {
            width: 100%;
            border-collapse: collapse;
        }
        .marks-table th, .marks-table td {
            padding: 5px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .marks-table th {
            background-color: #f2f2f2;
            font-weight: 600;
        }
        .component-fail {
            color: #dc3545;
            font-size: 0.8em;
            display: block;
        }
        .result-summary {
            display: flex;
            margin-top: 15px;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .result-summary-left, .result-summary-right {
            flex: 1;
        }
        .result-summary h5 {
            font-weight: 600;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .pass {
            color: #28a745;
            font-weight: 500;
        }
        .fail {
            color: #dc3545;
            font-weight: 500;
        }
        .signature-section {
            display: flex;
            margin-top: 30px;
        }
        .signature-box {
            flex: 1;
            text-align: center;
            padding: 30px 15px 0;
        }
        .signature-line {
            border-top: 1px solid #777;
            padding-top: 5px;
            font-weight: 500;
        }
        .fourth-subject {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .fourth-subject td {
            font-style: italic;
            border-top: 2px solid #007bff;
        }
        .fourth-subject-header {
            background-color: #e7f1ff;
            text-align: center;
            font-weight: bold;
            color: #0056b3;
            font-size: 0.9em;
            letter-spacing: 1px;
        }
        .info-item {
            margin-bottom: 5px;
        }
        .student-photo-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #aaa;
            font-size: 12px;
        }
        .table-container {
            overflow-x: auto;
        }
        .print-button {
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
                    <h1 class="h2">ছাত্র/ছাত্রীর মার্কশীট</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="marks_entry.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-pen-alt"></i> নম্বর এন্ট্রি
                            </a>
                            <a href="tabulation_sheet.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-table"></i> টেবুলেশন শীট
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Selection Form -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5>পরীক্ষা এবং ছাত্র/ছাত্রী নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row">
                            <div class="col-md-3 mb-3">
                                <label for="exam_id" class="form-label">পরীক্ষা নির্বাচন করুন</label>
                                <select class="form-select" id="exam_id" name="exam_id" required onchange="this.form.submit()">
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php if ($exams_result && $exams_result->num_rows > 0): 
                                        while ($exam = $exams_result->fetch_assoc()): 
                                    ?>
                                        <option value="<?php echo $exam['id']; ?>" <?php echo ($exam_id == $exam['id']) ? 'selected' : ''; ?>
                                                data-class="<?php echo isset($exam['class_id']) ? $exam['class_id'] : '0'; ?>">
                                            <?php echo $exam['exam_name'] . 
                                                     (isset($exam['class_name']) ? ' - ' . $exam['class_name'] : '') . 
                                                     ' (' . date('d/m/Y', strtotime($exam['exam_date'])) . ')'; ?>
                                        </option>
                                    <?php endwhile; endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <label for="class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select class="form-select" id="class_id" name="class_id" required onchange="this.form.submit()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php if ($classes_result && $classes_result->num_rows > 0):
                                        while ($class = $classes_result->fetch_assoc()): 
                                    ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo $class['class_name']; ?>
                                        </option>
                                    <?php endwhile; endif; ?>
                                </select>
                            </div>
                            
                            <?php if ($class_id > 0): ?>
                            <div class="col-md-4 mb-3">
                                <label for="student_id" class="form-label">ছাত্র/ছাত্রী নির্বাচন করুন</label>
                                <div class="input-group">
                                    <span class="input-group-text" onclick="searchStudents(true)" style="cursor: pointer;"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" id="student_search" placeholder="নাম বা রোল নম্বর দিয়ে খুঁজুন" onkeyup="searchStudents()" onkeypress="handleKeyPress(event)">
                                    <select class="form-select" id="student_id" name="student_id" required onchange="this.form.submit()">
                                        <option value="">ছাত্র/ছাত্রী নির্বাচন করুন</option>
                                        <?php foreach ($students as $student): ?>
                                            <option value="<?php echo $student['id']; ?>" <?php echo ($student_id == $student['id']) ? 'selected' : ''; ?> data-name="<?php echo $student['student_name']; ?>" data-roll="<?php echo $student['roll_number']; ?>">
                                                <?php echo $student['roll_number'] . ' - ' . $student['student_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> অনুসন্ধান করুন
                                </button>
                            </div>
                            
                            <div class="col-md-1 mb-3 d-flex align-items-end">
                                <button type="button" class="btn btn-success w-100" onclick="window.print()">
                                    <i class="fas fa-print"></i> প্রিন্ট
                                </button>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
                
                <?php if ($student_details && $exam_details && !empty($marks_data)): ?>
                <!-- Marksheet -->
                <div class="marksheet-container">
                    <div class="school-info">
                        <div class="school-name">স্কুল ম্যানেজমেন্ট সিস্টেম</div>
                        <div class="school-address">বাংলাদেশ</div>
                    </div>
                    
                    <div class="marksheet-title">ছাত্র/ছাত্রী মার্কশীট</div>
                    
                    <div class="student-info-container">
                        <div class="student-info-left">
                            <div class="info-item">
                                <span class="info-label">শিক্ষার্থীর নাম:</span> <?php echo $student_details['student_name']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">রোল নম্বর:</span> <?php echo $student_details['roll_number']; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">শ্রেণী:</span> <?php echo isset($student_details['class_name']) ? $student_details['class_name'] : 'N/A'; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">পিতা/অভিভাবকের নাম:</span> <?php echo $student_details['guardian_name'] ?: 'N/A'; ?>
                            </div>
                        </div>
                        <div class="student-info-right">
                            <div class="d-flex justify-content-end">
                                <div>
                                    <div class="info-item">
                                        <span class="info-label">পরীক্ষা:</span> <?php echo $exam_details['exam_name']; ?>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">তারিখ:</span> <?php echo date('d/m/Y', strtotime($exam_details['exam_date'])); ?>
                                    </div>
                                </div>
                                <div class="student-photo">
                                    <?php 
                                    // Check if image path exists
                                    if (!empty($student_details['image_path'])) {
                                        $image_path = $student_details['image_path'];
                                        
                                        // Check if the path already contains the uploads directory
                                        if (strpos($image_path, 'uploads/') === false) {
                                            $image_path = '../uploads/profile_photos/' . $image_path;
                                        } else if (strpos($image_path, '../') === false) {
                                            $image_path = '../' . $image_path;
                                        }
                                        
                                        // Display student photo with fallback
                                        echo '<img src="' . $image_path . '" alt="Student Photo" 
                                            onerror="this.onerror=null; this.style.display=\'none\'; document.getElementById(\'photo-fallback\').style.display=\'flex\';">';
                                        echo '<div id="photo-fallback" class="student-photo-placeholder" style="display:none;">
                                            <i class="fas fa-user fa-3x"></i>
                                        </div>';
                                    } else {
                                        echo '<div class="student-photo-placeholder">
                                            <i class="fas fa-user fa-3x"></i>
                                        </div>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="marks-table">
                            <thead>
                                <tr>
                                    <th>ক্রম</th>
                                    <th>বিষয়</th>
                                    <th>বিষয় কোড</th>
                                    <th>সিকিউ</th>
                                    <?php if (array_filter(array_column($marks_data, 'max_mcq'))): ?>
                                    <th>এমসিকিউ</th>
                                    <?php endif; ?>
                                    <?php if (array_filter(array_column($marks_data, 'max_practical'))): ?>
                                    <th>ব্যবহারিক</th>
                                    <?php endif; ?>
                                    <th>মোট</th>
                                    <th>পূর্ণ নম্বর</th>
                                    <th>লেটার গ্রেড</th>
                                    <th>জিপিএ</th>
                                    <th>মন্তব্য</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($marks_data as $index => $mark): 
                                    $is_fourth = isset($mark['is_fourth_subject']) && $mark['is_fourth_subject'];
                                    if ($is_fourth && $index > 0): ?>
                                    <tr>
                                        <td colspan="<?php echo 7 + (array_filter(array_column($marks_data, 'max_mcq')) ? 1 : 0) + (array_filter(array_column($marks_data, 'max_practical')) ? 1 : 0); ?>" class="fourth-subject-header">
                                            <i class="fas fa-book-open me-2"></i> ৪র্থ বিষয়
                                        </td>
                                    </tr>
                                <?php endif; ?>
                                <tr<?php echo $is_fourth ? ' class="fourth-subject"' : ''; ?>>
                                    <td><?php echo $index + 1; ?></td>
                                    <td>
                                        <?php echo $mark['subject_name']; ?>
                                        <?php if ($is_fourth): ?>
                                        <small class="d-block text-primary">(৪র্থ বিষয়)</small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $mark['subject_code']; ?></td>
                                    <td><?php echo intval($mark['cq_marks']); ?></td>
                                    <?php if (array_filter(array_column($marks_data, 'max_mcq'))): ?>
                                    <td><?php echo intval($mark['mcq_marks']); ?></td>
                                    <?php endif; ?>
                                    <?php if (array_filter(array_column($marks_data, 'max_practical'))): ?>
                                    <td><?php echo intval($mark['practical_marks']); ?></td>
                                    <?php endif; ?>
                                    <td><?php echo intval($mark['total_marks']); ?></td>
                                    <td><?php echo intval($mark['max_total']); ?></td>
                                    <td>
                                        <?php 
                                        // Check if failed in CQ or MCQ
                                        if ((isset($mark['cq_passed']) && !$mark['cq_passed']) || 
                                            (isset($mark['mcq_passed']) && !$mark['mcq_passed'])) {
                                            echo 'F'; // Always show F if failed in CQ or MCQ
                                        } else {
                                            echo getGrade($mark['total_marks'], $mark['max_total'], $mark['subject_id']);
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        // Show GPA 0 if failed in CQ or MCQ
                                        if ((isset($mark['cq_passed']) && !$mark['cq_passed']) || 
                                            (isset($mark['mcq_passed']) && !$mark['mcq_passed'])) {
                                            echo '0.0'; 
                                        } else {
                                            echo number_format($mark['gpa'], 1);
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (
                                            (isset($mark['cq_passed']) && !$mark['cq_passed']) || 
                                            (isset($mark['mcq_passed']) && !$mark['mcq_passed']) || 
                                            (isset($mark['practical_passed']) && !$mark['practical_passed']) ||
                                            !isPassed($mark['total_marks'], $mark['max_total'], $mark['subject_id'])
                                        ): ?>
                                        <span class="fail">অকৃতকার্য</span>
                                        <?php if (!empty($mark['fail_reasons'])): ?>
                                        <span class="component-fail"><?php echo $mark['fail_reasons']; ?> ফেইল</span>
                                        <?php endif; ?>
                                        <?php else: ?>
                                        <span class="pass">কৃতকার্য</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="result-summary">
                        <div class="result-summary-left">
                            <h5>ফলাফল সারসংক্ষেপ</h5>
                            <div class="info-item">
                                <span class="info-label">মোট বিষয়:</span> <?php echo count($marks_data); ?>
                                <?php if (isset($fourth_subject_data)): ?>
                                <small class="text-muted">(৪র্থ বিষয় সহ)</small>
                                <?php endif; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">মোট নম্বর:</span> <?php echo intval($total_marks); ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">প্রাপ্ত নম্বর:</span> <?php echo intval($obtained_marks); ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">শতকরা হার:</span> <?php echo number_format($percentage, 2); ?>%
                            </div>
                        </div>
                        <div class="result-summary-right">
                            <h5>চূড়ান্ত ফলাফল</h5>
                            <div class="info-item">
                                <span class="info-label">গড় জিপিএ (৪র্থ বিষয় বাদে):</span> <?php echo number_format($gpa_without_fourth, 2); ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">গড় জিপিএ (৪র্থ বিষয় সহ):</span> <?php echo number_format($gpa, 2); ?>
                            </div>
                            <?php if (isset($fourth_subject_data)): ?>
                            <div class="info-item text-primary">
                                <small>
                                    <?php 
                                    $contribution = $gpa - $gpa_without_fourth;
                                    $excess_points = calculate_fourth_subject_contribution($fourth_subject_data['gpa']);
                                    if ($excess_points > 0) {
                                        echo "৪র্থ বিষয় ({$fourth_subject_data['subject_name']}) থেকে বেশি " . number_format($excess_points, 2) . 
                                            " পয়েন্ট সকল বিষয়ের মধ্যে ভাগ হয়ে " . number_format($contribution, 2) . " পয়েন্ট যোগ হয়েছে।"; 
                                    } else {
                                        echo "৪র্থ বিষয় ({$fourth_subject_data['subject_name']}) থেকে কোন অতিরিক্ত পয়েন্ট যোগ হয়নি।"; 
                                    }
                                    ?>
                                </small>
                            </div>
                            <?php endif; ?>
                            <div class="info-item">
                                <span class="info-label">লেটার গ্রেড:</span> <?php echo $grade; ?>
                            </div>
                            <div class="info-item">
                                <span class="info-label">অবস্থা:</span> 
                                <?php 
                                // Update result status - if any subject has component fail, student fails
                                $component_fail = false;
                                foreach ($marks_data as $mark) {
                                    if ((isset($mark['cq_passed']) && !$mark['cq_passed']) || 
                                        (isset($mark['mcq_passed']) && !$mark['mcq_passed'])) {
                                        $component_fail = true;
                                        break;
                                    }
                                }
                                
                                if ($component_fail || $result_status == 'অকৃতকার্য'): 
                                ?>
                                <span class="fail">অকৃতকার্য</span>
                                <?php else: ?>
                                <span class="pass">কৃতকার্য</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="signature-section">
                        <div class="signature-box">
                            <div class="signature-line">শিক্ষক স্বাক্ষর</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-line">প্রধান শিক্ষক স্বাক্ষর</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-line">অভিভাবক স্বাক্ষর</div>
                        </div>
                    </div>
                </div>
                <?php elseif ($student_id > 0 && $exam_id > 0 && empty($marks_data)): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> নির্বাচিত ছাত্র/ছাত্রীর জন্য কোন নম্বর পাওয়া যায়নি। প্রথমে <a href="marks_entry.php">নম্বর এন্ট্রি</a> করুন।
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Sync exam and class selection
            $('#exam_id').change(function() {
                var classId = $(this).find(':selected').data('class');
                $('#class_id').val(classId);
            });
            
            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
        
        // Function to search students by name or roll number
        function searchStudents(autoSubmit) {
            var searchText = $('#student_search').val().toLowerCase();
            var hasMatches = false;
            var matchCount = 0;
            var lastMatchedOption = null;
            
            if (searchText.length > 0) {
                $('#student_id option').each(function() {
                    var option = $(this);
                    
                    // Skip the first empty option
                    if (option.val() === '') return true;
                    
                    var studentName = (option.data('name') || '').toString().toLowerCase();
                    var studentRoll = (option.data('roll') || '').toString().toLowerCase();
                    
                    // Check if name or roll contains the search text
                    if (studentName.indexOf(searchText) !== -1 || studentRoll.indexOf(searchText) !== -1) {
                        option.show();
                        hasMatches = true;
                        matchCount++;
                        lastMatchedOption = option;
                        
                        // If only one match, select it
                        if (matchCount === 1) {
                            option.prop('selected', true);
                        }
                    } else {
                        option.hide();
                    }
                });
                
                // Show a message if no matches found
                if (!hasMatches) {
                    // If no matches and dropdown is open, close it
                    if ($('#student_id').is(':focus')) {
                        $('#student_id').blur();
                    }
                } else {
                    // Open the dropdown to show matches
                    $('#student_id').focus();
                    
                    // If only one match and autoSubmit flag is true, submit the form
                    if (matchCount === 1 && autoSubmit) {
                        $('form').submit();
                    }
                }
            } else {
                // If search box is empty, show all options
                $('#student_id option').show();
            }
            
            return matchCount;
        }
        
        // Handle Enter key press in search box
        function handleKeyPress(e) {
            if (e.keyCode === 13) {
                e.preventDefault();
                var matchCount = searchStudents(false);
                if (matchCount === 1) {
                    $('form').submit();
                } else if (matchCount > 0) {
                    $('#student_id').focus();
                }
            }
        }
    </script>
</body>
</html> 