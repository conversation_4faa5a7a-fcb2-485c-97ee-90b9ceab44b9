<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student ID and payment IDs
$studentId = isset($_GET['student_id']) ? intval($_GET['student_id']) : 0;
$paymentIds = isset($_GET['payment_ids']) ? $_GET['payment_ids'] : [];

// Convert single payment ID to array
if (!is_array($paymentIds) && !empty($paymentIds)) {
    $paymentIds = [$paymentIds];
}

// Validate inputs
if ($studentId <= 0) {
    echo "অবৈধ শিক্ষার্থী আইডি";
    exit();
}

// Get student information
$studentQuery = "SELECT s.*, c.class_name, d.department_name, ss.session_name
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.id
                LEFT JOIN departments d ON s.department_id = d.id
                LEFT JOIN sessions ss ON s.session_id = ss.id
                WHERE s.id = ?";
$stmt = $conn->prepare($studentQuery);
$stmt->bind_param("i", $studentId);
$stmt->execute();
$studentResult = $stmt->get_result();

if ($studentResult->num_rows === 0) {
    echo "শিক্ষার্থী খুঁজে পাওয়া যায়নি";
    exit();
}

$student = $studentResult->fetch_assoc();

// Get payment information
$paymentQuery = "";
$paymentParams = [];
$paymentTypes = "";

if (!empty($paymentIds)) {
    // Get specific payments
    $placeholders = str_repeat("?,", count($paymentIds) - 1) . "?";
    $paymentQuery = "SELECT fp.*, f.fee_type, f.due_date, f.amount as total_fee_amount
                    FROM fee_payments fp
                    JOIN fees f ON fp.fee_id = f.id
                    WHERE fp.id IN ($placeholders) AND f.student_id = ?
                    ORDER BY fp.payment_date DESC";
    
    $paymentParams = $paymentIds;
    $paymentTypes = str_repeat("i", count($paymentIds));
    $paymentParams[] = $studentId;
    $paymentTypes .= "i";
} else {
    // Get all payments for the student
    $paymentQuery = "SELECT fp.*, f.fee_type, f.due_date, f.amount as total_fee_amount
                    FROM fee_payments fp
                    JOIN fees f ON fp.fee_id = f.id
                    WHERE f.student_id = ?
                    ORDER BY fp.payment_date DESC";
    
    $paymentParams = [$studentId];
    $paymentTypes = "i";
}

$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param($paymentTypes, ...$paymentParams);
$stmt->execute();
$payments = $stmt->get_result();

if ($payments->num_rows === 0) {
    echo "কোন পেমেন্ট রেকর্ড পাওয়া যায়নি";
    exit();
}

// Calculate total amount
$totalAmount = 0;
$paidAmount = 0;
$dueAmount = 0;
$paymentsData = [];

while ($payment = $payments->fetch_assoc()) {
    $totalAmount += $payment['total_fee_amount'];
    $paidAmount += $payment['amount'];
    $paymentsData[] = $payment;
}

// Calculate due amount
$dueAmount = $totalAmount - $paidAmount;

// Determine payment status based on due amount
$paymentStatus = "পরিশোধিত";
$statusClass = "text-success";

if ($dueAmount > 0) {
    $paymentStatus = "বকেয়া";
    $statusClass = "text-danger";
} else if ($dueAmount < 0) {
    $paymentStatus = "অতিরিক্ত পরিশোধিত";
    $statusClass = "text-warning";
}

// Generate receipt number if not provided
$receiptNumber = "REC-" . date('Ymd') . "-" . $studentId . "-" . rand(1000, 9999);

// Get school information
$schoolName = "স্কুল ম্যানেজমেন্ট সিস্টেম";
$schoolAddress = "ঢাকা, বাংলাদেশ";
$schoolPhone = "০১৭১২৩৪৫৬৭৮";
$schoolEmail = "<EMAIL>";
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পেমেন্ট রিসিপ্ট - <?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* A4 size styling */
        @page {
            size: A4;
            margin: 10mm;
        }
        
        body {
            font-family: 'Noto Sans Bengali', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
            font-size: 14px;
        }
        
        .receipt-container {
            width: 210mm;
            min-height: 287mm;
            margin: 0 auto;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            padding: 20mm;
        }
        
        .school-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #3f51b5;
            padding-bottom: 15px;
        }
        
        .school-name {
            font-size: 24px;
            font-weight: bold;
            color: #3f51b5;
            margin-bottom: 5px;
        }
        
        .school-address {
            font-size: 14px;
            color: #555;
            margin-bottom: 5px;
        }
        
        .receipt-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 8px;
            background-color: #e8eaf6;
            color: #3f51b5;
            border-radius: 5px;
        }
        
        .receipt-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .receipt-info-item {
            padding: 5px 0;
        }
        
        .receipt-info-label {
            font-weight: 600;
            color: #555;
        }
        
        .student-info, .payment-info {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #3f51b5;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 5px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        table, th, td {
            border: 1px solid #ddd;
        }
        
        th {
            background-color: #f1f3f9;
            color: #333;
            font-weight: 600;
            text-align: left;
            padding: 10px;
        }
        
        td {
            padding: 10px;
            vertical-align: top;
        }
        
        .total-row {
            font-weight: bold;
            background-color: #f1f3f9;
        }
        
        .footer {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        
        .signature {
            width: 30%;
            text-align: center;
            padding-top: 30px;
            border-top: 1px solid #000;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 90px;
            color: rgba(220, 220, 220, 0.3);
            z-index: 0;
            pointer-events: none;
        }
        
        @media print {
            body {
                background-color: #fff;
            }
            
            .receipt-container {
                box-shadow: none;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
        
        .print-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 999;
        }
        
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .badge-paid {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        
        .badge-partial {
            background-color: #fff3cd;
            color: #664d03;
        }
        
        .badge-due {
            background-color: #f8d7da;
            color: #842029;
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-button no-print" onclick="window.print()">
        <i class="fas fa-print me-1"></i> প্রিন্ট
    </button>
    
    <div class="receipt-container">
        <!-- Watermark -->
        <div class="watermark">রিসিপ্ট</div>
        
        <!-- School Header -->
        <div class="school-header">
            <div class="school-name"><?= htmlspecialchars($schoolName) ?></div>
            <div class="school-address"><?= htmlspecialchars($schoolAddress) ?></div>
            <div class="school-contact">
                <span><i class="fas fa-phone me-1"></i> <?= htmlspecialchars($schoolPhone) ?></span> | 
                <span><i class="fas fa-envelope me-1"></i> <?= htmlspecialchars($schoolEmail) ?></span>
            </div>
        </div>
        
        <!-- Receipt Title -->
        <div class="receipt-title">ফি পেমেন্ট রিসিপ্ট</div>
        
        <!-- Receipt Information -->
        <div class="receipt-info">
            <div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">রিসিপ্ট নং:</span> 
                    <span><?= htmlspecialchars($receiptNumber) ?></span>
                </div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">তারিখ:</span> 
                    <span><?= date('d F, Y') ?></span>
                </div>
            </div>
            <div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">মোট পরিমাণ:</span> 
                    <span>৳ <?= number_format($totalAmount, 2) ?></span>
                </div>
                <div class="receipt-info-item">
                    <span class="receipt-info-label">পেমেন্ট সংখ্যা:</span> 
                    <span><?= count($paymentsData) ?></span>
                </div>
            </div>
        </div>
        
        <!-- Student Information -->
        <div class="student-info">
            <div class="section-title"><i class="fas fa-user-graduate me-1"></i> শিক্ষার্থী তথ্য</div>
            <div class="row">
                <div class="col-md-6">
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">শিক্ষার্থী আইডি:</span> 
                        <span><?= htmlspecialchars($student['student_id']) ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">নাম:</span> 
                        <span><?= htmlspecialchars($student['first_name'] . ' ' . $student['last_name']) ?></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">ক্লাস:</span> 
                        <span><?= htmlspecialchars($student['class_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">বিভাগ:</span> 
                        <span><?= htmlspecialchars($student['department_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                    <div class="receipt-info-item">
                        <span class="receipt-info-label">সেশন:</span> 
                        <span><?= htmlspecialchars($student['session_name'] ?? 'অনির্দিষ্ট') ?></span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Payment Details -->
        <div class="payment-info">
            <div class="section-title"><i class="fas fa-money-bill-wave me-1"></i> পেমেন্ট বিবরণ</div>
            <table class="table">
                <thead>
                    <tr>
                        <th width="5%">#</th>
                        <th width="15%">তারিখ</th>
                        <th width="20%">ফি টাইপ</th>
                        <th width="15%">পেমেন্ট পদ্ধতি</th>
                        <th width="15%">রিসিপ্ট নং</th>
                        <th width="15%">পরিমাণ</th>
                        <th width="15%">নোট</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $counter = 1;
                    foreach($paymentsData as $payment): 
                        $paymentMethod = $payment['payment_method'];
                        $methodDisplay = $paymentMethod;
                        switch($paymentMethod) {
                            case 'cash': $methodDisplay = 'নগদ'; break;
                            case 'bank': $methodDisplay = 'ব্যাংক ট্রান্সফার'; break;
                            case 'bkash': $methodDisplay = 'বিকাশ'; break;
                            case 'nagad': $methodDisplay = 'নগদ (ডিজিটাল)'; break;
                            case 'rocket': $methodDisplay = 'রকেট'; break;
                        }
                    ?>
                    <tr>
                        <td><?= $counter++ ?></td>
                        <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                        <td><?= htmlspecialchars($payment['fee_type']) ?></td>
                        <td><?= htmlspecialchars($methodDisplay) ?></td>
                        <td><?= htmlspecialchars($payment['receipt_no'] ?: '-') ?></td>
                        <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                        <td><?= htmlspecialchars($payment['notes'] ?: '-') ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr class="total-row">
                        <td colspan="5" class="text-end">মোট</td>
                        <td>৳ <?= number_format($paidAmount, 2) ?></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Payment Summary Information -->
        <div class="payment-summary mb-4">
            <div class="row">
                <div class="col-4 text-center">
                    <h5>মোট ফি</h5>
                    <h3>৳ <?= number_format($totalAmount, 2) ?></h3>
                </div>
                <div class="col-4 text-center">
                    <h5>পরিশোধিত</h5>
                    <h3>৳ <?= number_format($paidAmount, 2) ?></h3>
                </div>
                <div class="col-4 text-center">
                    <h5>স্ট্যাটাস</h5>
                    <h3 class="<?= $statusClass ?>">
                        <?= $paymentStatus ?>
                        <?php if ($dueAmount != 0): ?>
                            <small>(৳ <?= number_format(abs($dueAmount), 2) ?>)</small>
                        <?php endif; ?>
                    </h3>
                </div>
            </div>
        </div>
        
        <!-- Amount in Words -->
        <div class="mb-4">
            <strong>কথায়:</strong> 
            <?php
            // Convert number to Bangla words (simplified)
            function numberToWords($number) {
                $words = "";
                if ($number > 0) {
                    $words = "এক হাজার টাকা মাত্র"; // Simplified placeholder
                }
                return $words;
            }
            
            echo numberToWords($totalAmount);
            ?>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="signature">
                শিক্ষার্থীর স্বাক্ষর
            </div>
            <div class="signature">
                অভিভাবকের স্বাক্ষর
            </div>
            <div class="signature">
                অফিসিয়াল স্বাক্ষর
            </div>
        </div>
        
        <div class="mt-4 text-center">
            <small class="text-muted">এই রসিদটি কম্পিউটার দ্বারা জেনারেট করা হয়েছে এবং সিল বা স্বাক্ষর ছাড়াই বৈধ।</small>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 