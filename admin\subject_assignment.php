<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create teacher_subjects table if it doesn't exist
$teacherSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS teacher_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    teacher_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    session_id INT(11) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY(teacher_id, subject_id, session_id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
)";
$conn->query($teacherSubjectsTableQuery);

// Handle assignment addition
if (isset($_POST['assign_subject'])) {
    $teacher_id = $_POST['teacher_id'];
    $subject_id = $_POST['subject_id'];
    $session_id = $_POST['session_id'];
    
    // Validate input
    if (empty($teacher_id) || empty($subject_id) || empty($session_id)) {
        $errorMessage = "শিক্ষক, বিষয় এবং সেশন অবশ্যই নির্বাচন করতে হবে!";
    } else {
        // Check if assignment already exists
        $checkQuery = "SELECT * FROM teacher_subjects WHERE teacher_id = ? AND subject_id = ? AND session_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("iii", $teacher_id, $subject_id, $session_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $errorMessage = "এই শিক্ষককে ইতিমধ্যে এই বিষয় এবং সেশনে বরাদ্দ করা হয়েছে!";
        } else {
            // Insert the new assignment
            $insertQuery = "INSERT INTO teacher_subjects (teacher_id, subject_id, session_id) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("iii", $teacher_id, $subject_id, $session_id);
            
            if ($stmt->execute()) {
                $successMessage = "বিষয় সফলভাবে শিক্ষককে বরাদ্দ করা হয়েছে!";
            } else {
                $errorMessage = "বিষয় বরাদ্দ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
        $stmt->close();
    }
}

// Handle assignment deletion
if (isset($_GET['delete'])) {
    $assignment_id = $_GET['delete'];
    $deleteQuery = "DELETE FROM teacher_subjects WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $assignment_id);
    
    if ($stmt->execute()) {
        $successMessage = "বিষয় বরাদ্দ সফলভাবে মুছে ফেলা হয়েছে!";
    } else {
        $errorMessage = "বিষয় বরাদ্দ মুছতে সমস্যা হয়েছে: " . $conn->error;
    }
    $stmt->close();
}

// Get teachers for dropdown
$teachersQuery = "SELECT id, CONCAT(first_name, ' ', last_name) AS teacher_name FROM teachers ORDER BY first_name, last_name";
$teachers = $conn->query($teachersQuery);

// Get subjects for dropdown
$subjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, d.department_name 
                 FROM subjects s
                 LEFT JOIN departments d ON s.department_id = d.id 
                 WHERE s.is_active = 1
                 ORDER BY d.department_name, s.subject_name";
$subjects = $conn->query($subjectsQuery);

// Get sessions for dropdown
$sessionsQuery = "SELECT id, session_name FROM sessions ORDER BY session_name";
$sessions = $conn->query($sessionsQuery);

// Get departments for filter
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get existing assignments with details
$assignmentsQuery = "SELECT ts.id, 
                   t.first_name, t.last_name, 
                   s.subject_name, s.subject_code, 
                   d.department_name,
                   ss.session_name
                   FROM teacher_subjects ts
                   JOIN teachers t ON ts.teacher_id = t.id
                   JOIN subjects s ON ts.subject_id = s.id
                   JOIN departments d ON s.department_id = d.id
                   JOIN sessions ss ON ts.session_id = ss.id
                   ORDER BY t.first_name, t.last_name, s.subject_name";
$assignments = $conn->query($assignmentsQuery);

// Get teacher's assigned subjects counts
$teacherSubjectCountQuery = "SELECT t.id, CONCAT(t.first_name, ' ', t.last_name) AS teacher_name, 
                            COUNT(ts.id) AS assigned_subjects
                            FROM teachers t
                            LEFT JOIN teacher_subjects ts ON t.id = ts.teacher_id
                            GROUP BY t.id
                            ORDER BY assigned_subjects DESC
                            LIMIT 5";
$teacherSubjectCounts = $conn->query($teacherSubjectCountQuery);

// Get most assigned subjects
$popularSubjectsQuery = "SELECT s.id, s.subject_name, COUNT(ts.id) AS assignment_count
                        FROM subjects s
                        LEFT JOIN teacher_subjects ts ON s.id = ts.subject_id
                        GROUP BY s.id
                        ORDER BY assignment_count DESC
                        LIMIT 5";
$popularSubjects = $conn->query($popularSubjectsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় বরাদ্দকরণ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_dashboard.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় ব্যবস্থাপনা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় বরাদ্দকরণ</h2>
                        <p class="text-muted">শিক্ষকদের বিষয় বরাদ্দ করুন এবং বিদ্যমান বরাদ্দ ব্যবস্থাপনা করুন</p>
                    </div>
                    <div class="col-auto">
                        <a href="subject_dashboard.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>বিষয় ড্যাশবোর্ডে ফিরে যান
                        </a>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <!-- Assignment Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">নতুন বিষয় বরাদ্দকরণ</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="subject_assignment.php">
                                    <div class="mb-3">
                                        <label for="teacher_id" class="form-label">শিক্ষক*</label>
                                        <select class="form-select" id="teacher_id" name="teacher_id" required>
                                            <option value="">শিক্ষক নির্বাচন করুন</option>
                                            <?php if ($teachers && $teachers->num_rows > 0): ?>
                                                <?php while ($teacher = $teachers->fetch_assoc()): ?>
                                                    <option value="<?php echo $teacher['id']; ?>">
                                                        <?php echo $teacher['teacher_name']; ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="subject_id" class="form-label">বিষয়*</label>
                                        <select class="form-select" id="subject_id" name="subject_id" required>
                                            <option value="">বিষয় নির্বাচন করুন</option>
                                            <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                <?php 
                                                $currentDept = '';
                                                while ($subject = $subjects->fetch_assoc()): 
                                                    if ($currentDept != $subject['department_name']) {
                                                        if ($currentDept != '') echo '</optgroup>';
                                                        echo '<optgroup label="' . $subject['department_name'] . '">';
                                                        $currentDept = $subject['department_name'];
                                                    }
                                                ?>
                                                    <option value="<?php echo $subject['id']; ?>">
                                                        <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')'; ?>
                                                    </option>
                                                <?php endwhile; ?>
                                                <?php if ($currentDept != '') echo '</optgroup>'; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="session_id" class="form-label">সেশন*</label>
                                        <select class="form-select" id="session_id" name="session_id" required>
                                            <option value="">সেশন নির্বাচন করুন</option>
                                            <?php if ($sessions && $sessions->num_rows > 0): ?>
                                                <?php while ($session = $sessions->fetch_assoc()): ?>
                                                    <option value="<?php echo $session['id']; ?>">
                                                        <?php echo $session['session_name']; ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    
                                    <button type="submit" name="assign_subject" class="btn btn-primary">বরাদ্দ করুন</button>
                                </form>
                            </div>
                        </div>

                        <!-- Quick Stats -->
                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">পরিসংখ্যান</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6 class="text-muted">সর্বাধিক বিষয় বরাদ্দকৃত শিক্ষক</h6>
                                    <ul class="list-group">
                                        <?php if ($teacherSubjectCounts && $teacherSubjectCounts->num_rows > 0): ?>
                                            <?php while ($teacherCount = $teacherSubjectCounts->fetch_assoc()): ?>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <?php echo $teacherCount['teacher_name']; ?>
                                                    <span class="badge bg-primary rounded-pill"><?php echo $teacherCount['assigned_subjects']; ?></span>
                                                </li>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <li class="list-group-item">কোন বরাদ্দ পাওয়া যায়নি</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h6 class="text-muted">সর্বাধিক বরাদ্দকৃত বিষয়</h6>
                                    <ul class="list-group">
                                        <?php if ($popularSubjects && $popularSubjects->num_rows > 0): ?>
                                            <?php while ($subject = $popularSubjects->fetch_assoc()): ?>
                                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                                    <?php echo $subject['subject_name']; ?>
                                                    <span class="badge bg-success rounded-pill"><?php echo $subject['assignment_count']; ?></span>
                                                </li>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <li class="list-group-item">কোন বরাদ্দ পাওয়া যায়নি</li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Assignments List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বরাদ্দকৃত বিষয়সমূহ</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead>
                                            <tr>
                                                <th>শিক্ষক</th>
                                                <th>বিষয়</th>
                                                <th>বিষয় কোড</th>
                                                <th>বিভাগ</th>
                                                <th>সেশন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($assignments && $assignments->num_rows > 0): ?>
                                                <?php while ($assignment = $assignments->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $assignment['first_name'] . ' ' . $assignment['last_name']; ?></td>
                                                        <td><?php echo $assignment['subject_name']; ?></td>
                                                        <td><?php echo $assignment['subject_code']; ?></td>
                                                        <td><?php echo $assignment['department_name']; ?></td>
                                                        <td><?php echo $assignment['session_name']; ?></td>
                                                        <td>
                                                            <a href="subject_assignment.php?delete=<?php echo $assignment['id']; ?>" 
                                                               class="btn btn-sm btn-danger" 
                                                               onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই বরাদ্দ মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="6" class="text-center">কোন বরাদ্দকৃত বিষয় পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Export Options -->
                                <div class="mt-3">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-file-export me-2"></i>এক্সপোর্ট
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="subject_assignment_export.php?format=pdf">PDF ফরম্যাট</a></li>
                                            <li><a class="dropdown-item" href="subject_assignment_export.php?format=excel">Excel ফরম্যাট</a></li>
                                            <li><a class="dropdown-item" href="subject_assignment_export.php?format=csv">CSV ফরম্যাট</a></li>
                                        </ul>
                                    </div>
                                    
                                    <a href="subject_assignment_print.php" class="btn btn-outline-secondary ms-2" target="_blank">
                                        <i class="fas fa-print me-2"></i>প্রিন্ট
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 