<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Handle category update for subjects
if (isset($_POST['update_categories'])) {
    $subjects = $_POST['subject'] ?? [];
    
    // Begin transaction
    $conn->begin_transaction();
    
    try {
        foreach ($subjects as $subjectId => $data) {
            $category = $data['category'] ?? 'required';
            
            $updateQuery = "UPDATE subjects SET category = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("si", $category, $subjectId);
            $stmt->execute();
        }
        
        // Commit transaction
        $conn->commit();
        $successMessage = "বিষয় ক্যাটাগরি সফলভাবে আপডেট করা হয়েছে!";
    } catch (Exception $e) {
        // Rollback on error
        $conn->rollback();
        $errorMessage = "বিষয় ক্যাটাগরি আপডেট করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
}

// Department filter
$departmentFilter = isset($_GET['department']) ? $_GET['department'] : '';

// Get subjects with department filter
$subjectsQuery = "SELECT s.*, d.department_name 
                 FROM subjects s
                 JOIN subject_departments sd ON s.id = sd.subject_id
                 JOIN departments d ON sd.department_id = d.id
                 WHERE s.is_active = 1";

if (!empty($departmentFilter)) {
    $subjectsQuery .= " AND sd.department_id = " . intval($departmentFilter);
}

$subjectsQuery .= " ORDER BY d.department_name, s.subject_name";
$subjects = $conn->query($subjectsQuery);

// Get student subject selections
$checkTableQuery = "SHOW TABLES LIKE 'student_subjects'";
$tableExists = $conn->query($checkTableQuery)->num_rows > 0;

if ($tableExists) {
    $selectionsQuery = "SELECT 
                        s.first_name, s.last_name, s.student_id, 
                        d.department_name,
                        COUNT(CASE WHEN ss.category = 'required' THEN 1 END) as required_count,
                        COUNT(CASE WHEN ss.category = 'optional' THEN 1 END) as optional_count,
                        COUNT(CASE WHEN ss.category = 'fourth' THEN 1 END) as fourth_count,
                        COUNT(ss.id) as total_subjects
                        FROM students s
                        JOIN departments d ON s.department_id = d.id
                        LEFT JOIN student_subjects ss ON s.id = ss.student_id
                        GROUP BY s.id
                        ORDER BY d.department_name, s.first_name, s.last_name";
    $selections = $conn->query($selectionsQuery);
} else {
    // Display setup message if table doesn't exist
    $selections = null;
    $tableSetupMessage = "বিষয় নির্বাচন টেবিল সেটআপ করা হয়নি। <a href='../database/student_subjects_setup.php' class='btn btn-primary btn-sm'>টেবিল সেটআপ করুন</a>";
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ক্যাটাগরি ব্যবস্থাপনা - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_categories.php">
                            <i class="fas fa-tags me-2"></i> বিষয় ক্যাটাগরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় ক্যাটাগরি ব্যবস্থাপনা</h2>
                        <p class="text-muted">বিষয়গুলির ক্যাটাগরি সেট করুন এবং শিক্ষার্থীদের বিষয় নির্বাচন দেখুন</p>
                    </div>
                </div>

                <?php if (isset($errorMessage)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($successMessage)): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Nav Tabs -->
                <ul class="nav nav-tabs mb-4" id="myTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab" aria-controls="categories" aria-selected="true">
                            <i class="fas fa-tags me-2"></i>বিষয় ক্যাটাগরি ব্যবস্থাপনা
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="selections-tab" data-bs-toggle="tab" data-bs-target="#selections" type="button" role="tab" aria-controls="selections" aria-selected="false">
                            <i class="fas fa-list-check me-2"></i>শিক্ষার্থী বিষয় নির্বাচন
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="myTabContent">
                    <!-- Subject Categories Tab -->
                    <div class="tab-pane fade show active" id="categories" role="tabpanel" aria-labelledby="categories-tab">
                        <!-- Department Filter -->
                        <div class="card mb-4">
                            <div class="card-body">
                                <form method="GET" action="subject_categories.php" class="d-flex">
                                    <select name="department" class="form-select me-2">
                                        <option value="">সকল বিভাগ</option>
                                        <?php if ($departments && $departments->num_rows > 0): ?>
                                            <?php while ($dept = $departments->fetch_assoc()): ?>
                                                <option value="<?php echo $dept['id']; ?>" <?php echo ($departmentFilter == $dept['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $dept['department_name']; ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter me-2"></i>ফিল্টার
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Subject Categories -->
                        <form method="POST" action="subject_categories.php">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">বিষয় ক্যাটাগরি সেটিংস</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>বিষয় কোড</th>
                                                    <th>বিষয়ের নাম</th>
                                                    <th>বিভাগ</th>
                                                    <th>ক্যাটাগরি</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if ($subjects && $subjects->num_rows > 0): ?>
                                                    <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                        <tr>
                                                            <td><?php echo $subject['subject_code']; ?></td>
                                                            <td><?php echo $subject['subject_name']; ?></td>
                                                            <td><?php echo $subject['department_name']; ?></td>
                                                            <td>
                                                                <select name="subject[<?php echo $subject['id']; ?>][category]" class="form-select">
                                                                    <option value="required" <?php echo ($subject['category'] == 'required' || strpos($subject['category'], 'required') !== false) ? 'selected' : ''; ?>>
                                                                        আবশ্যিক
                                                                    </option>
                                                                    <option value="optional" <?php echo ($subject['category'] == 'optional' || strpos($subject['category'], 'optional') !== false) ? 'selected' : ''; ?>>
                                                                        ঐচ্ছিক
                                                                    </option>
                                                                    <option value="fourth" <?php echo ($subject['category'] == 'fourth' || strpos($subject['category'], 'fourth') !== false) ? 'selected' : ''; ?>>
                                                                        ৪র্থ বিষয়
                                                                    </option>
                                                                </select>
                                                            </td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="4" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <button type="submit" name="update_categories" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>ক্যাটাগরি আপডেট করুন
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Category Legend -->
                        <div class="card mt-4">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">বিষয় ক্যাটাগরি গাইডলাইন</h5>
                            </div>
                            <div class="card-body">
                                <p>শিক্ষার্থী বিষয় নির্বাচনের জন্য নিম্নলিখিত নিয়মাবলী অনুসরণ করা হয়:</p>
                                <ul>
                                    <li>প্রতিটি শিক্ষার্থীকে মোট <strong>৭টি</strong> বিষয় নির্বাচন করতে হবে।</li>
                                    <li><strong>আবশ্যিক বিষয়:</strong> প্রতিটি বিভাগের জন্য আবশ্যিক বিষয়সমূহ শিক্ষার্থীকে অবশ্যই নির্বাচন করতে হবে।</li>
                                    <li><strong>ঐচ্ছিক বিষয়:</strong> শিক্ষার্থী তার পছন্দ অনুযায়ী ঐচ্ছিক বিষয় নির্বাচন করতে পারবে।</li>
                                    <li><strong>৪র্থ বিষয়:</strong> শিক্ষার্থী সর্বোচ্চ <strong>একটি</strong> ৪র্থ বিষয় নির্বাচন করতে পারবে।</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Student Selections Tab -->
                    <div class="tab-pane fade" id="selections" role="tabpanel" aria-labelledby="selections-tab">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">শিক্ষার্থীদের বিষয় নির্বাচন</h5>
                            </div>
                            <div class="card-body">
                                <?php if (isset($tableSetupMessage)): ?>
                                    <div class="alert alert-warning">
                                        <?php echo $tableSetupMessage; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>শিক্ষার্থী আইডি</th>
                                                    <th>নাম</th>
                                                    <th>বিভাগ</th>
                                                    <th>আবশ্যিক বিষয়</th>
                                                    <th>ঐচ্ছিক বিষয়</th>
                                                    <th>৪র্থ বিষয়</th>
                                                    <th>মোট বিষয়</th>
                                                    <th>অ্যাকশন</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php if ($selections && $selections->num_rows > 0): ?>
                                                    <?php while ($selection = $selections->fetch_assoc()): ?>
                                                        <tr>
                                                            <td><?php echo $selection['student_id']; ?></td>
                                                            <td><?php echo $selection['first_name'] . ' ' . $selection['last_name']; ?></td>
                                                            <td><?php echo $selection['department_name']; ?></td>
                                                            <td>
                                                                <span class="badge bg-primary"><?php echo $selection['required_count']; ?></span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-info"><?php echo $selection['optional_count']; ?></span>
                                                            </td>
                                                            <td>
                                                                <span class="badge bg-warning"><?php echo $selection['fourth_count']; ?></span>
                                                            </td>
                                                            <td>
                                                                <span class="badge <?php echo ($selection['total_subjects'] == 7) ? 'bg-success' : 'bg-danger'; ?>">
                                                                    <?php echo $selection['total_subjects']; ?>/7
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <a href="view_student_subjects.php?id=<?php echo $selection['student_id']; ?>" class="btn btn-sm btn-info">
                                                                    <i class="fas fa-eye"></i> বিস্তারিত
                                                                </a>
                                                            </td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                <?php else: ?>
                                                    <tr>
                                                        <td colspan="8" class="text-center">কোন শিক্ষার্থী বিষয় নির্বাচন করেনি</td>
                                                    </tr>
                                                <?php endif; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 