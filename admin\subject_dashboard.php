<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get statistics
// Count total subjects
$subjectsCountQuery = "SELECT COUNT(*) as total FROM subjects";
$subjectsResult = $conn->query($subjectsCountQuery);
$totalSubjects = ($subjectsResult) ? $subjectsResult->fetch_assoc()['total'] : 0;

// Count active subjects
$activeSubjectsQuery = "SELECT COUNT(*) as total FROM subjects WHERE is_active = 1";
$activeResult = $conn->query($activeSubjectsQuery);
$activeSubjects = ($activeResult) ? $activeResult->fetch_assoc()['total'] : 0;

// Count subjects by department
$deptSubjectsQuery = "SELECT d.department_name, COUNT(s.id) as total 
                     FROM departments d
                     LEFT JOIN subjects s ON d.id = s.department_id
                     GROUP BY d.id
                     ORDER BY total DESC
                     LIMIT 5";
$deptSubjects = $conn->query($deptSubjectsQuery);

// Get recent subjects
$recentSubjectsQuery = "SELECT s.*, d.department_name 
                       FROM subjects s
                       LEFT JOIN departments d ON s.department_id = d.id
                       ORDER BY s.created_at DESC 
                       LIMIT 5";
$recentSubjects = $conn->query($recentSubjectsQuery);

// Get departments for stats
$departmentsQuery = "SELECT id, department_name FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ব্যবস্থাপনা ড্যাশবোর্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card-hover {
            transition: transform 0.3s, box-shadow 0.3s;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .action-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto 15px;
            font-size: 24px;
        }
        .stat-card {
            border-left: 4px solid;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="staff.php">
                            <i class="fas fa-user-tie me-2"></i> কর্মচারী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="subject_dashboard.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় ব্যবস্থাপনা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-chalkboard me-2"></i> ক্লাস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> রিপোর্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় ব্যবস্থাপনা ড্যাশবোর্ড</h2>
                        <p class="text-muted">বিষয় সংক্রান্ত সকল কার্যক্রম এখান থেকে পরিচালনা করুন</p>
                    </div>
                </div>

                <!-- Statistics Summary -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card h-100" style="border-left-color: #0d6efd;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-primary fw-bold">মোট বিষয়</h6>
                                        <h2 class="mb-0"><?php echo $totalSubjects; ?></h2>
                                    </div>
                                    <div class="text-primary">
                                        <i class="fas fa-book-open fa-2x"></i>
                                    </div>
                                </div>
                                <a href="subjects.php" class="btn btn-sm btn-outline-primary mt-3">বিস্তারিত দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card h-100" style="border-left-color: #198754;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-success fw-bold">সক্রিয় বিষয়</h6>
                                        <h2 class="mb-0"><?php echo $activeSubjects; ?></h2>
                                    </div>
                                    <div class="text-success">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                </div>
                                <a href="subjects.php?filter=active" class="btn btn-sm btn-outline-success mt-3">সক্রিয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card h-100" style="border-left-color: #dc3545;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-danger fw-bold">নিষ্ক্রিয় বিষয়</h6>
                                        <h2 class="mb-0"><?php echo $totalSubjects - $activeSubjects; ?></h2>
                                    </div>
                                    <div class="text-danger">
                                        <i class="fas fa-times-circle fa-2x"></i>
                                    </div>
                                </div>
                                <a href="subjects.php?filter=inactive" class="btn btn-sm btn-outline-danger mt-3">নিষ্ক্রিয় দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card h-100" style="border-left-color: #6f42c1;">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-purple fw-bold">বিভাগ সংখ্যা</h6>
                                        <h2 class="mb-0"><?php echo ($departments) ? $departments->num_rows : 0; ?></h2>
                                    </div>
                                    <div class="text-purple">
                                        <i class="fas fa-building fa-2x"></i>
                                    </div>
                                </div>
                                <a href="departments.php" class="btn btn-sm btn-outline-secondary mt-3">বিভাগ দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">দ্রুত অ্যাকশন</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-4">
                                    <div class="col-md-3 col-sm-6">
                                        <a href="subjects.php" class="text-decoration-none">
                                            <div class="card card-hover text-center h-100">
                                                <div class="card-body">
                                                    <div class="action-icon bg-primary text-white">
                                                        <i class="fas fa-list"></i>
                                                    </div>
                                                    <h5>বিষয় তালিকা</h5>
                                                    <p class="text-muted small">সকল বিষয়ের তালিকা দেখুন</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    
                                    <div class="col-md-3 col-sm-6">
                                        <a href="subjects.php#add-subject" class="text-decoration-none">
                                            <div class="card card-hover text-center h-100">
                                                <div class="card-body">
                                                    <div class="action-icon bg-success text-white">
                                                        <i class="fas fa-plus-circle"></i>
                                                    </div>
                                                    <h5>নতুন বিষয়</h5>
                                                    <p class="text-muted small">নতুন বিষয় যোগ করুন</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    
                                    <div class="col-md-3 col-sm-6">
                                        <a href="subject_assignment.php" class="text-decoration-none">
                                            <div class="card card-hover text-center h-100">
                                                <div class="card-body">
                                                    <div class="action-icon bg-info text-white">
                                                        <i class="fas fa-chalkboard-teacher"></i>
                                                    </div>
                                                    <h5>বিষয় বরাদ্দকরণ</h5>
                                                    <p class="text-muted small">শিক্ষকদের বিষয় বরাদ্দ করুন</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                    
                                    <div class="col-md-3 col-sm-6">
                                        <a href="subject_reports.php" class="text-decoration-none">
                                            <div class="card card-hover text-center h-100">
                                                <div class="card-body">
                                                    <div class="action-icon bg-warning text-dark">
                                                        <i class="fas fa-chart-bar"></i>
                                                    </div>
                                                    <h5>বিষয় রিপোর্ট</h5>
                                                    <p class="text-muted small">বিষয় সংক্রান্ত রিপোর্ট দেখুন</p>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Management Links -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">বিষয় ব্যবস্থাপনা</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="subjects.php" class="text-decoration-none">
                                            <i class="fas fa-list-ul me-2 text-primary"></i> বিষয় তালিকা
                                        </a>
                                        <span class="badge bg-primary rounded-pill"><?php echo $totalSubjects; ?></span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="subject_import.php" class="text-decoration-none">
                                            <i class="fas fa-file-import me-2 text-success"></i> বিষয় ইমপোর্ট (CSV/Excel)
                                        </a>
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-upload"></i>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="subject_export.php" class="text-decoration-none">
                                            <i class="fas fa-file-export me-2 text-info"></i> বিষয় এক্সপোর্ট
                                        </a>
                                        <span class="badge bg-info rounded-pill">
                                            <i class="fas fa-download"></i>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="subject_archive.php" class="text-decoration-none">
                                            <i class="fas fa-archive me-2 text-warning"></i> আর্কাইভ করা বিষয়
                                        </a>
                                        <span class="badge bg-warning rounded-pill">
                                            <i class="fas fa-box-archive"></i>
                                        </span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <a href="subject_categories.php" class="text-decoration-none">
                                            <i class="fas fa-tags me-2 text-danger"></i> বিষয় ক্যাটাগরি ব্যবস্থাপনা
                                        </a>
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-tag"></i>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">সাম্প্রতিক বিষয়</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>বিষয়ের নাম</th>
                                                <th>বিষয় কোড</th>
                                                <th>বিভাগ</th>
                                                <th>অবস্থা</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentSubjects && $recentSubjects->num_rows > 0): ?>
                                                <?php while ($subject = $recentSubjects->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                                        <td><?php echo htmlspecialchars($subject['department_name'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <?php if ($subject['is_active']): ?>
                                                                <span class="badge bg-success">সক্রিয়</span>
                                                            <?php else: ?>
                                                                <span class="badge bg-danger">নিষ্ক্রিয়</span>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন বিষয় পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="subjects.php" class="btn btn-sm btn-success">সকল বিষয় দেখুন</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Department and Related Data -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">বিভাগ অনুযায়ী বিষয়</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>বিভাগের নাম</th>
                                                <th>বিষয় সংখ্যা</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($deptSubjects && $deptSubjects->num_rows > 0): ?>
                                                <?php while ($dept = $deptSubjects->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($dept['department_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($dept['total']); ?></td>
                                                        <td>
                                                            <a href="subjects.php?department=<?php echo urlencode($dept['department_name']); ?>" class="btn btn-sm btn-info">
                                                                <i class="fas fa-eye"></i> দেখুন
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="3" class="text-center">কোন বিভাগ পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-3">
                                    <a href="departments.php" class="btn btn-sm btn-info">সকল বিভাগ দেখুন</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">বিষয় সংক্রান্ত পরিসংখ্যান</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6 class="text-muted">বিষয়ের ধরণ</h6>
                                    <div class="progress" style="height: 25px;">
                                        <?php if ($totalSubjects > 0): ?>
                                            <?php $activePercent = round(($activeSubjects / $totalSubjects) * 100); ?>
                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $activePercent; ?>%;" aria-valuenow="<?php echo $activePercent; ?>" aria-valuemin="0" aria-valuemax="100">
                                                সক্রিয় (<?php echo $activePercent; ?>%)
                                            </div>
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo 100 - $activePercent; ?>%;" aria-valuenow="<?php echo 100 - $activePercent; ?>" aria-valuemin="0" aria-valuemax="100">
                                                নিষ্ক্রিয় (<?php echo 100 - $activePercent; ?>%)
                                            </div>
                                        <?php else: ?>
                                            <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <div>
                                    <h6 class="text-muted mb-3">দ্রুত অ্যাকশন</h6>
                                    <div class="d-grid gap-2">
                                        <a href="subject_bulk_activate.php" class="btn btn-outline-success btn-sm">
                                            <i class="fas fa-check-circle me-2"></i> সকল বিষয় সক্রিয় করুন
                                        </a>
                                        <a href="subject_bulk_deactivate.php" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-times-circle me-2"></i> সকল বিষয় নিষ্ক্রিয় করুন
                                        </a>
                                        <a href="subject_cleanup.php" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-broom me-2"></i> বিষয় তথ্য রিফ্রেশ করুন
                                        </a>
                                        <a href="subject_reports.php" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-file-pdf me-2"></i> বিষয় রিপোর্ট ডাউনলোড করুন
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 