<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get export parameters
$format = isset($_GET['format']) ? $_GET['format'] : 'pdf';
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'all_subjects';
$department_id = isset($_GET['department_id']) ? $_GET['department_id'] : '';
$session_id = isset($_GET['session_id']) ? $_GET['session_id'] : '';

// Build the base query for subjects
$queryBase = "SELECT s.id, s.subject_name, s.subject_code, s.description, s.is_active, 
              d.department_name, COUNT(ts.id) as teacher_count 
              FROM subjects s
              LEFT JOIN departments d ON s.department_id = d.id
              LEFT JOIN teacher_subjects ts ON s.id = ts.subject_id";

$queryWhere = " WHERE 1=1";
$queryGroup = " GROUP BY s.id";
$queryOrder = " ORDER BY d.department_name, s.subject_name";

// Add filters based on selected options
if (!empty($department_id)) {
    $queryWhere .= " AND s.department_id = $department_id";
}

if (!empty($session_id) && $report_type == 'assigned_subjects') {
    $queryWhere .= " AND ts.session_id = $session_id";
}

// Add specific conditions based on report type
switch ($report_type) {
    case 'active_subjects':
        $queryWhere .= " AND s.is_active = 1";
        $report_title = "সক্রিয় বিষয়ের তালিকা";
        break;
    case 'inactive_subjects':
        $queryWhere .= " AND s.is_active = 0";
        $report_title = "নিষ্ক্রিয় বিষয়ের তালিকা";
        break;
    case 'assigned_subjects':
        $queryWhere .= " AND ts.id IS NOT NULL";
        $report_title = "বরাদ্দকৃত বিষয়ের তালিকা";
        break;
    case 'unassigned_subjects':
        $queryWhere .= " AND ts.id IS NULL";
        $report_title = "অবরাদ্দকৃত বিষয়ের তালিকা";
        break;
    default:
        $report_title = "সকল বিষয়ের তালিকা";
}

// Combine the query parts
$query = $queryBase . $queryWhere . $queryGroup . $queryOrder;

// Execute the query
$result = $conn->query($query);

// Get department name if filtered
$department_name = "সকল বিভাগ";
if (!empty($department_id)) {
    $dept_query = "SELECT department_name FROM departments WHERE id = $department_id";
    $dept_result = $conn->query($dept_query);
    if ($dept_result && $dept_result->num_rows > 0) {
        $department_name = $dept_result->fetch_assoc()['department_name'];
    }
}

// Get session name if filtered
$session_name = "সকল সেশন";
if (!empty($session_id)) {
    $session_query = "SELECT session_name FROM sessions WHERE id = $session_id";
    $session_result = $conn->query($session_query);
    if ($session_result && $session_result->num_rows > 0) {
        $session_name = $session_result->fetch_assoc()['session_name'];
    }
}

// Generate filename based on report type
$filename = 'subject_report_' . $report_type . '_' . date('Y-m-d_H-i-s');

// Collect data for export
$data = [];
if ($result && $result->num_rows > 0) {
    $counter = 1;
    while ($subject = $result->fetch_assoc()) {
        $data[] = [
            'sl' => $counter++,
            'subject_name' => $subject['subject_name'],
            'subject_code' => $subject['subject_code'],
            'department_name' => $subject['department_name'],
            'description' => $subject['description'] ? mb_substr($subject['description'], 0, 30) . '...' : 'N/A',
            'teacher_count' => $subject['teacher_count'],
            'status' => $subject['is_active'] == 1 ? 'সক্রিয়' : 'নিষ্ক্রিয়'
        ];
    }
}

// Export based on requested format
switch ($format) {
    case 'csv':
        exportCSV($data, $filename, $report_title, $department_name, $session_name);
        break;
    case 'excel':
        exportExcel($data, $filename, $report_title, $department_name, $session_name);
        break;
    case 'pdf':
    default:
        exportPDF($data, $filename, $report_title, $department_name, $session_name);
}

// Function to export as CSV
function exportCSV($data, $filename, $report_title, $department_name, $session_name) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    
    // Create file pointer connected to output stream
    $output = fopen('php://output', 'w');
    
    // Add BOM to fix Excel UTF-8 display issues
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Add report header
    fputcsv($output, ["বিষয় রিপোর্ট"]);
    fputcsv($output, ["রিপোর্টের ধরন: " . $report_title]);
    fputcsv($output, ["বিভাগ: " . $department_name]);
    fputcsv($output, ["সেশন: " . $session_name]);
    fputcsv($output, ["তারিখ: " . date('d-m-Y')]);
    fputcsv($output, []);
    
    // Add column headers
    fputcsv($output, ["#", "বিষয়ের নাম", "বিষয় কোড", "বিভাগ", "বিবরণ", "বরাদ্দকৃত শিক্ষক", "স্ট্যাটাস"]);
    
    // Add data rows
    foreach ($data as $row) {
        fputcsv($output, [
            $row['sl'],
            $row['subject_name'],
            $row['subject_code'],
            $row['department_name'],
            $row['description'],
            $row['teacher_count'],
            $row['status']
        ]);
    }
    
    fclose($output);
    exit;
}

// Function to export as Excel
function exportExcel($data, $filename, $report_title, $department_name, $session_name) {
    // If PHPSpreadsheet is not available, fallback to CSV
    if (!class_exists('PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        exportCSV($data, $filename, $report_title, $department_name, $session_name);
        exit;
    }
    
    require_once '../vendor/autoload.php';
    
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator('কলেজ ম্যানেজমেন্ট সিস্টেম')
        ->setLastModifiedBy('কলেজ ম্যানেজমেন্ট সিস্টেম')
        ->setTitle('বিষয় রিপোর্ট')
        ->setSubject('বিষয় রিপোর্ট')
        ->setDescription('বিষয় সংক্রান্ত রিপোর্ট');

    // Add report header
    $sheet->setCellValue('A1', 'বিষয় রিপোর্ট');
    $sheet->setCellValue('A2', 'রিপোর্টের ধরন: ' . $report_title);
    $sheet->setCellValue('A3', 'বিভাগ: ' . $department_name);
    $sheet->setCellValue('A4', 'সেশন: ' . $session_name);
    $sheet->setCellValue('A5', 'তারিখ: ' . date('d-m-Y'));
    
    // Merge header cells
    $sheet->mergeCells('A1:G1');
    $sheet->mergeCells('A2:G2');
    $sheet->mergeCells('A3:G3');
    $sheet->mergeCells('A4:G4');
    $sheet->mergeCells('A5:G5');
    
    // Style header
    $headerStyle = $sheet->getStyle('A1:G5');
    $headerStyle->getFont()->setBold(true);
    $headerStyle->getFont()->setSize(14);
    $headerStyle->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    
    // Add column headers in row 7
    $sheet->setCellValue('A7', '#');
    $sheet->setCellValue('B7', 'বিষয়ের নাম');
    $sheet->setCellValue('C7', 'বিষয় কোড');
    $sheet->setCellValue('D7', 'বিভাগ');
    $sheet->setCellValue('E7', 'বিবরণ');
    $sheet->setCellValue('F7', 'বরাদ্দকৃত শিক্ষক');
    $sheet->setCellValue('G7', 'স্ট্যাটাস');
    
    // Style column headers
    $columnHeaderStyle = $sheet->getStyle('A7:G7');
    $columnHeaderStyle->getFont()->setBold(true);
    $columnHeaderStyle->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
    $columnHeaderStyle->getFill()->getStartColor()->setRGB('DDDDDD');
    
    // Add data rows
    $row = 8;
    foreach ($data as $item) {
        $sheet->setCellValue('A' . $row, $item['sl']);
        $sheet->setCellValue('B' . $row, $item['subject_name']);
        $sheet->setCellValue('C' . $row, $item['subject_code']);
        $sheet->setCellValue('D' . $row, $item['department_name']);
        $sheet->setCellValue('E' . $row, $item['description']);
        $sheet->setCellValue('F' . $row, $item['teacher_count']);
        $sheet->setCellValue('G' . $row, $item['status']);
        $row++;
    }
    
    // Auto size columns
    foreach (range('A', 'G') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // Create Excel file
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    
    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    // Save to PHP output
    $writer->save('php://output');
    exit;
}

// Function to export as PDF
function exportPDF($data, $filename, $report_title, $department_name, $session_name) {
    // If TCPDF is not available, fallback to CSV
    if (!class_exists('TCPDF')) {
        exportCSV($data, $filename, $report_title, $department_name, $session_name);
        exit;
    }
    
    require_once '../vendor/autoload.php';
    
    // Create new PDF document
    $pdf = new TCPDF('L', 'mm', 'A4', true, 'UTF-8', false);
    
    // Set document information
    $pdf->SetCreator('কলেজ ম্যানেজমেন্ট সিস্টেম');
    $pdf->SetAuthor('কলেজ ম্যানেজমেন্ট সিস্টেম');
    $pdf->SetTitle('বিষয় রিপোর্ট');
    $pdf->SetSubject('বিষয় রিপোর্ট');
    
    // Set header and footer fonts
    $pdf->setHeaderFont(Array('times', '', 10));
    $pdf->setFooterFont(Array('times', '', 8));
    
    // Remove default header/footer
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(true);
    
    // Set default monospaced font
    $pdf->SetDefaultMonospacedFont('courier');
    
    // Set margins
    $pdf->SetMargins(15, 15, 15);
    
    // Set auto page breaks
    $pdf->SetAutoPageBreak(true, 15);
    
    // Set image scale factor
    $pdf->setImageScale(1.25);
    
    // Set font
    $pdf->SetFont('times', '', 10);
    
    // Add a page
    $pdf->AddPage();
    
    // Report header
    $pdf->SetFont('times', 'B', 16);
    $pdf->Cell(0, 10, 'বিষয় রিপোর্ট', 0, 1, 'C');
    
    $pdf->SetFont('times', 'B', 12);
    $pdf->Cell(0, 7, 'রিপোর্টের ধরন: ' . $report_title, 0, 1, 'C');
    $pdf->Cell(0, 7, 'বিভাগ: ' . $department_name, 0, 1, 'C');
    $pdf->Cell(0, 7, 'সেশন: ' . $session_name, 0, 1, 'C');
    $pdf->Cell(0, 7, 'তারিখ: ' . date('d-m-Y'), 0, 1, 'C');
    
    $pdf->Ln(5);
    
    // Table header
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetTextColor(0);
    $pdf->SetFont('times', 'B', 10);
    
    // Table header width
    $w = array(15, 45, 30, 40, 60, 35, 30);
    
    // Header
    $pdf->Cell($w[0], 7, '#', 1, 0, 'C', true);
    $pdf->Cell($w[1], 7, 'বিষয়ের নাম', 1, 0, 'C', true);
    $pdf->Cell($w[2], 7, 'বিষয় কোড', 1, 0, 'C', true);
    $pdf->Cell($w[3], 7, 'বিভাগ', 1, 0, 'C', true);
    $pdf->Cell($w[4], 7, 'বিবরণ', 1, 0, 'C', true);
    $pdf->Cell($w[5], 7, 'বরাদ্দকৃত শিক্ষক', 1, 0, 'C', true);
    $pdf->Cell($w[6], 7, 'স্ট্যাটাস', 1, 1, 'C', true);
    
    // Data
    $pdf->SetFont('times', '', 10);
    $pdf->SetFillColor(255, 255, 255);
    
    foreach ($data as $row) {
        $pdf->Cell($w[0], 6, $row['sl'], 1, 0, 'C');
        $pdf->Cell($w[1], 6, $row['subject_name'], 1, 0, 'L');
        $pdf->Cell($w[2], 6, $row['subject_code'], 1, 0, 'C');
        $pdf->Cell($w[3], 6, $row['department_name'], 1, 0, 'L');
        $pdf->Cell($w[4], 6, $row['description'], 1, 0, 'L');
        $pdf->Cell($w[5], 6, $row['teacher_count'], 1, 0, 'C');
        
        // Status with color
        if ($row['status'] === 'সক্রিয়') {
            $pdf->SetTextColor(0, 128, 0); // Green for active
        } else {
            $pdf->SetTextColor(255, 128, 0); // Orange for inactive
        }
        $pdf->Cell($w[6], 6, $row['status'], 1, 1, 'C');
        $pdf->SetTextColor(0); // Reset to black
    }
    
    // Close and output PDF
    $pdf->Output($filename . '.pdf', 'D');
    exit;
}
?> 