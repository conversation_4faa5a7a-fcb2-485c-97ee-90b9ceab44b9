<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create marks_distribution table if it doesn't exist
$marksDistTableQuery = "CREATE TABLE IF NOT EXISTS marks_distribution (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    distribution_type VARCHAR(100) NOT NULL,
    marks_percentage DECIMAL(5,2) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
)";
$conn->query($marksDistTableQuery);

// Handle form submission for adding/updating marks distribution
$successMessage = '';
$errorMessage = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_distribution'])) {
        $subjectId = $_POST['subject_id'];
        $distributionType = $_POST['distribution_type'];
        $marksPercentage = $_POST['marks_percentage'];
        
        // Check if total percentage will not exceed 100%
        $checkQuery = "SELECT SUM(marks_percentage) as total FROM marks_distribution WHERE subject_id = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("i", $subjectId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $currentTotal = $row['total'] ?? 0;
        
        if (($currentTotal + $marksPercentage) > 100) {
            $errorMessage = "মোট মার্কস পারসেন্টেজ ১০০% এর বেশি হবে। বর্তমানে এই বিষয়ের জন্য $currentTotal% বরাদ্দ আছে।";
        } else {
            // Insert new marks distribution
            $insertQuery = "INSERT INTO marks_distribution (subject_id, distribution_type, marks_percentage) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("isd", $subjectId, $distributionType, $marksPercentage);
            
            if ($stmt->execute()) {
                $successMessage = "মার্কস ডিস্ট্রিবিউশন সফলভাবে যোগ করা হয়েছে!";
            } else {
                $errorMessage = "মার্কস ডিস্ট্রিবিউশন যোগ করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    } elseif (isset($_POST['update_distribution'])) {
        $distributionId = $_POST['distribution_id'];
        $distributionType = $_POST['distribution_type'];
        $marksPercentage = $_POST['marks_percentage'];
        $subjectId = $_POST['subject_id'];
        
        // Get current percentage
        $currentQuery = "SELECT marks_percentage FROM marks_distribution WHERE id = ?";
        $stmt = $conn->prepare($currentQuery);
        $stmt->bind_param("i", $distributionId);
        $stmt->execute();
        $result = $stmt->get_result();
        $current = $result->fetch_assoc();
        $currentPercentage = $current['marks_percentage'];
        
        // Check if total percentage will not exceed 100%
        $checkQuery = "SELECT SUM(marks_percentage) as total FROM marks_distribution WHERE subject_id = ? AND id != ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ii", $subjectId, $distributionId);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        $otherTotal = $row['total'] ?? 0;
        
        if (($otherTotal + $marksPercentage) > 100) {
            $errorMessage = "মোট মার্কস পারসেন্টেজ ১০০% এর বেশি হবে।";
        } else {
            // Update marks distribution
            $updateQuery = "UPDATE marks_distribution SET distribution_type = ?, marks_percentage = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("sdi", $distributionType, $marksPercentage, $distributionId);
            
            if ($stmt->execute()) {
                $successMessage = "মার্কস ডিস্ট্রিবিউশন সফলভাবে আপডেট করা হয়েছে!";
            } else {
                $errorMessage = "মার্কস ডিস্ট্রিবিউশন আপডেট করতে সমস্যা হয়েছে: " . $conn->error;
            }
        }
    } elseif (isset($_POST['delete_distribution'])) {
        $distributionId = $_POST['distribution_id'];
        
        // Delete marks distribution
        $deleteQuery = "DELETE FROM marks_distribution WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $distributionId);
        
        if ($stmt->execute()) {
            $successMessage = "মার্কস ডিস্ট্রিবিউশন সফলভাবে মুছে ফেলা হয়েছে!";
        } else {
            $errorMessage = "মার্কস ডিস্ট্রিবিউশন মুছতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Get subjects
$subjectsQuery = "SELECT s.id, s.subject_name, s.subject_code, d.department_name 
                 FROM subjects s
                 LEFT JOIN departments d ON s.department_id = d.id
                 ORDER BY s.subject_name";
$subjects = $conn->query($subjectsQuery);

// Get subject ID from URL
$selectedSubjectId = isset($_GET['subject_id']) ? $_GET['subject_id'] : '';

// Get departments for filter
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);

// Get distributions for selected subject
$distributions = null;
if (!empty($selectedSubjectId)) {
    $distsQuery = "SELECT md.*, s.subject_name, s.subject_code 
                  FROM marks_distribution md
                  JOIN subjects s ON md.subject_id = s.id
                  WHERE md.subject_id = ?
                  ORDER BY md.distribution_type";
    $stmt = $conn->prepare($distsQuery);
    $stmt->bind_param("i", $selectedSubjectId);
    $stmt->execute();
    $distributions = $stmt->get_result();
    
    // Get subject information
    $subjectInfoQuery = "SELECT s.subject_name, s.subject_code, d.department_name 
                       FROM subjects s
                       LEFT JOIN departments d ON s.department_id = d.id
                       WHERE s.id = ?";
    $stmt = $conn->prepare($subjectInfoQuery);
    $stmt->bind_param("i", $selectedSubjectId);
    $stmt->execute();
    $subjectResult = $stmt->get_result();
    $subjectInfo = $subjectResult->fetch_assoc();
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় মার্কস ডিস্ট্রিবিউশন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .progress {
            height: 20px;
        }
        .progress-bar {
            transition: width 0.5s;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="departments.php">
                            <i class="fas fa-building me-2"></i> বিভাগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book-open me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link active" href="subject_marks_distribution.php">
                            <i class="fas fa-percentage me-2"></i> মার্কস ডিস্ট্রিবিউশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exam_management.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা ব্যবস্থাপনা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="result_management.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল ব্যবস্থাপনা
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="sessions.php">
                            <i class="fas fa-calendar-alt me-2"></i> সেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col">
                        <h2>বিষয় মার্কস ডিস্ট্রিবিউশন</h2>
                        <p class="text-muted">প্রতিটি বিষয়ের জন্য পরীক্ষার মার্কস ডিস্ট্রিবিউশন সেট করুন</p>
                    </div>
                </div>

                <?php if ($successMessage): ?>
                    <div class="alert alert-success" role="alert">
                        <?php echo $successMessage; ?>
                    </div>
                <?php endif; ?>

                <?php if ($errorMessage): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo $errorMessage; ?>
                    </div>
                <?php endif; ?>

                <!-- Subject Selection -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">বিষয় নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="subject_marks_distribution.php">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="subject" class="form-label">বিষয়</label>
                                    <select name="subject_id" id="subject" class="form-select" required>
                                        <option value="">বিষয় নির্বাচন করুন</option>
                                        <?php if ($subjects && $subjects->num_rows > 0): ?>
                                            <?php while ($subject = $subjects->fetch_assoc()): ?>
                                                <option value="<?php echo $subject['id']; ?>" <?php echo ($selectedSubjectId == $subject['id']) ? 'selected' : ''; ?>>
                                                    <?php echo $subject['subject_name'] . ' (' . $subject['subject_code'] . ')' . (($subject['department_name']) ? ' - ' . $subject['department_name'] : ''); ?>
                                                </option>
                                            <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                <div class="col-md-6 d-flex align-items-end mb-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-2"></i>মার্কস ডিস্ট্রিবিউশন দেখুন
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <?php if (!empty($selectedSubjectId)): ?>
                    <!-- Marks Distribution Section -->
                    <div class="row">
                        <div class="col-md-5">
                            <!-- Add/Edit Distribution Form -->
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h5 class="mb-0">মার্কস ডিস্ট্রিবিউশন যোগ করুন</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="">
                                        <input type="hidden" name="subject_id" value="<?php echo $selectedSubjectId; ?>">
                                        <div class="mb-3">
                                            <label for="distribution_type" class="form-label">ডিস্ট্রিবিউশন টাইপ</label>
                                            <input type="text" class="form-control" id="distribution_type" name="distribution_type" placeholder="উদাহরণ: মিডটার্ম, ফাইনাল, এ্যাসাইনমেন্ট" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="marks_percentage" class="form-label">মার্কস পারসেন্টেজ (%)</label>
                                            <input type="number" class="form-control" id="marks_percentage" name="marks_percentage" min="1" max="100" required>
                                        </div>
                                        <button type="submit" name="add_distribution" class="btn btn-success">
                                            <i class="fas fa-plus-circle me-2"></i>যোগ করুন
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-7">
                            <!-- Display Current Distributions -->
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <?php echo $subjectInfo['subject_name'] . ' (' . $subjectInfo['subject_code'] . ')'; ?> - মার্কস ডিস্ট্রিবিউশন
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <!-- Progress bar for total distribution -->
                                    <?php
                                    $totalPercentage = 0;
                                    if ($distributions && $distributions->num_rows > 0) {
                                        $distributions->data_seek(0);
                                        while ($dist = $distributions->fetch_assoc()) {
                                            $totalPercentage += $dist['marks_percentage'];
                                        }
                                        // Reset pointer
                                        $distributions->data_seek(0);
                                    }
                                    
                                    $progressClass = 'bg-success';
                                    if ($totalPercentage > 100) {
                                        $progressClass = 'bg-danger';
                                    } elseif ($totalPercentage < 100) {
                                        $progressClass = 'bg-warning';
                                    }
                                    ?>
                                    <div class="mb-3">
                                        <label class="form-label">মোট ডিস্ট্রিবিউশন: <?php echo $totalPercentage; ?>%</label>
                                        <div class="progress">
                                            <div class="progress-bar <?php echo $progressClass; ?>" role="progressbar" style="width: <?php echo min($totalPercentage, 100); ?>%" aria-valuenow="<?php echo $totalPercentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                <?php echo $totalPercentage; ?>%
                                            </div>
                                        </div>
                                        <?php if ($totalPercentage > 100): ?>
                                            <div class="text-danger mt-1">
                                                <i class="fas fa-exclamation-triangle me-1"></i>মোট ১০০% এর বেশি হয়েছে!
                                            </div>
                                        <?php elseif ($totalPercentage < 100): ?>
                                            <div class="text-warning mt-1">
                                                <i class="fas fa-exclamation-circle me-1"></i>মোট ১০০% হওয়া প্রয়োজন, বর্তমানে <?php echo (100 - $totalPercentage); ?>% কম।
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                
                                    <?php if ($distributions && $distributions->num_rows > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>ডিস্ট্রিবিউশন টাইপ</th>
                                                        <th>মার্কস (%)</th>
                                                        <th>একশন</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php while ($dist = $distributions->fetch_assoc()): ?>
                                                        <tr>
                                                            <td><?php echo htmlspecialchars($dist['distribution_type']); ?></td>
                                                            <td>
                                                                <div class="progress">
                                                                    <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $dist['marks_percentage']; ?>%" aria-valuenow="<?php echo $dist['marks_percentage']; ?>" aria-valuemin="0" aria-valuemax="100">
                                                                        <?php echo $dist['marks_percentage']; ?>%
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>
                                                                <button type="button" class="btn btn-sm btn-warning edit-dist" 
                                                                    data-id="<?php echo $dist['id']; ?>"
                                                                    data-type="<?php echo htmlspecialchars($dist['distribution_type']); ?>"
                                                                    data-percentage="<?php echo $dist['marks_percentage']; ?>"
                                                                    data-bs-toggle="modal" data-bs-target="#editDistModal">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <form method="POST" action="" class="d-inline">
                                                                    <input type="hidden" name="distribution_id" value="<?php echo $dist['id']; ?>">
                                                                    <button type="submit" name="delete_distribution" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই মার্কস ডিস্ট্রিবিউশন মুছতে চান?')">
                                                                        <i class="fas fa-trash"></i>
                                                                    </button>
                                                                </form>
                                                            </td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>এই বিষয়ের জন্য কোন মার্কস ডিস্ট্রিবিউশন নেই। উপরের ফর্ম ব্যবহার করে নতুন ডিস্ট্রিবিউশন যোগ করুন।
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>মার্কস ডিস্ট্রিবিউশন দেখতে একটি বিষয় নির্বাচন করুন।
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Edit Distribution Modal -->
    <div class="modal fade" id="editDistModal" tabindex="-1" aria-labelledby="editDistModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="editDistModalLabel">মার্কস ডিস্ট্রিবিউশন সম্পাদনা করুন</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="POST" action="">
                    <div class="modal-body">
                        <input type="hidden" name="distribution_id" id="edit_distribution_id">
                        <input type="hidden" name="subject_id" value="<?php echo $selectedSubjectId; ?>">
                        <div class="mb-3">
                            <label for="edit_distribution_type" class="form-label">ডিস্ট্রিবিউশন টাইপ</label>
                            <input type="text" class="form-control" id="edit_distribution_type" name="distribution_type" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_marks_percentage" class="form-label">মার্কস পারসেন্টেজ (%)</label>
                            <input type="number" class="form-control" id="edit_marks_percentage" name="marks_percentage" min="1" max="100" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                        <button type="submit" name="update_distribution" class="btn btn-warning">আপডেট করুন</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Edit distribution button click handler
            const editButtons = document.querySelectorAll('.edit-dist');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const type = this.getAttribute('data-type');
                    const percentage = this.getAttribute('data-percentage');
                    
                    document.getElementById('edit_distribution_id').value = id;
                    document.getElementById('edit_distribution_type').value = type;
                    document.getElementById('edit_marks_percentage').value = percentage;
                });
            });
        });
    </script>
</body>
</html> 