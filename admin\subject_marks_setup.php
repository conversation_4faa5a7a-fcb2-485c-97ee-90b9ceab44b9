<?php
session_start();
require_once('includes/db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Create subject_marks_distribution table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS subject_marks_distribution (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    cq_marks FLOAT DEFAULT 70,
    mcq_marks FLOAT DEFAULT 30,
    practical_marks FLOAT DEFAULT 0,
    total_marks FLOAT DEFAULT 100,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id),
    INDEX (subject_id)
)";

if (!mysqli_query($conn, $create_table_sql)) {
    $_SESSION['error'] = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn);
}

// Process form submission
$success_message = '';
$error_message = '';

if (isset($_POST['setup_marks'])) {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    try {
        // Get all active subjects
        $subjects_sql = "SELECT * FROM subjects WHERE is_active = 1";
        $subjects_result = mysqli_query($conn, $subjects_sql);
        
        if ($subjects_result && mysqli_num_rows($subjects_result) > 0) {
            // Delete existing entries first (optional - depends on your requirements)
            $delete_sql = "DELETE FROM subject_marks_distribution";
            mysqli_query($conn, $delete_sql);
            
            // Insert default marks for each subject
            $insert_sql = "INSERT INTO subject_marks_distribution 
                          (subject_id, cq_marks, mcq_marks, practical_marks, total_marks) 
                          VALUES (?, ?, ?, ?, ?)";
            $stmt = mysqli_prepare($conn, $insert_sql);
            
            while ($subject = mysqli_fetch_assoc($subjects_result)) {
                $subject_id = $subject['id'];
                
                // Default values (you can customize these as needed)
                $cq_marks = isset($_POST['cq_marks']) ? floatval($_POST['cq_marks']) : 70;
                $mcq_marks = isset($_POST['mcq_marks']) ? floatval($_POST['mcq_marks']) : 30;
                $practical_marks = isset($_POST['practical_marks']) ? floatval($_POST['practical_marks']) : 0;
                $total_marks = $cq_marks + $mcq_marks + $practical_marks;
                
                mysqli_stmt_bind_param($stmt, "idddd", $subject_id, $cq_marks, $mcq_marks, $practical_marks, $total_marks);
                mysqli_stmt_execute($stmt);
            }
            
            $success_message = "সকল বিষয়ের জন্য মার্কস সেটআপ সফলভাবে সম্পন্ন হয়েছে!";
        } else {
            $error_message = "কোন সক্রিয় বিষয় পাওয়া যায়নি।";
        }
        
        // Commit transaction
        mysqli_commit($conn);
        $_SESSION['success'] = $success_message;
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $_SESSION['error'] = "মার্কস সেটআপ করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
    
    // Redirect to the minimum marks configuration page
    header("Location: subject_minimum_pass.php");
    exit();
}

// Count how many subjects don't have marks configuration
$missing_config_sql = "SELECT COUNT(*) as missing_count 
                      FROM subjects s 
                      LEFT JOIN subject_marks_distribution smd ON s.id = smd.subject_id 
                      WHERE s.is_active = 1 AND smd.id IS NULL";
$missing_result = mysqli_query($conn, $missing_config_sql);
$missing_count = 0;

if ($missing_result) {
    $missing_row = mysqli_fetch_assoc($missing_result);
    $missing_count = $missing_row['missing_count'];
}

// Include header
$page_title = "বিষয় মার্কস সেটআপ";
include('includes/header.php');
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">ড্যাশবোর্ড</a></li>
        <li class="breadcrumb-item"><a href="subject_minimum_pass.php">ন্যূনতম পাস নম্বর কনফিগারেশন</a></li>
        <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
    </ol>
    
    <!-- Messages Section -->
    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['success']; 
        unset($_SESSION['success']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['error']; 
        unset($_SESSION['error']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-cogs me-1"></i>
            বিষয় মার্কস সেটআপ
        </div>
        <div class="card-body">
            <?php if ($missing_count > 0): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <?php echo $missing_count; ?>টি বিষয়ের জন্য মার্কস ডিস্ট্রিবিউশন সেটআপ করা হয়নি।
                </div>
            <?php else: ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    সমস্ত বিষয়ের জন্য মার্কস ডিস্ট্রিবিউশন সেটআপ করা হয়েছে।
                </div>
            <?php endif; ?>
            
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-12 mb-4">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>সতর্কতা:</strong> এই অপারেশনটি সমস্ত বিষয়ের জন্য ডিফল্ট মার্কস সেটআপ করবে। আপনি কি নিশ্চিত যে আপনি এগিয়ে যেতে চান?
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="cq_marks" class="form-label">ডিফল্ট সিকিউ মার্কস</label>
                        <input type="number" class="form-control" id="cq_marks" name="cq_marks" value="70" min="0" max="100" step="0.01">
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="mcq_marks" class="form-label">ডিফল্ট এমসিকিউ মার্কস</label>
                        <input type="number" class="form-control" id="mcq_marks" name="mcq_marks" value="30" min="0" max="100" step="0.01">
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="practical_marks" class="form-label">ডিফল্ট প্র্যাকটিক্যাল মার্কস</label>
                        <input type="number" class="form-control" id="practical_marks" name="practical_marks" value="0" min="0" max="100" step="0.01">
                    </div>
                </div>
                
                <div class="form-text mb-3">
                    নোট: প্রতিটি বিষয়ের জন্য সমান মার্কস ডিস্ট্রিবিউশন সেটআপ করা হবে। পরে আপনি প্রতিটি বিষয়ের জন্য আলাদাভাবে এগুলি পরিবর্তন করতে পারবেন।
                </div>
                
                <div class="mt-3">
                    <button type="submit" name="setup_marks" class="btn btn-primary">
                        <i class="fas fa-cog me-2"></i> সমস্ত বিষয়ের জন্য মার্কস সেটআপ করুন
                    </button>
                    <a href="subject_minimum_pass.php" class="btn btn-secondary ms-2">
                        <i class="fas fa-arrow-left me-2"></i> ফিরে যান
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Auto-hide success messages after 3 seconds
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 3000);
    
    // Update total as user changes the values
    $(document).ready(function() {
        $('#cq_marks, #mcq_marks, #practical_marks').on('change', function() {
            var cq = parseFloat($('#cq_marks').val()) || 0;
            var mcq = parseFloat($('#mcq_marks').val()) || 0;
            var practical = parseFloat($('#practical_marks').val()) || 0;
            
            var total = cq + mcq + practical;
            
            if (total != 100) {
                alert('সতর্কতা: মোট মার্কস ১০০ হওয়া উচিত। বর্তমানে: ' + total);
            }
        });
    });
</script>

<?php include('../includes/footer.php'); ?> 