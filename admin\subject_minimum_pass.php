<?php
// Check if user is logged in as admin
session_start();
require_once('includes/db_connection.php');
require_once('includes/functions.php');

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Check and create table if not exists
$table_name = "subject_minimum_pass";
$create_table_sql = "CREATE TABLE IF NOT EXISTS $table_name (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    cq_min_marks FLOAT DEFAULT 0,
    mcq_min_marks FLOAT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (subject_id)
)";

if (!mysqli_query($conn, $create_table_sql)) {
    $_SESSION['error'] = "টেবিল তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn);
}

// Create or alter fourth_subject_config table
$fourth_subject_config_sql = "CREATE TABLE IF NOT EXISTS fourth_subject_config (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    excess_point_limit FLOAT DEFAULT 2,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if (!mysqli_query($conn, $fourth_subject_config_sql)) {
    $_SESSION['error'] = "৪র্থ বিষয় কনফিগ টেবিল তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn);
}

// Create subject_marks_distribution table if it doesn't exist
$subject_marks_table = "CREATE TABLE IF NOT EXISTS subject_marks_distribution (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_id INT(11) NOT NULL,
    cq_marks FLOAT DEFAULT 0,
    mcq_marks FLOAT DEFAULT 0,
    practical_marks FLOAT DEFAULT 0,
    total_marks FLOAT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX (subject_id)
)";

if (!mysqli_query($conn, $subject_marks_table)) {
    $_SESSION['error'] = "বিষয় নম্বর বন্টন টেবিল তৈরি করতে সমস্যা হয়েছে: " . mysqli_error($conn);
}

// Process form submission
if (isset($_POST['save_config'])) {
    // Start transaction
    mysqli_begin_transaction($conn);
    
    try {
        foreach ($_POST['subject_id'] as $key => $subject_id) {
            $cq_min_marks = isset($_POST['cq_min_marks'][$key]) ? floatval($_POST['cq_min_marks'][$key]) : 0;
            $mcq_min_marks = isset($_POST['mcq_min_marks'][$key]) ? floatval($_POST['mcq_min_marks'][$key]) : 0;

            // Check if config already exists for this subject
            $check_sql = "SELECT id FROM $table_name WHERE subject_id = ?";
            $stmt = mysqli_prepare($conn, $check_sql);
            mysqli_stmt_bind_param($stmt, "i", $subject_id);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);
            
            if (mysqli_num_rows($result) > 0) {
                // Update existing record
                $row = mysqli_fetch_assoc($result);
                $update_sql = "UPDATE $table_name SET cq_min_marks = ?, mcq_min_marks = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "ddi", $cq_min_marks, $mcq_min_marks, $row['id']);
                mysqli_stmt_execute($stmt);
            } else {
                // Insert new record
                $insert_sql = "INSERT INTO $table_name (subject_id, cq_min_marks, mcq_min_marks) VALUES (?, ?, ?)";
                $stmt = mysqli_prepare($conn, $insert_sql);
                mysqli_stmt_bind_param($stmt, "idd", $subject_id, $cq_min_marks, $mcq_min_marks);
                mysqli_stmt_execute($stmt);
            }
        }
        
        // Save fourth subject excess point limit if set
        if (isset($_POST['fourth_subject_excess_point'])) {
            $excess_point_limit = floatval($_POST['fourth_subject_excess_point']);
            
            // Check if config already exists
            $check_sql = "SELECT id FROM fourth_subject_config LIMIT 1";
            $result = mysqli_query($conn, $check_sql);
            
            if (mysqli_num_rows($result) > 0) {
                // Update existing record
                $row = mysqli_fetch_assoc($result);
                $update_sql = "UPDATE fourth_subject_config SET excess_point_limit = ? WHERE id = ?";
                $stmt = mysqli_prepare($conn, $update_sql);
                mysqli_stmt_bind_param($stmt, "di", $excess_point_limit, $row['id']);
                mysqli_stmt_execute($stmt);
            } else {
                // Insert new record
                $insert_sql = "INSERT INTO fourth_subject_config (excess_point_limit) VALUES (?)";
                $stmt = mysqli_prepare($conn, $insert_sql);
                mysqli_stmt_bind_param($stmt, "d", $excess_point_limit);
                mysqli_stmt_execute($stmt);
            }
        }
        
        // Commit transaction
        mysqli_commit($conn);
        $_SESSION['success'] = "সফলভাবে কনফিগারেশন সংরক্ষণ করা হয়েছে";
    } catch (Exception $e) {
        // Rollback transaction on error
        mysqli_rollback($conn);
        $_SESSION['error'] = "কনফিগারেশন সংরক্ষণ করতে সমস্যা হয়েছে: " . $e->getMessage();
    }
    
    // Redirect to same page to refresh
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Get all active subjects - using default class (1)
$default_class_id = 1;
$subjects_sql = "SELECT * FROM subjects WHERE is_active = 1 ORDER BY subject_name";
$subjects_result = mysqli_query($conn, $subjects_sql);

$subjects_by_class = [];
if ($subjects_result && mysqli_num_rows($subjects_result) > 0) {
    while ($subject = mysqli_fetch_assoc($subjects_result)) {
        $subjects_by_class[$default_class_id][] = $subject;
    }
}

// Get existing minimum pass configs
$min_pass_sql = "SELECT * FROM $table_name";
$min_pass_result = mysqli_query($conn, $min_pass_sql);

$min_pass_configs = [];
if ($min_pass_result && mysqli_num_rows($min_pass_result) > 0) {
    while ($config = mysqli_fetch_assoc($min_pass_result)) {
        $min_pass_configs[$config['subject_id']] = $config;
    }
}

// Get subject marks distribution
$subject_marks = [];

// First check if subject_marks_distribution table exists
$check_table_sql = "SHOW TABLES LIKE 'subject_marks_distribution'";
$table_exists = mysqli_query($conn, $check_table_sql);

if ($table_exists && mysqli_num_rows($table_exists) > 0) {
    // Table exists, get data from it
    $marks_sql = "SELECT * FROM subject_marks_distribution";
    $marks_result = mysqli_query($conn, $marks_sql);
    
    if ($marks_result && mysqli_num_rows($marks_result) > 0) {
        while ($marks = mysqli_fetch_assoc($marks_result)) {
            $subject_marks[$marks['subject_id']] = $marks;
        }
    }
} else {
    // Fallback: Try to get marks from exam_subjects table
    $check_exam_subjects_sql = "SHOW TABLES LIKE 'exam_subjects'";
    $exam_subjects_exists = mysqli_query($conn, $check_exam_subjects_sql);
    
    if ($exam_subjects_exists && mysqli_num_rows($exam_subjects_exists) > 0) {
        $marks_sql = "SELECT subject_id, 
                      MAX(CASE WHEN component_type = 'cq' OR component_type = 'written' THEN marks ELSE 0 END) as cq_marks,
                      MAX(CASE WHEN component_type = 'mcq' THEN marks ELSE 0 END) as mcq_marks
                      FROM exam_subjects 
                      GROUP BY subject_id";
        $marks_result = mysqli_query($conn, $marks_sql);
        
        if ($marks_result && mysqli_num_rows($marks_result) > 0) {
            while ($marks = mysqli_fetch_assoc($marks_result)) {
                $subject_marks[$marks['subject_id']] = $marks;
            }
        }
    }
}

// Get fourth subject config
$fourth_subject_sql = "SELECT * FROM fourth_subject_config LIMIT 1";
$fourth_subject_result = mysqli_query($conn, $fourth_subject_sql);
$fourth_subject_config = mysqli_fetch_assoc($fourth_subject_result);
$excess_point_limit = isset($fourth_subject_config['excess_point_limit']) ? $fourth_subject_config['excess_point_limit'] : 2;

// Include header
$page_title = "বিষয় ভিত্তিক ন্যূনতম পাস নম্বর কনফিগারেশন";
include('includes/header.php');
?>

<div class="container-fluid px-4">
    <h1 class="mt-4"><?php echo $page_title; ?></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">ড্যাশবোর্ড</a></li>
        <li class="breadcrumb-item active"><?php echo $page_title; ?></li>
    </ol>
    
    <!-- Messages Section -->
    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['success']; 
        unset($_SESSION['success']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php 
        echo $_SESSION['error']; 
        unset($_SESSION['error']);
        ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif; ?>
    
    <?php if (empty($subject_marks)): ?>
    <div class="alert alert-warning mb-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>কোন বিষয় মার্কস ডেটা পাওয়া যায়নি!</h5>
        <p>বিষয় ভিত্তিক সিকিউ এবং এমসিকিউ মার্কস সেট আপ করতে নিচের বাটনে ক্লিক করুন:</p>
        <a href="subject_marks_setup.php" class="btn btn-primary">
            <i class="fas fa-cogs me-2"></i>বিষয় মার্কস সেটআপ করুন
        </a>
    </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            বিষয় ভিত্তিক ন্যূনতম পাস নম্বর কনফিগারেশন
        </div>
        <div class="card-body">
            <form method="post" action="">
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <small>
                                <i class="fas fa-info-circle"></i> প্রতিটি বিষয়ের জন্য সিকিউ এবং এমসিকিউ বিভাগের ন্যূনতম পাস নম্বর নির্ধারণ করুন।
                            </small>
                        </div>
                    </div>
                </div>
                
                <?php if (count($subjects_by_class) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="bg-light">
                                <tr>
                                    <th>বিষয় নাম</th>
                                    <th>সিকিউ ন্যূনতম পাস নম্বর</th>
                                    <th>এমসিকিউ ন্যূনতম পাস নম্বর</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subjects_by_class as $class_id => $subjects): ?>
                                    <?php foreach ($subjects as $subject): ?>
                                        <?php 
                                        $subject_id = $subject['id'];
                                        $cq_min_marks = isset($min_pass_configs[$subject_id]) ? $min_pass_configs[$subject_id]['cq_min_marks'] : 0;
                                        $mcq_min_marks = isset($min_pass_configs[$subject_id]) ? $min_pass_configs[$subject_id]['mcq_min_marks'] : 0;
                                        
                                        $cq_total = isset($subject_marks[$subject_id]) ? $subject_marks[$subject_id]['cq_marks'] : 0;
                                        $mcq_total = isset($subject_marks[$subject_id]) ? $subject_marks[$subject_id]['mcq_marks'] : 0;
                                        ?>
                                        <tr>
                                            <td>
                                                <?php echo $subject['subject_name']; ?>
                                                <input type="hidden" name="subject_id[]" value="<?php echo $subject_id; ?>">
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="cq_min_marks[]" value="<?php echo $cq_min_marks; ?>" min="0" max="<?php echo $cq_total; ?>" step="0.01" <?php echo $cq_total == 0 ? 'disabled' : ''; ?>>
                                                    <span class="input-group-text">/<?php echo $cq_total; ?></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" name="mcq_min_marks[]" value="<?php echo $mcq_min_marks; ?>" min="0" max="<?php echo $mcq_total; ?>" step="0.01" <?php echo $mcq_total == 0 ? 'disabled' : ''; ?>>
                                                    <span class="input-group-text">/<?php echo $mcq_total; ?></span>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Fourth Subject Excess Point Configuration -->
                    <div class="card mt-4 mb-4">
                        <div class="card-header bg-primary text-white">
                            <i class="fas fa-cog me-1"></i>
                            ৪র্থ বিষয় বেশি পয়েন্ট কনফিগারেশন
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <p><i class="fas fa-info-circle me-2"></i>৪র্থ বিষয়ে নির্দিষ্ট পয়েন্টের বেশি পয়েন্ট হলে, ঐ বেশি টুকু মোট ফলাফলে যোগ হবে। নির্দিষ্ট পয়েন্টের কম হলে ০ যোগ হবে।</p>
                                        <p>বর্তমান নিয়ম: ৪র্থ বিষয়ে <strong><?php echo $excess_point_limit; ?></strong> পয়েন্টের বেশি পেলে, বেশি পয়েন্ট মোট ফলাফলে যোগ হবে। কম হলে ০ যোগ হবে।</p>
                                    </div>
                                    
                                    <div class="form-group row">
                                        <label for="fourth_subject_excess_point" class="col-sm-6 col-form-label">৪র্থ বিষয়ে বেশি পয়েন্টের সীমা নির্ধারণ:</label>
                                        <div class="col-sm-6">
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="fourth_subject_excess_point" name="fourth_subject_excess_point" value="<?php echo $excess_point_limit; ?>" min="0" max="5" step="0.01">
                                                <span class="input-group-text">পয়েন্ট</span>
                                            </div>
                                            <small class="form-text text-muted">৪র্থ বিষয়ে এই পয়েন্টের বেশি পেলে, অতিরিক্ত পয়েন্ট গণনা করা হবে। কম হলে ০ যোগ হবে।</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <button type="submit" name="save_config" class="btn btn-primary">
                            <i class="fas fa-save"></i> কনফিগারেশন সংরক্ষণ করুন
                        </button>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i> কোন সক্রিয় বিষয় পাওয়া যায়নি।
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</div>

<script>
    // Auto-hide success messages after 3 seconds
    setTimeout(function() {
        $('.alert-success').fadeOut('slow');
    }, 3000);
</script>

<?php include('../includes/footer.php'); ?> 