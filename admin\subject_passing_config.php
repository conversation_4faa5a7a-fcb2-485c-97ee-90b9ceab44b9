<?php
session_start();
require_once('../includes/dbh.inc.php');
require_once('includes/functions.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Create subject_passing_config table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS subject_passing_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    cq_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    mcq_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    practical_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    overall_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id)
)";
$conn->query($create_table_sql);

// Get all subjects
$subjects_sql = "SELECT * FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_sql);
$subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[$subject['id']] = $subject;
}

// Get existing configurations
$configs_sql = "SELECT * FROM subject_passing_config";
$configs_result = $conn->query($configs_sql);
$subject_configs = [];

while ($config = $configs_result->fetch_assoc()) {
    $subject_configs[$config['subject_id']] = $config;
}

// Handle form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['save_config'])) {
        try {
            $conn->begin_transaction();
            
            $subject_ids = $_POST['subject_id'] ?? [];
            $cq_percentages = $_POST['cq_pass_percentage'] ?? [];
            $mcq_percentages = $_POST['mcq_pass_percentage'] ?? [];
            $practical_percentages = $_POST['practical_pass_percentage'] ?? [];
            $overall_percentages = $_POST['overall_pass_percentage'] ?? [];
            $is_active_values = $_POST['is_active'] ?? [];
            
            // Delete existing configurations to avoid duplicates
            $delete_sql = "DELETE FROM subject_passing_config";
            $conn->query($delete_sql);
            
            // Insert new configurations
            $insert_sql = "INSERT INTO subject_passing_config 
                          (subject_id, cq_pass_percentage, mcq_pass_percentage, practical_pass_percentage, overall_pass_percentage, is_active) 
                          VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_sql);
            
            foreach ($subject_ids as $index => $subject_id) {
                if (!isset($subjects[$subject_id])) continue; // Skip if subject doesn't exist
                
                $cq_percentage = floatval($cq_percentages[$index] ?? 33.00);
                $mcq_percentage = floatval($mcq_percentages[$index] ?? 33.00);
                $practical_percentage = floatval($practical_percentages[$index] ?? 33.00);
                $overall_percentage = floatval($overall_percentages[$index] ?? 33.00);
                $is_active = isset($is_active_values[$index]) ? 1 : 0;
                
                $stmt->bind_param("iddddi", $subject_id, $cq_percentage, $mcq_percentage, $practical_percentage, $overall_percentage, $is_active);
                $stmt->execute();
            }
            
            $conn->commit();
            $success_message = "বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন সফলভাবে সংরক্ষিত হয়েছে!";
            
            // Refresh the configs
            $configs_result = $conn->query($configs_sql);
            $subject_configs = [];
            while ($config = $configs_result->fetch_assoc()) {
                $subject_configs[$config['subject_id']] = $config;
            }
            
        } catch (Exception $e) {
            $conn->rollback();
            $error_message = "ত্রুটি: " . $e->getMessage();
        }
    }
}

// Get exam pattern types for each subject
$pattern_types = [];
$exam_subjects_sql = "SELECT es.subject_id, 
                     MAX(es.cq_marks) as max_cq, 
                     MAX(es.mcq_marks) as max_mcq, 
                     MAX(es.practical_marks) as max_practical 
                     FROM exam_subjects es 
                     GROUP BY es.subject_id";
$pattern_result = $conn->query($exam_subjects_sql);

while ($pattern = $pattern_result->fetch_assoc()) {
    $subject_id = $pattern['subject_id'];
    $has_cq = $pattern['max_cq'] > 0;
    $has_mcq = $pattern['max_mcq'] > 0;
    $has_practical = $pattern['max_practical'] > 0;
    
    // Determine pattern type
    if ($has_cq && $has_mcq && $has_practical) {
        $pattern_types[$subject_id] = 'all';
    } elseif ($has_cq && $has_mcq) {
        $pattern_types[$subject_id] = 'cq_mcq';
    } elseif ($has_cq && $has_practical) {
        $pattern_types[$subject_id] = 'cq_practical';
    } else {
        $pattern_types[$subject_id] = 'cq';
    }
}

// Get classes for subject grouping
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);
$classes = [];
while ($class = $classes_result->fetch_assoc()) {
    $classes[$class['id']] = $class;
}

// Get subjects with class info
$subject_classes_sql = "SELECT s.id, s.subject_name, s.subject_code, s.class_id 
                       FROM subjects s 
                       ORDER BY s.class_id, s.subject_name";
$subject_classes_result = $conn->query($subject_classes_sql);
$subjects_by_class = [];

while ($subject = $subject_classes_result->fetch_assoc()) {
    $class_id = $subject['class_id'];
    if (!isset($subjects_by_class[$class_id])) {
        $subjects_by_class[$class_id] = [];
    }
    $subjects_by_class[$class_id][] = $subject;
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .config-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .class-header {
            background-color: #6c757d;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
        }
        .table th, .table td {
            vertical-align: middle;
        }
        .form-label {
            font-weight: 500;
        }
        .component-badge {
            font-size: 0.8rem;
            margin-left: 5px;
            padding: 3px 8px;
        }
        .component-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 5px;
        }
        .input-group-text {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="passing_marks_config.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-cogs"></i> গ্রেডিং কনফিগ
                            </a>
                            <a href="results.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-clipboard-list"></i> ফলাফল তালিকা
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন</strong>: 
                    এখানে আপনি প্রতিটি বিষয়ের জন্য আলাদা আলাদা পাসিং শতাংশ সেট করতে পারেন। এতে করে সিকিউ, এমসিকিউ, এবং ব্যবহারিক অংশের জন্য আলাদা পাসিং মার্কস নির্ধারণ করা যাবে।
                </div>
                
                <div class="config-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cog me-2"></i> বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <?php foreach ($classes as $class_id => $class): ?>
                                <?php if (isset($subjects_by_class[$class_id])): ?>
                                    <div class="class-header mb-3">
                                        <i class="fas fa-graduation-cap me-2"></i> <?php echo $class['class_name']; ?>
                                    </div>
                                    
                                    <div class="table-responsive mb-4">
                                        <table class="table table-bordered table-hover">
                                            <thead class="table-light">
                                                <tr>
                                                    <th width="5%">#</th>
                                                    <th width="20%">বিষয়</th>
                                                    <th width="15%">সিকিউ পাসিং %</th>
                                                    <th width="15%">এমসিকিউ পাসিং %</th>
                                                    <th width="15%">ব্যবহারিক পাসিং %</th>
                                                    <th width="15%">মোট পাসিং %</th>
                                                    <th width="10%">সক্রিয়</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($subjects_by_class[$class_id] as $index => $subject): ?>
                                                    <?php 
                                                        $subject_id = $subject['id'];
                                                        $config = $subject_configs[$subject_id] ?? null;
                                                        $pattern = $pattern_types[$subject_id] ?? 'cq';
                                                        
                                                        $cq_pass = $config ? $config['cq_pass_percentage'] : 33.00;
                                                        $mcq_pass = $config ? $config['mcq_pass_percentage'] : 33.00;
                                                        $practical_pass = $config ? $config['practical_pass_percentage'] : 33.00;
                                                        $overall_pass = $config ? $config['overall_pass_percentage'] : 33.00;
                                                        $is_active = $config ? $config['is_active'] : 1;
                                                        
                                                        $show_mcq = in_array($pattern, ['cq_mcq', 'all']);
                                                        $show_practical = in_array($pattern, ['cq_practical', 'all']);
                                                    ?>
                                                    <tr>
                                                        <td><?php echo $index + 1; ?></td>
                                                        <td>
                                                            <input type="hidden" name="subject_id[]" value="<?php echo $subject_id; ?>">
                                                            <strong><?php echo $subject['subject_name']; ?></strong>
                                                            <small class="text-muted d-block"><?php echo $subject['subject_code']; ?></small>
                                                            
                                                            <div class="component-container">
                                                                <span class="badge bg-info component-badge">সিকিউ</span>
                                                                <?php if ($show_mcq): ?>
                                                                    <span class="badge bg-success component-badge">এমসিকিউ</span>
                                                                <?php endif; ?>
                                                                <?php if ($show_practical): ?>
                                                                    <span class="badge bg-warning component-badge">ব্যবহারিক</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="input-group">
                                                                <input type="number" class="form-control" name="cq_pass_percentage[]" 
                                                                       value="<?php echo $cq_pass; ?>" 
                                                                       min="0" max="100" step="0.01" required>
                                                                <span class="input-group-text">%</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="input-group">
                                                                <input type="number" class="form-control" name="mcq_pass_percentage[]" 
                                                                       value="<?php echo $mcq_pass; ?>" 
                                                                       min="0" max="100" step="0.01" required
                                                                       <?php echo !$show_mcq ? 'disabled' : ''; ?>>
                                                                <span class="input-group-text">%</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="input-group">
                                                                <input type="number" class="form-control" name="practical_pass_percentage[]" 
                                                                       value="<?php echo $practical_pass; ?>" 
                                                                       min="0" max="100" step="0.01" required
                                                                       <?php echo !$show_practical ? 'disabled' : ''; ?>>
                                                                <span class="input-group-text">%</span>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <div class="input-group">
                                                                <input type="number" class="form-control" name="overall_pass_percentage[]" 
                                                                       value="<?php echo $overall_pass; ?>" 
                                                                       min="0" max="100" step="0.01" required>
                                                                <span class="input-group-text">%</span>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="form-check form-switch d-flex justify-content-center">
                                                                <input class="form-check-input" type="checkbox" name="is_active[]" 
                                                                       <?php echo $is_active ? 'checked' : ''; ?>>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                            
                            <div class="d-flex justify-content-end mt-4">
                                <button type="submit" name="save_config" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>সেভ করুন
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="mt-4 mb-5 p-4 bg-white rounded shadow-sm">
                    <h4>বিষয় ভিত্তিক পাস/ফেইল নির্ধারণ</h4>
                    <p>নিম্নলিখিত নিয়ম অনুসারে একজন ছাত্র/ছাত্রী কোন বিষয়ে পাস করবে:</p>
                    <ol>
                        <li>প্রতিটি বিষয়ের প্রতিটি অংশে (সিকিউ, এমসিকিউ, ব্যবহারিক) নির্ধারিত শতাংশ নম্বর অর্জন করতে হবে।</li>
                        <li>একজন ছাত্র/ছাত্রী যদি যেকোনো একটি অংশে ফেইল করে, তাহলে সে সম্পূর্ণ বিষয়ে ফেইল করবে।</li>
                        <li>এছাড়াও, মোট নম্বরের নির্ধারিত শতাংশ অর্জন করতে হবে।</li>
                    </ol>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        এই কনফিগারেশন মার্কশিট, টেবুলেশন শিট, ফলাফল তালিকা ইত্যাদিতে প্রয়োগ করা হবে।
                    </div>
                    
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>বিশেষ দ্রষ্টব্য:</strong> যেসব বিষয়ের জন্য এমসিকিউ বা ব্যবহারিক অংশ নেই, সেগুলোর ক্ষেত্রে শুধু সিকিউ এবং মোট নম্বরের শতাংশ বিবেচনা করা হবে।
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
            
            // Enable/disable mcq and practical fields based on patterns
            $('input[name="is_active[]"]').change(function() {
                var row = $(this).closest('tr');
                var isChecked = $(this).prop('checked');
                
                row.find('input[type="number"]').prop('disabled', !isChecked);
            });
        });
    </script>
</body>
</html> 