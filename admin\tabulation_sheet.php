<?php
session_start();
require_once('../includes/dbh.inc.php');
require_once('includes/functions.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

// Get all exams
// First check if class_id column exists in exams table
$check_class_id = $conn->query("SHOW COLUMNS FROM exams LIKE 'class_id'");
if ($check_class_id->num_rows > 0) {
    // If class_id exists, join with classes table
    $exams_sql = "SELECT e.*, c.class_name 
                FROM exams e 
                JOIN classes c ON e.class_id = c.id 
                ORDER BY e.exam_date DESC";
} else {
    // If class_id doesn't exist, don't join with classes table
    $exams_sql = "SELECT e.* 
                FROM exams e 
                ORDER BY e.exam_date DESC";
}
$exams_result = $conn->query($exams_sql);

// Get all classes
$classes_sql = "SELECT * FROM classes ORDER BY class_name";
$classes_result = $conn->query($classes_sql);

// Get variables
$class_id = isset($_GET['class_id']) ? intval($_GET['class_id']) : 0;
$exam_id = isset($_GET['exam_id']) ? intval($_GET['exam_id']) : 0;

// Tabulation data
$students = [];
$subjects = [];
$marks = [];
$exam_details = null;
$class_details = null;

// If class_id and exam_id are provided, get the tabulation sheet data
if ($class_id > 0 && $exam_id > 0) {
    // Get exam details
    if ($check_class_id->num_rows > 0) {
        // If class_id exists, join with classes table
        $exam_sql = "SELECT e.*, c.class_name 
                    FROM exams e 
                    JOIN classes c ON e.class_id = c.id 
                    WHERE e.id = ?";
    } else {
        // If class_id doesn't exist, don't join with classes table
        $exam_sql = "SELECT e.* 
                    FROM exams e 
                    WHERE e.id = ?";
    }
    $exam_stmt = $conn->prepare($exam_sql);
    $exam_stmt->bind_param("i", $exam_id);
    $exam_stmt->execute();
    $exam_details = $exam_stmt->get_result()->fetch_assoc();
    
    // Get class details
    $class_sql = "SELECT * FROM classes WHERE id = ?";
    $class_stmt = $conn->prepare($class_sql);
    $class_stmt->bind_param("i", $class_id);
    $class_stmt->execute();
    $class_details = $class_stmt->get_result()->fetch_assoc();
    
    // Get subjects for this exam
    $subjects_sql = "SELECT es.*, s.subject_name, s.subject_code 
                    FROM exam_subjects es 
                    JOIN subjects s ON es.subject_id = s.id 
                    WHERE es.exam_id = ?
                    ORDER BY s.subject_name";
    $subjects_stmt = $conn->prepare($subjects_sql);
    $subjects_stmt->bind_param("i", $exam_id);
    $subjects_stmt->execute();
    $subjects_result = $subjects_stmt->get_result();
    
    while ($subject = $subjects_result->fetch_assoc()) {
        $subjects[] = $subject;
    }
    
    // Get students
    $students_sql = "SELECT s.*, 
                    CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                    COALESCE(s.roll_number, 'N/A') as roll_number
                    FROM students s 
                    WHERE s.class_id = ? 
                    ORDER BY s.roll_number, s.first_name, s.last_name";
    $students_stmt = $conn->prepare($students_sql);
    $students_stmt->bind_param("i", $class_id);
    $students_stmt->execute();
    $students_result = $students_stmt->get_result();
    
    while ($student = $students_result->fetch_assoc()) {
        $students[] = $student;
    }
    
    // Get all marks for the selected exam and class
    $marks_sql = "SELECT m.*, s.id as student_id, 
                  CONCAT(COALESCE(s.first_name, ''), ' ', COALESCE(s.last_name, '')) as student_name,
                  s.roll_number
                  FROM marks m
                  JOIN students s ON m.student_id = s.id
                  WHERE m.exam_id = ? AND s.class_id = ?";
    $marks_stmt = $conn->prepare($marks_sql);
    $marks_stmt->bind_param("ii", $exam_id, $class_id);
    $marks_stmt->execute();
    $marks_result = $marks_stmt->get_result();
    
    // Organize marks by student and subject
    $marks = [];
    while ($mark = $marks_result->fetch_assoc()) {
        $marks[$mark['student_id']][$mark['subject_id']] = $mark;
    }
}

// Function to calculate total marks, GPA, and grade for a student
function calculateResult($student_marks, $subjects) {
    global $conn;
    $total_obtained = 0;
    $total_max = 0;
    $fail_count = 0;
    $component_fail = false;
    $percentage = 0; // Initialize percentage variable here
    
    foreach ($subjects as $subject) {
        $subject_id = $subject['subject_id'];
        $max_marks = $subject['total_marks'];
        $total_max += $max_marks;
        
        if (isset($student_marks[$subject_id])) {
            $obtained_marks = $student_marks[$subject_id]['total_marks'];
            $total_obtained += $obtained_marks;
            $cq_marks = $student_marks[$subject_id]['cq_marks'];
            $mcq_marks = $student_marks[$subject_id]['mcq_marks'];
            $max_cq = $subject['cq_marks'];
            $max_mcq = $subject['mcq_marks'];
            $is_failed = false;
            $cq_min = 0;
            $mcq_min = 0;
            
            // Check subject-specific minimum pass marks for CQ and MCQ
            if ($max_cq > 0 || $max_mcq > 0) {
                // Check for minimum marks in the subject_minimum_pass table
                $sql = "SELECT cq_min_marks, mcq_min_marks FROM subject_minimum_pass 
                        WHERE subject_id = ? AND is_active = 1";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $subject_id);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result && $result->num_rows > 0) {
                    $min_pass = $result->fetch_assoc();
                    $cq_min = $min_pass['cq_min_marks'];
                    $mcq_min = $min_pass['mcq_min_marks'];
                    
                    // Check for minimum CQ marks
                    if ($max_cq > 0 && $cq_min > 0 && $cq_marks < $cq_min) {
                        $is_failed = true;
                    }
                    
                    // Check for minimum MCQ marks
                    if ($max_mcq > 0 && $mcq_min > 0 && $mcq_marks < $mcq_min) {
                        $is_failed = true;
                    }
                }
            }
            
            // Store component pass/fail status for use in display
            if (isset($student_marks[$subject_id])) {
                $student_marks[$subject_id]['cq_passed'] = ($max_cq > 0 && $cq_min > 0) ? ($cq_marks >= $cq_min) : true;
                $student_marks[$subject_id]['mcq_passed'] = ($max_mcq > 0 && $mcq_min > 0) ? ($mcq_marks >= $mcq_min) : true;
                
                // Check if this subject has component fail
                if ((!$student_marks[$subject_id]['cq_passed']) || (!$student_marks[$subject_id]['mcq_passed'])) {
                    $component_fail = true; // Track if there's any component fail
                    $is_failed = true;
                }
            }
            
            // If no component-specific failure, check overall passing marks
            if (!$is_failed) {
                // Calculate passing mark with fallback to default 33%
                $passing_mark = isset($subject['pass_marks']) ? $subject['pass_marks'] : 0;
                if ($passing_mark <= 0) {
                    $passing_mark = $max_marks * 0.33; // 33% of max marks
                }
                
                if ($obtained_marks < $passing_mark) {
                $is_failed = true;
                }
            }
            
            if ($is_failed) {
                $fail_count++;
            }
        }
    }
    
    // Calculate percentage, GPA, and grade
    // If any component failure exists, set GPA to 0 and grade to F
    if ($component_fail || $fail_count > 0) {
        $gpa = 0;
        $grade = 'F';
        $result_status = 'Fail';
        // Make sure percentage is set even in fail case
        if ($total_max > 0) {
            $percentage = ($total_obtained / $total_max) * 100;
        } else {
            $percentage = 0;
        }
    } else {
        if ($total_max > 0) {
            $percentage = ($total_obtained / $total_max) * 100;
        } else {
            $percentage = 0;
        }
        
    if ($percentage >= 80) {
        $gpa = 5.0;
        $grade = 'A+';
    } elseif ($percentage >= 70) {
        $gpa = 4.0;
        $grade = 'A';
    } elseif ($percentage >= 60) {
        $gpa = 3.5;
        $grade = 'A-';
    } elseif ($percentage >= 50) {
        $gpa = 3.0;
        $grade = 'B';
    } elseif ($percentage >= 40) {
        $gpa = 2.0;
        $grade = 'C';
    } elseif ($percentage >= 33) {
        $gpa = 1.0;
        $grade = 'D';
    } else {
        $gpa = 0;
        $grade = 'F';
    }
    
        $result_status = 'Pass';
    }
    
    return [
        'total_obtained' => $total_obtained,
        'total_max' => $total_max,
        'percentage' => $percentage,
        'gpa' => $gpa,
        'grade' => $grade,
        'result_status' => $result_status,
        'fail_count' => $fail_count
    ];
}

// Function to get letter grade based on marks
function getGrade($marks, $total) {
    $percentage = ($marks / $total) * 100;
    
    if ($percentage >= 80) return 'A+';
    elseif ($percentage >= 70) return 'A';
    elseif ($percentage >= 60) return 'A-';
    elseif ($percentage >= 50) return 'B';
    elseif ($percentage >= 40) return 'C';
    elseif ($percentage >= 33) return 'D';
    else return 'F';
}

// Function to get GPA based on marks
function getGPA($marks, $total) {
    $percentage = ($marks / $total) * 100;
    
    if ($percentage >= 80) return 5.00;
    elseif ($percentage >= 70) return 4.00;
    elseif ($percentage >= 60) return 3.50;
    elseif ($percentage >= 50) return 3.00;
    elseif ($percentage >= 40) return 2.00;
    elseif ($percentage >= 33) return 1.00;
    else return 0.00;
}

// Sort students by rank (highest total marks first)
function sortStudentsByRank(&$students, $marks, $subjects) {
    $ranks = [];
    
    // Calculate results for all students
    foreach ($students as $student) {
        $student_id = $student['id'];
        $student_marks = isset($marks[$student_id]) ? $marks[$student_id] : [];
        $result = calculateResult($student_marks, $subjects);
        
        $ranks[$student_id] = [
            'total_obtained' => $result['total_obtained'],
            'gpa' => $result['gpa'],
            'fail_count' => $result['fail_count']
        ];
    }
    
    // Sort students by total obtained marks (if there are no fails), or by GPA
    usort($students, function($a, $b) use ($ranks) {
        $a_id = $a['id'];
        $b_id = $b['id'];
        
        // If one student has fails and the other doesn't, the one without fails gets higher rank
        if ($ranks[$a_id]['fail_count'] == 0 && $ranks[$b_id]['fail_count'] > 0) {
            return -1;
        } else if ($ranks[$a_id]['fail_count'] > 0 && $ranks[$b_id]['fail_count'] == 0) {
            return 1;
        }
        
        // If both have fails or both don't have fails, compare by total marks
        return $ranks[$b_id]['total_obtained'] - $ranks[$a_id]['total_obtained'];
    });
    
    // Add rank to each student
    $current_rank = 1;
    $prev_marks = -1;
    $skip_ranks = 0;
    
    foreach ($students as &$student) {
        $student_id = $student['id'];
        $total_marks = $ranks[$student_id]['total_obtained'];
        
        // If the total marks are the same as the previous student, give the same rank
        if ($total_marks == $prev_marks) {
            $student['rank'] = $current_rank - $skip_ranks - 1;
            $skip_ranks++;
        } else {
            $student['rank'] = $current_rank;
            $skip_ranks = 0;
        }
        
        $prev_marks = $total_marks;
        $current_rank++;
    }
}

// If data is being displayed, sort students by rank
if ($class_id > 0 && $exam_id > 0 && !empty($students) && !empty($subjects)) {
    sortStudentsByRank($students, $marks, $subjects);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>টেবুলেশন শীট - <?php echo $exam_details ? $exam_details['exam_name'] : 'স্কুল ম্যানেজমেন্ট সিস্টেম'; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @media print {
            @page {
                size: A4 landscape;
                margin: 0.5cm;
            }
            body {
                margin: 0;
                padding: 0;
                font-size: 10pt;
                width: 100%;
                height: 100%;
            }
            .no-print {
                display: none !important;
            }
            .page-header, .page-footer {
                display: block;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                page-break-inside: auto;
            }
            tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            thead {
                display: table-header-group;
            }
            tfoot {
                display: table-footer-group;
            }
            .table-responsive {
                overflow-x: visible !important;
            }
            
            /* Print color handling */
            .fourth-subject, .optional-subject, .compulsory-subject, 
            .subject-header, .fail, .pass, .badge {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
            
            .student-result {
                page-break-inside: avoid;
            }
            
            .container-fluid, .row, main {
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: none !important;
            }
        }
        
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            line-height: 1.5;
            background-color: #f8f9fa;
        }
        .tabulation-container {
            width: 100%;
            max-width: 297mm; /* A4 landscape width */
            margin: 0 auto;
            padding: 10mm;
        }
        .table-header {
            text-align: center;
            margin-bottom: 20px;
        }
        .school-name {
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .school-address {
            font-size: 12pt;
            margin-bottom: 10px;
        }
        .sheet-title {
            font-size: 18pt;
            font-weight: bold;
            margin: 15px 0;
            text-transform: uppercase;
            color: #005fb3;
        }
        .tabulation-table th, .tabulation-table td {
            text-align: center;
            padding: 5px;
            border: 1px solid #333;
            font-size: 10pt;
        }
        .tabulation-table th {
            background-color: #e9ecef;
            white-space: nowrap;
        }
        .student-name-cell {
            text-align: left;
            white-space: nowrap;
        }
        .key-sample {
            display: inline-block;
            width: 50px;
            text-align: center;
            border-radius: 3px;
            margin-right: 5px;
        }
        .table-key .card-header {
            background-color: #0d6efd;
            color: white;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            flex: 1;
            text-align: center;
            margin: 0 20px;
        }
        .signature-line {
            border-top: 1px solid #333;
            padding-top: 5px;
            margin-top: 40px;
        }
        .fixed-column {
            position: sticky;
            left: 0;
            background-color: #fff;
            z-index: 1;
        }
        .fixed-column-2 {
            position: sticky;
            left: 80px; /* Adjust based on first column width */
            background-color: #fff;
            z-index: 1;
        }
        .student-result {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
            margin-bottom: 10px;
            page-break-inside: avoid;
            max-width: 297mm; /* A4 landscape width */
            width: 100%;
            margin-left: auto;
            margin-right: auto;
        }
        .student-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 8px 12px;
            font-weight: 500;
        }
        .student-info {
            font-size: 10pt;
        }
        .student-details {
            font-size: 12pt;
            white-space: nowrap;
            line-height: 1.4;
            margin-left: 5px;
        }
        .student-name {
            font-weight: bold;
            font-size: 13pt;
            color: #0d6efd;
        }
        .student-roll {
            font-weight: bold;
            margin-right: 5px;
            color: #333;
        }
        .result-summary .badge {
            font-size: 8pt;
            padding: 2px 4px;
        }
        .empty-mark {
            color: #aaa;
            font-style: italic;
        }
        .fourth-subject {
            background-color: #e7f1ff !important;
            border-top: 2px solid #0d6efd !important;
        }
        .optional-subject {
            background-color: #f8f9fa !important;
        }
        .compulsory-subject {
            background-color: #ffffff !important;
        }
        .fail {
            color: red;
            font-weight: bold;
        }
        .pass {
            color: green;
            font-weight: bold;
        }
        /* Compact table styles */
        .table-sm td, .table-sm th {
            padding: 0.2rem 0.4rem;
            font-size: 9pt;
        }
        .total-marks {
            font-weight: bold;
            font-size: 10pt;
        }
        .grade-badge {
            padding: 2px 4px;
            font-size: 8pt;
            border-radius: 2px;
            margin-left: 2px;
            white-space: nowrap;
            display: inline-block;
            min-width: 60px;
        }
        @media screen {
            body {
                background-color: #f0f0f0;
            }
            .tabulation-container {
                background-color: white;
                box-shadow: 0 0 15px rgba(0,0,0,0.1);
                border-radius: 5px;
                margin-bottom: 30px;
            }
        }
        .student-photo {
            width: 45px;
            height: 45px;
            object-fit: cover;
            border-radius: 50%;
            border: 1px solid #ddd;
            background-color: #fff;
        }
        .student-image {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        @media print {
            .student-photo {
                width: 35px;
                height: 35px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
                    <h1 class="h2">টেবুলেশন শীট</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="exam_dashboard.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-tachometer-alt"></i> ড্যাশবোর্ড
                            </a>
                            <a href="marks_entry.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-pen-alt"></i> নম্বর এন্ট্রি
                            </a>
                            <a href="student_marksheet.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-file-alt"></i> ছাত্র/ছাত্রী মার্কশীট
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Selection Form -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5>পরীক্ষা এবং ক্লাস নির্বাচন করুন</h5>
                    </div>
                    <div class="card-body">
                        <form method="get" action="" class="row">
                            <div class="col-md-5 mb-3">
                                <label for="exam_id" class="form-label">পরীক্ষা নির্বাচন করুন</label>
                                <select class="form-select" id="exam_id" name="exam_id" required onchange="this.form.submit()">
                                    <option value="">পরীক্ষা নির্বাচন করুন</option>
                                    <?php if ($exams_result && $exams_result->num_rows > 0): 
                                        while ($exam = $exams_result->fetch_assoc()): 
                                    ?>
                                        <option value="<?php echo $exam['id']; ?>" <?php echo ($exam_id == $exam['id']) ? 'selected' : ''; ?>
                                                data-class="<?php echo $exam['class_id']; ?>">
                                            <?php echo $exam['exam_name'] . (isset($exam['class_name']) ? ' - ' . $exam['class_name'] : '') . ' (' . date('d/m/Y', strtotime($exam['exam_date'])) . ')'; ?>
                                        </option>
                                    <?php endwhile; endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-5 mb-3">
                                <label for="class_id" class="form-label">ক্লাস নির্বাচন করুন</label>
                                <select class="form-select" id="class_id" name="class_id" required onchange="this.form.submit()">
                                    <option value="">ক্লাস নির্বাচন করুন</option>
                                    <?php if ($classes_result && $classes_result->num_rows > 0):
                                        while ($class = $classes_result->fetch_assoc()): 
                                    ?>
                                        <option value="<?php echo $class['id']; ?>" <?php echo ($class_id == $class['id']) ? 'selected' : ''; ?>>
                                            <?php echo $class['class_name']; ?>
                                        </option>
                                    <?php endwhile; endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="button" onclick="printTabulation()" class="btn btn-primary w-100">
                                    <i class="fas fa-print"></i> প্রিন্ট শীট
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Print Options Modal -->
                <div class="modal fade" id="printOptionsModal" tabindex="-1" aria-labelledby="printOptionsModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="printOptionsModalLabel"><i class="fas fa-print me-2"></i>প্রিন্ট অপশন</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-12 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <img src="../assets/images/a4-landscape.png" alt="A4 Landscape" class="img-fluid mb-2" style="max-height: 100px;">
                                                <h5>A4 আড়াআড়ি (Landscape)</h5>
                                                <p class="text-muted">297mm × 210mm</p>
                                                <button type="button" onclick="printTabulationSheet('a4')" class="btn btn-primary w-100">
                                                    <i class="fas fa-print me-2"></i>A4 আড়াআড়ি প্রিন্ট করুন
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>প্রিন্ট সেটআপে নিশ্চিত করুন:
                                            <ul class="mb-0 mt-2">
                                                <li>পেপার সাইজ A4 সিলেক্ট করুন</li>
                                                <li>অরিয়েন্টেশন "Landscape" সিলেক্ট করুন</li>
                                                <li>মার্জিন "None" বা মিনিমাম রাখুন</li>
                                                <li>হেডার/ফুটার "Empty" সিলেক্ট করুন</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if ($class_id > 0 && $exam_id > 0 && !empty($students) && !empty($subjects)): ?>
                <!-- Tabulation Sheet -->
                <div class="tabulation-container">
                    <div class="table-header">
                        <div class="school-name">স্কুল ম্যানেজমেন্ট সিস্টেম</div>
                        <div class="school-address">বাংলাদেশ</div>
                        <div class="sheet-title">টেবুলেশন শীট</div>
                        <?php if ($exam_details): ?>
                        <div class="exam-details">
                            <h4><?php echo $exam_details['exam_name']; ?> - <?php echo $class_details['class_name']; ?></h4>
                            <h5>তারিখ: <?php echo date('d F Y', strtotime($exam_details['exam_date'])); ?></h5>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Table key/legend -->
                    <div class="table-key no-print mb-3">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <strong>টেবিল ব্যাখ্যা:</strong>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3 mb-2">
                                                <span class="key-sample compulsory-subject p-1 border">12 (A)</span> - আবশ্যিক বিষয়
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <span class="key-sample optional-subject p-1 border">12 (A)</span> - ঐচ্ছিক বিষয়
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <span class="key-sample fourth-subject p-1 border">12 (A)</span> - ৪র্থ বিষয়
                                            </div>
                                            <div class="col-md-3 mb-2">
                                                <span class="key-sample empty-mark p-1 border">-</span> - নম্বর প্রদান করা হয়নি
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <?php
                    // Organize subject information and student chosen subjects
                    $all_subjects = [];
                    foreach ($subjects as $subject) {
                        $all_subjects[$subject['subject_id']] = $subject;
                    }
                    
                    // Get all student subjects by student ID
                    $student_chosen_subjects = [];
                    $student_ids = array_column($students, 'id');
                    $student_ids_str = implode(',', $student_ids);
                    
                    if (!empty($student_ids)) {
                        $all_chosen_sql = "SELECT student_id, subject_id, category FROM student_subjects 
                                          WHERE student_id IN ($student_ids_str)";
                        $all_chosen_result = $conn->query($all_chosen_sql);
                        
                        if ($all_chosen_result && $all_chosen_result->num_rows > 0) {
                            while ($chosen = $all_chosen_result->fetch_assoc()) {
                                if (!isset($student_chosen_subjects[$chosen['student_id']])) {
                                    $student_chosen_subjects[$chosen['student_id']] = [];
                                }
                                $student_chosen_subjects[$chosen['student_id']][$chosen['subject_id']] = $chosen['category'];
                            }
                        }
                    }
                    ?>
                    
                    <!-- For each student, display a separate table with only their chosen subjects -->
                    <?php foreach ($students as $index => $student): 
                        $student_id = $student['id'];
                        $student_marks = isset($marks[$student_id]) ? $marks[$student_id] : [];
                        $result = calculateResult($student_marks, $subjects);
                        
                        // Get this student's chosen subjects
                        $chosen_subjects = isset($student_chosen_subjects[$student_id]) ? $student_chosen_subjects[$student_id] : [];
                        
                        // Ensure we only display up to 7 subjects
                        $subject_count = count($chosen_subjects);
                        if ($subject_count > 7) {
                            // If more than 7, prioritize keeping the required subjects
                            $required_subjects = [];
                            $optional_subjects = [];
                            $fourth_subjects = [];
                            
                            foreach ($chosen_subjects as $subject_id => $category) {
                                if ($category === 'fourth') {
                                    $fourth_subjects[$subject_id] = $category;
                                } else if ($category === 'optional') {
                                    $optional_subjects[$subject_id] = $category;
                                } else {
                                    $required_subjects[$subject_id] = $category;
                                }
                            }
                            
                            // Keep required first, then fourth, then optional until we reach 7
                            $chosen_subjects = [];
                            foreach ($required_subjects as $subject_id => $category) {
                                $chosen_subjects[$subject_id] = $category;
                                if (count($chosen_subjects) >= 7) break;
                            }
                            
                            if (count($chosen_subjects) < 7) {
                                foreach ($fourth_subjects as $subject_id => $category) {
                                    $chosen_subjects[$subject_id] = $category;
                                    if (count($chosen_subjects) >= 7) break;
                                }
                            }
                            
                            if (count($chosen_subjects) < 7) {
                                foreach ($optional_subjects as $subject_id => $category) {
                                    $chosen_subjects[$subject_id] = $category;
                                    if (count($chosen_subjects) >= 7) break;
                                }
                            }
                        }
                    ?>
                    
                    <div class="student-result mb-4">
                        <div class="student-header bg-light p-2 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="student-image me-2">
                                    <?php 
                                    // Try to find image field - check various possible column names for image
                                    $possible_image_fields = ['profile_photo', 'image', 'photo', 'student_image', 'image_path', 'picture'];
                                    $image_path = '';
                                    
                                    foreach ($possible_image_fields as $field) {
                                        if (isset($student[$field]) && !empty($student[$field])) {
                                            $image_path = $student[$field];
                                            break;
                                        }
                                    }
                                    
                                    // If still no image found, try to query the database structure
                                    if (empty($image_path)) {
                                        $columns_query = "SHOW COLUMNS FROM students LIKE '%image%' OR LIKE '%photo%' OR LIKE '%picture%'";
                                        $columns_result = $conn->query($columns_query);
                                        
                                        if ($columns_result && $columns_result->num_rows > 0) {
                                            while ($column = $columns_result->fetch_assoc()) {
                                                $field_name = $column['Field'];
                                                
                                                // Get the specific field value for this student
                                                $image_query = "SELECT $field_name FROM students WHERE id = ?";
                                                $image_stmt = $conn->prepare($image_query);
                                                $image_stmt->bind_param("i", $student_id);
                                                $image_stmt->execute();
                                                $image_result = $image_stmt->get_result();
                                                
                                                if ($image_result && $image_result->num_rows > 0) {
                                                    $image_data = $image_result->fetch_assoc();
                                                    if (!empty($image_data[$field_name])) {
                                                        $image_path = $image_data[$field_name];
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Check if the path already contains the uploads directory
                                    if (!empty($image_path)) {
                                        if (strpos($image_path, 'uploads/') === false) {
                                            $image_path = '../uploads/profile_photos/' . $image_path;
                                        } else if (strpos($image_path, '../') === false) {
                                            $image_path = '../' . $image_path;
                                        }
                                    } else {
                                        // Fallback to default image
                                        $image_path = '../assets/images/default-user.png';
                                    }
                                    ?>
                                    
                                    <!-- Display student photo with fallback -->
                                    <img src="<?php echo $image_path; ?>" 
                                         alt="<?php echo $student['student_name']; ?>" 
                                         class="student-photo"
                                         onerror="this.onerror=null; this.src='../assets/images/default-user.png';">
                                </div>
                                <div class="student-details">
                                    <div><strong>ক্রম:</strong> <?php echo $index + 1; ?> | 
                                    <span class="student-roll"><strong>রোল:</strong> <?php echo $student['roll_number']; ?></span></div>
                                    <div><strong>নাম:</strong> <span class="student-name"><?php echo $student['student_name']; ?></span></div>
                                </div>
                            </div>
                            <div class="result-summary">
                                <span class="badge <?php echo ($result['result_status'] == 'Pass') ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo ($result['result_status'] == 'Pass') ? 'কৃতকার্য' : 'অকৃতকার্য'; ?>
                                </span>
                                <span class="ms-2"><strong>র‍্যাঙ্ক:</strong> <?php echo isset($student['rank']) ? $student['rank'] : 'N/A'; ?></span>
                                <span class="ms-2"><strong>জিপিএ:</strong> <?php echo number_format($result['gpa'], 2); ?></span>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-0">
                                <thead>
                                    <tr>
                                        <?php 
                                        $colspan_adjustment = 0;
                                        foreach ($chosen_subjects as $subject_id => $category): 
                                            if (!isset($all_subjects[$subject_id])) {
                                                $colspan_adjustment++;
                                                continue;
                                            }
                                            $subject = $all_subjects[$subject_id];
                                            $category_class = '';
                                            $category_text = '';
                                            
                                            if ($category === 'fourth') {
                                                $category_class = 'fourth-subject';
                                                $category_text = '৪র্থ বিষয়';
                                            } else if ($category === 'optional') {
                                                $category_class = 'optional-subject';
                                                $category_text = 'ঐচ্ছিক';
                                            } else {
                                                $category_class = 'compulsory-subject';
                                                $category_text = 'আবশ্যিক';
                                            }
                                        ?>
                                        <th class="<?php echo $category_class; ?>" style="min-width: 100px;">
                                            <?php echo $subject['subject_name']; ?> <small>(<?php echo intval($subject['total_marks']); ?>)</small>
                                        </th>
                                        <?php endforeach; ?>
                                        <th style="min-width: 60px;">মোট</th>
                                        <th style="min-width: 50px;">%</th>
                                        <th style="min-width: 50px;">গ্রেড</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <?php foreach ($chosen_subjects as $subject_id => $category): 
                                            if (!isset($all_subjects[$subject_id])) continue;
                                            $subject = $all_subjects[$subject_id];
                                            $has_mark = isset($student_marks[$subject_id]);
                                            $mark_value = $has_mark ? $student_marks[$subject_id]['total_marks'] : '-';
                                            
                                            // Calculate passing mark with fallback to default 33%
                                            $passing_mark = isset($subject['pass_marks']) ? $subject['pass_marks'] : 0;
                                            if ($passing_mark <= 0) {
                                                $passing_mark = $subject['total_marks'] * 0.33; // 33% of max marks
                                            }
                                            
                                            $is_fail = $has_mark && ($mark_value < $passing_mark || 
                                                    (isset($student_marks[$subject_id]['cq_passed']) && !$student_marks[$subject_id]['cq_passed']) || 
                                                    (isset($student_marks[$subject_id]['mcq_passed']) && !$student_marks[$subject_id]['mcq_passed']));
                                            
                                            $cell_class = '';
                                            if ($category === 'fourth') {
                                                $cell_class = 'fourth-subject';
                                            } else if ($category === 'optional') {
                                                $cell_class = 'optional-subject';
                                            } else {
                                                $cell_class = 'compulsory-subject';
                                            }
                                            
                                            if ($is_fail) {
                                                $cell_class .= ' fail';
                                            }
                                        ?>
                                        <td class="<?php echo $cell_class; ?> text-center">
                                        <?php if ($has_mark): ?>
                                                <?php 
                                                // Compact display of marks with + signs
                                                $marks_parts = [];
                                                
                                                // Add component abbreviations
                                                if (isset($student_marks[$subject_id]['cq_marks']) && $student_marks[$subject_id]['cq_marks'] > 0) {
                                                    $marks_parts[] = intval($student_marks[$subject_id]['cq_marks']);
                                                }
                                                if (isset($student_marks[$subject_id]['mcq_marks']) && $student_marks[$subject_id]['mcq_marks'] > 0) {
                                                    $marks_parts[] = intval($student_marks[$subject_id]['mcq_marks']);
                                                }
                                                if (isset($student_marks[$subject_id]['practical_marks']) && $student_marks[$subject_id]['practical_marks'] > 0) {
                                                    $marks_parts[] = intval($student_marks[$subject_id]['practical_marks']);
                                                }
                                                
                                                if (count($marks_parts) > 1) {
                                                    echo implode('+', $marks_parts) . '=';
                                                }
                                                ?>
                                                <span class="total-marks"><?php echo intval($mark_value); ?></span>
                                                <span class="grade-badge <?php echo $is_fail ? 'bg-danger' : 'bg-success'; ?> text-white">
                                                <?php 
                                                // Check if failed in CQ or MCQ component
                                                if (isset($student_marks[$subject_id]['cq_passed']) && !$student_marks[$subject_id]['cq_passed'] || 
                                                    isset($student_marks[$subject_id]['mcq_passed']) && !$student_marks[$subject_id]['mcq_passed']) {
                                                    echo 'F (0.00)'; // Always show F if failed in CQ or MCQ
                                                } else {
                                                    $grade = getGrade($mark_value, $subject['total_marks']);
                                                    $gpa = getGPA($mark_value, $subject['total_marks']);
                                                    echo $grade . ' (' . number_format($gpa, 2) . ')';
                                                }
                                                ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="empty-mark">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php endforeach; ?>
                                        <td class="text-center fw-bold"><?php echo intval($result['total_obtained']); ?></td>
                                        <td class="text-center"><?php echo intval($result['percentage']); ?>%</td>
                                        <td class="text-center <?php echo ($result['result_status'] == 'Pass') ? 'pass' : 'fail'; ?> fw-bold">
                                            <?php echo $result['grade']; ?>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        </div>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="signature-section">
                        <div class="signature-box">
                            <div class="signature-line">শিক্ষক স্বাক্ষর</div>
                        </div>
                        <div class="signature-box">
                            <div class="signature-line">প্রধান শিক্ষক স্বাক্ষর</div>
                        </div>
                    </div>
                </div>
                <?php elseif ($class_id > 0 && $exam_id > 0): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i> 
                    <?php if (empty($students)): ?>
                        নির্বাচিত ক্লাসে কোন শিক্ষার্থী নেই। প্রথমে <a href="students.php">শিক্ষার্থী যোগ করুন</a>।
                    <?php elseif (empty($subjects)): ?>
                        নির্বাচিত পরীক্ষার জন্য কোন বিষয় নেই। প্রথমে <a href="exam_subject_assign.php?exam_id=<?php echo $exam_id; ?>">বিষয় বরাদ্দ করুন</a>।
                    <?php else: ?>
                        নির্বাচিত পরীক্ষা এবং ক্লাসের জন্য কোন নম্বর পাওয়া যায়নি। প্রথমে <a href="marks_entry.php">নম্বর এন্ট্রি</a> করুন।
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Sync exam and class selection
            $('#exam_id').change(function() {
                var classId = $(this).find(':selected').data('class');
                $('#class_id').val(classId);
            });
            
            // Handle fixed columns on scroll
            $('.table-responsive').on('scroll', function() {
                var leftPos = $(this).scrollLeft();
                $('.fixed-column, .fixed-column-2').css({
                    'left': leftPos
                });
            });
            
            // Auto-hide success messages after 3 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 3000);
        });
        
        // Function to open print options modal
        function printTabulation() {
            $('#printOptionsModal').modal('show');
        }
        
        // Function to print based on selected option
        function printTabulationSheet(size) {
            $('#printOptionsModal').modal('hide');
            
            // Small delay to allow modal to close before print
            setTimeout(function() {
                // Set up printing options
                var originalTitle = document.title;
                
                // Set title with exam name for better identification in print dialog
                if (document.querySelector('.exam-details h4')) {
                    document.title = document.querySelector('.exam-details h4').innerText + ' - টেবুলেশন শীট';
                }
                
                window.print();
                
                // Restore original title
                document.title = originalTitle;
            }, 300);
        }
    </script>
</body>
</html> 