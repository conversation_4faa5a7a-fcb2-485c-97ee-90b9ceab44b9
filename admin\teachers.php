<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Create teachers table if it doesn't exist
$teachersTableQuery = "CREATE TABLE IF NOT EXISTS teachers (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    teacher_id VARCHAR(20) NOT NULL UNIQUE,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    department_id INT(11),
    subject VARCHAR(100),
    designation VARCHAR(100),
    joining_date DATE,
    profile_photo VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";
$conn->query($teachersTableQuery);

// Add department_id column if it doesn't exist
$checkDeptIdColumnQuery = "SHOW COLUMNS FROM teachers LIKE 'department_id'";
$deptIdColumnResult = $conn->query($checkDeptIdColumnQuery);
if ($deptIdColumnResult->num_rows == 0) {
    $addDeptIdColumnQuery = "ALTER TABLE teachers ADD COLUMN department_id INT(11) NULL";
    $conn->query($addDeptIdColumnQuery);
}

// Add subject column if it doesn't exist
$checkSubjectColumnQuery = "SHOW COLUMNS FROM teachers LIKE 'subject'";
$subjectColumnResult = $conn->query($checkSubjectColumnQuery);
if ($subjectColumnResult->num_rows == 0) {
    $addSubjectColumnQuery = "ALTER TABLE teachers ADD COLUMN subject VARCHAR(100) NULL";
    $conn->query($addSubjectColumnQuery);
}

// Add designation column if it doesn't exist
$checkDesignationColumnQuery = "SHOW COLUMNS FROM teachers LIKE 'designation'";
$designationColumnResult = $conn->query($checkDesignationColumnQuery);
if ($designationColumnResult->num_rows == 0) {
    $addDesignationColumnQuery = "ALTER TABLE teachers ADD COLUMN designation VARCHAR(100) NULL";
    $conn->query($addDesignationColumnQuery);
}

// Add joining_date column if it doesn't exist
$checkJoiningDateColumnQuery = "SHOW COLUMNS FROM teachers LIKE 'joining_date'";
$joiningDateColumnResult = $conn->query($checkJoiningDateColumnQuery);
if ($joiningDateColumnResult->num_rows == 0) {
    $addJoiningDateColumnQuery = "ALTER TABLE teachers ADD COLUMN joining_date DATE NULL";
    $conn->query($addJoiningDateColumnQuery);
}

// Handle success and error messages
$success_msg = '';
$error_msg = '';

// Handle teacher addition
if (isset($_POST['add_teacher'])) {
    $teacherId = $conn->real_escape_string($_POST['teacher_id']);
    $firstName = $conn->real_escape_string($_POST['first_name']);
    $lastName = $conn->real_escape_string($_POST['last_name']);
    $email = $conn->real_escape_string($_POST['email'] ?? '');
    $phone = $conn->real_escape_string($_POST['phone'] ?? '');
    $departmentId = $_POST['department_id'] ?? null;
    $subject = $conn->real_escape_string($_POST['subject'] ?? '');
    $designation = $conn->real_escape_string($_POST['designation'] ?? '');
    $joiningDate = $conn->real_escape_string($_POST['joining_date'] ?? '');
    
    if (empty($teacherId) || empty($firstName) || empty($lastName)) {
        $error_msg = "শিক্ষকের আইডি, নাম এবং পদবি আবশ্যক";
    } else {
        $insertQuery = "INSERT INTO teachers (teacher_id, first_name, last_name, email, phone, department_id, subject, designation, joining_date) 
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("sssssisss", $teacherId, $firstName, $lastName, $email, $phone, $departmentId, $subject, $designation, $joiningDate);
        
        if ($stmt->execute()) {
            $success_msg = "শিক্ষক সফলভাবে যোগ করা হয়েছে";
        } else {
            $error_msg = "শিক্ষক যোগ করতে সমস্যা হয়েছে: " . $conn->error;
        }
    }
}

// Handle teacher deletion
if (isset($_GET['delete'])) {
    $teacherId = $_GET['delete'];
    $deleteQuery = "DELETE FROM teachers WHERE id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $teacherId);
    
    if ($stmt->execute()) {
        $success_msg = "শিক্ষক সফলভাবে মুছে ফেলা হয়েছে";
    } else {
        $error_msg = "শিক্ষক মুছে ফেলতে সমস্যা হয়েছে: " . $conn->error;
    }
}

// Get all teachers with department information
$teachersQuery = "SELECT t.*, d.department_name 
                 FROM teachers t 
                 LEFT JOIN departments d ON t.department_id = d.id 
                 ORDER BY t.first_name, t.last_name";
$teachers = $conn->query($teachersQuery);

// Get all departments for the dropdown
$departmentsQuery = "SELECT * FROM departments ORDER BY department_name";
$departments = $conn->query($departmentsQuery);
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষক ব্যবস্থাপনা | অ্যাডমিন ড্যাশবোর্ড</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            display: block;
        }
        .sidebar a:hover {
            background-color: #495057;
        }
        .active {
            background-color: #0d6efd;
        }
        .content {
            margin-left: 220px;
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar">
                <h2 class="text-white text-center mb-4">অ্যাডমিন প্যানেল</h2>
                <a href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড</a>
                <a href="students.php"><i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী</a>
                <a href="teachers.php" class="active"><i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক</a>
                <a href="sessions.php"><i class="fas fa-calendar-alt me-2"></i> সেশন</a>
                <a href="classes.php"><i class="fas fa-chalkboard me-2"></i> ক্লাস</a>
                <a href="departments.php"><i class="fas fa-building me-2"></i> বিভাগ</a>
                <a href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i> লগআউট</a>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-10 content">
                <h1 class="mb-4">শিক্ষক ব্যবস্থাপনা</h1>
                
                <!-- Success and Error Messages -->
                <?php if (!empty($success_msg)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($error_msg)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <div class="row">
                    <!-- Add Teacher Form -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">নতুন শিক্ষক যোগ করুন</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" action="teachers.php">
                                    <div class="mb-3">
                                        <label for="teacher_id" class="form-label">শিক্ষক আইডি*</label>
                                        <input type="text" class="form-control" id="teacher_id" name="teacher_id" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="first_name" class="form-label">নাম*</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="last_name" class="form-label">পদবি*</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">ইমেইল</label>
                                        <input type="email" class="form-control" id="email" name="email">
                                    </div>
                                    <div class="mb-3">
                                        <label for="phone" class="form-label">ফোন নম্বর</label>
                                        <input type="text" class="form-control" id="phone" name="phone">
                                    </div>
                                    <div class="mb-3">
                                        <label for="department_id" class="form-label">বিভাগ</label>
                                        <select class="form-select" id="department_id" name="department_id">
                                            <option value="">বিভাগ নির্বাচন করুন</option>
                                            <?php if ($departments && $departments->num_rows > 0): ?>
                                                <?php while ($dept = $departments->fetch_assoc()): ?>
                                                    <option value="<?php echo $dept['id']; ?>">
                                                        <?php echo htmlspecialchars($dept['department_name']); ?>
                                                    </option>
                                                <?php endwhile; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">বিষয়</label>
                                        <input type="text" class="form-control" id="subject" name="subject" placeholder="যেমন: বাংলা, ইংরেজি, গণিত">
                                    </div>
                                    <div class="mb-3">
                                        <label for="designation" class="form-label">পদবি</label>
                                        <input type="text" class="form-control" id="designation" name="designation">
                                    </div>
                                    <div class="mb-3">
                                        <label for="joining_date" class="form-label">যোগদানের তারিখ</label>
                                        <input type="date" class="form-control" id="joining_date" name="joining_date">
                                    </div>
                                    <button type="submit" name="add_teacher" class="btn btn-primary">শিক্ষক যোগ করুন</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Teachers List -->
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">শিক্ষক তালিকা</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="table-light">
                                            <tr>
                                                <th>শিক্ষক আইডি</th>
                                                <th>নাম</th>
                                                <th>বিভাগ</th>
                                                <th>বিষয়</th>
                                                <th>পদবি</th>
                                                <th>ফোন</th>
                                                <th>অ্যাকশন</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($teachers && $teachers->num_rows > 0): ?>
                                                <?php while ($teacher = $teachers->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($teacher['teacher_id']); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['subject'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['designation'] ?? 'N/A'); ?></td>
                                                        <td><?php echo htmlspecialchars($teacher['phone'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <a href="edit_teacher.php?id=<?php echo $teacher['id']; ?>" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <a href="teachers.php?delete=<?php echo $teacher['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('আপনি কি নিশ্চিত যে আপনি এই শিক্ষককে মুছে ফেলতে চান?')">
                                                                <i class="fas fa-trash"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="7" class="text-center">কোন শিক্ষক পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 