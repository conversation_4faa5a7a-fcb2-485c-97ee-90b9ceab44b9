<?php
session_start();
require_once '../includes/dbh.inc.php';

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Include header
include_once 'includes/header.php';

// Get all fees with some basic info for demonstration
$feesQuery = "SELECT f.id, f.fee_type, f.amount, f.paid, f.payment_status, f.student_id, 
             s.first_name, s.last_name, s.student_id as roll, c.class_name
             FROM fees f
             LEFT JOIN students s ON f.student_id = s.id
             LEFT JOIN classes c ON s.class_id = c.id
             WHERE f.payment_status != 'paid'
             ORDER BY f.id DESC
             LIMIT 20";
$feesResult = $conn->query($feesQuery);

// Check payment_date column in fees table
$checkColumn = "SHOW COLUMNS FROM fees LIKE 'payment_date'";
$columnExists = $conn->query($checkColumn)->num_rows > 0;

// Add payment_date column if it doesn't exist
if (!$columnExists) {
    $addColumn = "ALTER TABLE fees ADD COLUMN payment_date DATE NULL";
    $addResult = $conn->query($addColumn);
    $columnAddResult = $addResult ? "Successfully added payment_date column." : "Failed to add payment_date column: " . $conn->error;
} else {
    $columnAddResult = "payment_date column already exists.";
}

// Check fee_payments table
$checkPaymentsTable = "SHOW TABLES LIKE 'fee_payments'";
$paymentsTableExists = $conn->query($checkPaymentsTable)->num_rows > 0;

// Create fee_payments table if it doesn't exist
if (!$paymentsTableExists) {
    $createPaymentsTable = "CREATE TABLE IF NOT EXISTS fee_payments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        fee_id INT(11) NOT NULL,
        receipt_id INT(11) NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NULL,
        notes TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $createResult = $conn->query($createPaymentsTable);
    $tableCreateResult = $createResult ? "Successfully created fee_payments table." : "Failed to create fee_payments table: " . $conn->error;
} else {
    $tableCreateResult = "fee_payments table already exists.";
}

// Check payment_receipts table
$checkReceiptsTable = "SHOW TABLES LIKE 'payment_receipts'";
$receiptsTableExists = $conn->query($checkReceiptsTable)->num_rows > 0;

// Create payment_receipts table if it doesn't exist
if (!$receiptsTableExists) {
    $createReceiptsTable = "CREATE TABLE IF NOT EXISTS payment_receipts (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        payment_date DATE NOT NULL,
        payment_method VARCHAR(50) NOT NULL,
        total_amount DECIMAL(10,2) NOT NULL,
        receipt_no VARCHAR(50) NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $createResult = $conn->query($createReceiptsTable);
    $receiptsTableCreateResult = $createResult ? "Successfully created payment_receipts table." : "Failed to create payment_receipts table: " . $conn->error;
} else {
    $receiptsTableCreateResult = "payment_receipts table already exists.";
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12 my-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">ফি পেমেন্ট টেস্ট পেজ</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6>Database Structure Results:</h6>
                        <ul>
                            <li>fees table payment_date column: <?php echo $columnAddResult; ?></li>
                            <li>fee_payments table: <?php echo $tableCreateResult; ?></li>
                            <li>payment_receipts table: <?php echo $receiptsTableCreateResult; ?></li>
                        </ul>
                    </div>
                    
                    <h6>পেমেন্ট করুন</h6>
                    <p>নীচে তালিকা থেকে একটি ফি নির্বাচন করুন এবং পেমেন্ট প্রক্রিয়া শুরু করুন।</p>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ফি আইডি</th>
                                    <th>শিক্ষার্থী</th>
                                    <th>ক্লাস</th>
                                    <th>ফি ধরন</th>
                                    <th>মোট</th>
                                    <th>পরিশোধিত</th>
                                    <th>বাকি</th>
                                    <th>স্ট্যাটাস</th>
                                    <th>অ্যাকশন</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($feesResult && $feesResult->num_rows > 0): ?>
                                    <?php while ($fee = $feesResult->fetch_assoc()): ?>
                                        <tr>
                                            <td><?php echo $fee['id']; ?></td>
                                            <td><?php echo $fee['first_name'] . ' ' . $fee['last_name'] . ' (' . $fee['roll'] . ')'; ?></td>
                                            <td><?php echo $fee['class_name']; ?></td>
                                            <td><?php echo $fee['fee_type']; ?></td>
                                            <td><?php echo $fee['amount']; ?></td>
                                            <td><?php echo $fee['paid']; ?></td>
                                            <td><?php echo ($fee['amount'] - $fee['paid']); ?></td>
                                            <td><?php echo $fee['payment_status']; ?></td>
                                            <td>
                                                <button
                                                    class="btn btn-sm btn-primary pay-button"
                                                    data-fee-id="<?php echo $fee['id']; ?>"
                                                    data-student-id="<?php echo $fee['student_id']; ?>"
                                                    data-fee-type="<?php echo $fee['fee_type']; ?>"
                                                    data-amount="<?php echo $fee['amount']; ?>"
                                                    data-paid="<?php echo $fee['paid']; ?>"
                                                    data-due="<?php echo ($fee['amount'] - $fee['paid']); ?>"
                                                >
                                                    পেমেন্ট করুন
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">কোন ফি রেকর্ড পাওয়া যায়নি।</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">ফি পেমেন্ট করুন</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="paymentForm">
                <div class="modal-body">
                    <input type="hidden" id="fee_id" name="fee_id">
                    <input type="hidden" id="action" name="action" value="collect_fee_payment">
                    
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">পেমেন্ট পরিমাণ</label>
                        <input type="number" class="form-control" id="payment_amount" name="payment_amount" min="1" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">পেমেন্ট তারিখ</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">পেমেন্ট পদ্ধতি</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="cash">নগদ</option>
                            <option value="bank">ব্যাংক ট্রান্সফার</option>
                            <option value="bkash">বিকাশ</option>
                            <option value="nagad">নগদ</option>
                            <option value="rocket">রকেট</option>
                            <option value="other">অন্যান্য</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="receipt_no" class="form-label">রসিদ নম্বর (ঐচ্ছিক)</label>
                        <input type="text" class="form-control" id="receipt_no" name="receipt_no">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">নোট (ঐচ্ছিক)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                    
                    <div id="paymentResponseMessage"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">বাতিল</button>
                    <button type="submit" class="btn btn-primary">পেমেন্ট করুন</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Handle payment button click
        $('.pay-button').click(function() {
            const feeId = $(this).data('fee-id');
            const maxAmount = $(this).data('due');
            
            // Set values in the modal
            $('#fee_id').val(feeId);
            $('#payment_amount').val(maxAmount);
            $('#payment_amount').attr('max', maxAmount);
            
            // Show the modal
            $('#paymentModal').modal('show');
        });
        
        // Handle payment form submission
        $('#paymentForm').submit(function(e) {
            e.preventDefault();
            
            // Clear previous messages
            $('#paymentResponseMessage').html('');
            
            // Get form data
            const formData = $(this).serialize();
            
            // Submit payment via AJAX
            $.ajax({
                url: 'ajax_handler.php',
                type: 'POST',
                data: formData,
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#paymentResponseMessage').html('<div class="alert alert-success">পেমেন্ট সফলভাবে সম্পন্ন হয়েছে!</div>');
                        
                        // Reload the page after 2 seconds
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        $('#paymentResponseMessage').html('<div class="alert alert-danger">পেমেন্ট প্রক্রিয়াকরণে সমস্যা: ' + response.message + '</div>');
                    }
                },
                error: function() {
                    $('#paymentResponseMessage').html('<div class="alert alert-danger">সার্ভারে সমস্যা! পরে আবার চেষ্টা করুন।</div>');
                }
            });
        });
    });
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?> 