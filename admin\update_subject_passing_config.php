<?php
session_start();
require_once('../includes/dbh.inc.php');
require_once('includes/functions.php');

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php#login-section");
    exit();
}

$success_message = '';
$error_message = '';

// Create subject_passing_config table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS subject_passing_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    subject_id INT NOT NULL,
    cq_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    mcq_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    practical_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    overall_pass_percentage DECIMAL(5,2) NOT NULL DEFAULT 33.00,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY(subject_id)
)";
$conn->query($create_table_sql);

// Get all subjects
$subjects_sql = "SELECT * FROM subjects ORDER BY subject_name";
$subjects_result = $conn->query($subjects_sql);
$subjects = [];
while ($subject = $subjects_result->fetch_assoc()) {
    $subjects[$subject['id']] = $subject;
}

// Check if form submitted for migration
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['migrate_config'])) {
    try {
        $conn->begin_transaction();
        
        // Get all unique subject_ids from passing_marks_config where subject_id is not null
        $subject_ids_sql = "SELECT DISTINCT subject_id FROM passing_marks_config WHERE subject_id IS NOT NULL";
        $subject_ids_result = $conn->query($subject_ids_sql);
        $subject_ids = [];
        
        while ($row = $subject_ids_result->fetch_assoc()) {
            $subject_ids[] = $row['subject_id'];
        }
        
        // Get default values
        $default_sql = "SELECT cq_passing_percent, mcq_passing_percent, practical_passing_percent, passing_mark 
                      FROM passing_marks_config 
                      WHERE subject_id IS NULL 
                      ORDER BY min_percentage DESC LIMIT 1";
        $default_result = $conn->query($default_sql);
        $default = $default_result->fetch_assoc();
        
        $default_cq = $default['cq_passing_percent'] ?? 33.00;
        $default_mcq = $default['mcq_passing_percent'] ?? 33.00;
        $default_practical = $default['practical_passing_percent'] ?? 33.00;
        $default_overall = $default['passing_mark'] ?? 33.00;
        
        // Clear existing data in subject_passing_config
        $clear_sql = "DELETE FROM subject_passing_config";
        $conn->query($clear_sql);
        
        // Insert records for each subject with values from passing_marks_config
        $insert_sql = "INSERT INTO subject_passing_config 
                     (subject_id, cq_pass_percentage, mcq_pass_percentage, practical_pass_percentage, overall_pass_percentage, is_active) 
                     VALUES (?, ?, ?, ?, ?, 1)";
        $insert_stmt = $conn->prepare($insert_sql);
        
        // For subject-specific configs
        foreach ($subject_ids as $subject_id) {
            if (!isset($subjects[$subject_id])) continue; // Skip if subject doesn't exist anymore
            
            // Get passing percentages from passing_marks_config
            $config_sql = "SELECT cq_passing_percent, mcq_passing_percent, practical_passing_percent, passing_mark 
                           FROM passing_marks_config 
                           WHERE subject_id = ? 
                           ORDER BY min_percentage DESC LIMIT 1";
            $config_stmt = $conn->prepare($config_sql);
            $config_stmt->bind_param("i", $subject_id);
            $config_stmt->execute();
            $config_result = $config_stmt->get_result();
            
            if ($config_result->num_rows > 0) {
                $config = $config_result->fetch_assoc();
                $cq_percent = $config['cq_passing_percent'] ?? $default_cq;
                $mcq_percent = $config['mcq_passing_percent'] ?? $default_mcq;
                $practical_percent = $config['practical_passing_percent'] ?? $default_practical;
                $overall_percent = $config['passing_mark'] ?? $default_overall;
                
                $insert_stmt->bind_param("idddd", $subject_id, $cq_percent, $mcq_percent, $practical_percent, $overall_percent);
                $insert_stmt->execute();
            }
        }
        
        // For subjects without specific configs, insert default values
        foreach ($subjects as $subject_id => $subject) {
            // Skip subjects that already have a config
            if (in_array($subject_id, $subject_ids)) continue;
            
            $insert_stmt->bind_param("idddd", $subject_id, $default_cq, $default_mcq, $default_practical, $default_overall);
            $insert_stmt->execute();
        }
        
        $conn->commit();
        $success_message = "কনফিগারেশন সফলভাবে মাইগ্রেট করা হয়েছে। সর্বমোট " . count($subjects) . " টি বিষয়ের জন্য কনফিগারেশন সেট করা হয়েছে।";
        
    } catch (Exception $e) {
        $conn->rollback();
        $error_message = "ত্রুটি: " . $e->getMessage();
    }
}

// Count existing configs
$count_sql = "SELECT COUNT(*) as count FROM subject_passing_config";
$count_result = $conn->query($count_sql);
$config_count = $count_result->fetch_assoc()['count'];
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>আপডেট পাসিং মার্কস কনফিগারেশন - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kalpurush', Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .config-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include('includes/sidebar.php'); ?>
            
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">আপডেট পাসিং মার্কস কনফিগারেশন</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="subject_passing_config.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-wrench me-2"></i>বিষয় ভিত্তিক পাসিং কনফিগ
                            </a>
                            <a href="passing_marks_config.php" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-cogs me-2"></i>গ্রেডিং কনফিগারেশন
                            </a>
                        </div>
                    </div>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="alert alert-warning mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>মাইগ্রেশন টুল:</strong> এই টুলটি আপনাকে পুরাতন পাসিং মার্কস কনফিগারেশন থেকে নতুন বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশনে মাইগ্রেট করতে সাহায্য করবে।
                </div>
                
                <div class="config-card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-sync me-2"></i> কনফিগারেশন আপডেট টুল</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>বর্তমান অবস্থা</h4>
                                <p><strong>মোট বিষয়:</strong> <?php echo count($subjects); ?></p>
                                <p><strong>বিষয় ভিত্তিক কনফিগারেশন:</strong> <?php echo $config_count; ?></p>
                                
                                <?php if ($config_count < count($subjects)): ?>
                                    <div class="alert alert-info">
                                        আপনি <?php echo count($subjects) - $config_count; ?> টি বিষয়ের কনফিগারেশন যোগ করতে পারেন।
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-6">
                                <form method="post" action="">
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>সতর্কতা:</strong> এই পদক্ষেপটি আপনার সমস্ত বিষয়ের জন্য নতুন বিষয় ভিত্তিক পাসিং মার্কস কনফিগারেশন সেট করবে। বিদ্যমান কনফিগারেশনগুলি ওভাররাইট করা হবে।
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" name="migrate_config" class="btn btn-primary">
                                            <i class="fas fa-sync-alt me-2"></i>শুরু করুন কনফিগারেশন মাইগ্রেশন
                                        </button>
                                        <a href="subject_passing_config.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>বাতিল করুন
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 mb-5 p-4 bg-white rounded shadow-sm">
                    <h4>কনফিগারেশন মাইগ্রেশন সম্পর্কে</h4>
                    <p>এই টুলটি নিম্নলিখিত কাজগুলি করবে:</p>
                    <ol>
                        <li>পুরাতন পাসিং মার্কস কনফিগারেশন থেকে বিষয় ভিত্তিক ডেটা নিয়ে আসবে।</li>
                        <li>প্রতিটি বিষয়ের জন্য সিকিউ, এমসিকিউ, ব্যবহারিক এবং মোট পাসিং শতাংশ সেট করবে।</li>
                        <li>যদি কোন বিষয়ের জন্য স্পেসিফিক কনফিগারেশন না থাকে, তাহলে ডিফল্ট ভ্যালু থেকে সেট করবে।</li>
                    </ol>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        মাইগ্রেশন সম্পন্ন হওয়ার পর, আপনি "বিষয় ভিত্তিক পাসিং কনফিগ" পেজে গিয়ে কনফিগারেশনগুলি পরিবর্তন করতে পারবেন।
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Auto-hide success messages after 5 seconds
            setTimeout(function() {
                $('.alert-success').fadeOut('slow');
            }, 5000);
        });
    </script>
</body>
</html> 