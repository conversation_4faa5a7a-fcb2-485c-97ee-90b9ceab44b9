<?php
// Initialize the session
session_start();

// Check if user is logged in and is an admin (checking multiple possible session variables)
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    // Check other possible session variables
    if (!isset($_SESSION['admin_id']) && !isset($_SESSION['loggedin'])) {
        header("location: ../index.php");
        exit();
    }
}

// Include database connection file
require_once "../includes/dbh.inc.php";

// Debug Session (uncomment if needed)
// echo "<pre>"; print_r($_SESSION); echo "</pre>"; exit;

// Check if ID parameter exists
if(!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: fees.php");
    exit;
}

$feeId = $_GET['id'];

// Prepare SQL query to fetch fee details
$sql = "SELECT f.*, s.first_name, s.last_name, s.student_id, c.class_name 
        FROM fees f
        JOIN students s ON f.student_id = s.id
        JOIN classes c ON s.class_id = c.id
        WHERE f.id = ?";

// Prepare statement
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $feeId);
$stmt->execute();
$result = $stmt->get_result();

// If no fee found with the given ID
if($result->num_rows == 0) {
    header("Location: fees.php");
    exit;
}

// Get fee details
$fee = $result->fetch_assoc();

// Fetch payment history
$paymentsSQL = "SELECT * FROM fee_payments WHERE fee_id = ? ORDER BY payment_date DESC";
$paymentStmt = $conn->prepare($paymentsSQL);
$paymentStmt->bind_param("i", $feeId);
$paymentStmt->execute();
$paymentsResult = $paymentStmt->get_result();

// Set page title
$pageTitle = "ফি বিবরণ";

// Include direct HTML header instead of the missing header.php
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - স্কুল ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding-top: 20px;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
            color: white;
            padding-top: 20px;
        }
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.75);
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .main-content {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar (reusing the sidebar from dashboard.php) -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">

<!-- Page Content -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-file-invoice-dollar me-2"></i> ফি বিবরণ</h5>
                        <div>
                            <a href="fees.php" class="btn btn-light">
                                <i class="fas fa-arrow-left me-1"></i> ফিরে যান
                            </a>
                            <a href="edit_fee.php?id=<?= $feeId ?>" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i> সম্পাদনা করুন
                            </a>
                            <?php if($fee['payment_status'] != 'paid'): ?>
                                <a href="collect_fee.php?id=<?= $feeId ?>" class="btn btn-success">
                                    <i class="fas fa-hand-holding-usd me-1"></i> পেমেন্ট নিন
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i> শিক্ষার্থী এবং ফি তথ্য</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>শিক্ষার্থী আইডি:</strong>
                                            <span><?= htmlspecialchars($fee['student_id']) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>শিক্ষার্থী নাম:</strong>
                                            <span><?= htmlspecialchars($fee['first_name'] . ' ' . $fee['last_name']) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>ক্লাস:</strong>
                                            <span><?= htmlspecialchars($fee['class_name']) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>ফি টাইপ:</strong>
                                            <span><?= htmlspecialchars($fee['fee_type']) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>মোট পরিমাণ:</strong>
                                            <span>৳ <?= number_format($fee['amount'], 2) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>পরিশোধিত:</strong>
                                            <span>৳ <?= number_format($fee['paid'], 2) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>বকেয়া:</strong>
                                            <span>৳ <?= number_format($fee['amount'] - $fee['paid'], 2) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>শেষ তারিখ:</strong>
                                            <span><?= date('d/m/Y', strtotime($fee['due_date'])) ?></span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <strong>পেমেন্ট স্ট্যাটাস:</strong>
                                            <span>
                                                <?php
                                                    if($fee['payment_status'] == 'paid') {
                                                        echo '<span class="badge bg-success">পরিশোধিত</span>';
                                                    } elseif($fee['payment_status'] == 'partial') {
                                                        echo '<span class="badge bg-warning">আংশিক</span>';
                                                    } else {
                                                        echo '<span class="badge bg-danger">বকেয়া</span>';
                                                    }
                                                ?>
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="fas fa-history me-2"></i> পেমেন্ট ইতিহাস</h6>
                                </div>
                                <div class="card-body">
                                    <?php if($paymentsResult->num_rows > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover table-striped">
                                                <thead class="table-light">
                                                    <tr>
                                                        <th>তারিখ</th>
                                                        <th>পরিমাণ</th>
                                                        <th>পেমেন্ট পদ্ধতি</th>
                                                        <th>রিসিট নং</th>
                                                        <th>নোট</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php while($payment = $paymentsResult->fetch_assoc()): ?>
                                                        <tr>
                                                            <td><?= date('d/m/Y', strtotime($payment['payment_date'])) ?></td>
                                                            <td>৳ <?= number_format($payment['amount'], 2) ?></td>
                                                            <td><?= htmlspecialchars($payment['payment_method'] ?? '-') ?></td>
                                                            <td><?= htmlspecialchars($payment['receipt_number'] ?? '-') ?></td>
                                                            <td><?= htmlspecialchars($payment['notes'] ?? '-') ?></td>
                                                        </tr>
                                                    <?php endwhile; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-info">কোন পেমেন্ট রেকর্ড পাওয়া যায়নি।</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Custom footer instead of includes/footer.php -->
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize any plugins or add any script functionality here
        });
    </script>
</body>
</html> 