# অ্যাপ বিল্ড করার নির্দেশাবলী

## প্রয়োজনীয় প্যাকেজ ইনস্টল করুন
```
# প্লাগিন ইনস্টল করুন
cordova plugin add cordova-plugin-whitelist
cordova plugin add cordova-plugin-network-information
cordova plugin add cordova-plugin-inappbrowser
cordova plugin add cordova-plugin-splashscreen

# কিছু আইকন ও স্প্ল্যাশ স্ক্রিন যোগ করুন (প্রয়োজনে)
cordova-res android --skip-config --copy
```

## ১. ওয়েবসাইট URL আপডেট করুন

- `js/index.js` ফাইলে `yourwebsite.com/zfaw` পরিবর্তন করে আপনার সঠিক ওয়েবসাইট URL যোগ করুন
- `config.xml` ফাইলে `content src`, `allow-navigation` এবং `author` সেকশনগুলি আপডেট করুন

## ২. অ্যান্ড্রয়েড অ্যাপ বিল্ড করুন

```
cordova build android
```

এটি `platforms/android/app/build/outputs/apk/debug/app-debug.apk` এ একটি APK ফাইল তৈরি করবে

## ৩. রিলিজ ভার্সন তৈরি করুন

```
cordova build android --release
```

## ৪. সাইন করা APK তৈরি করুন
কমান্ড প্রম্পট/টার্মিনাল ওপেন করে নিম্নলিখিত কমান্ড চালান:

```
# কি-স্টোর তৈরি করুন (যদি না থাকে)
keytool -genkey -v -keystore zfaw-release-key.keystore -alias zfaw -keyalg RSA -keysize 2048 -validity 10000

# APK সাইন করুন
jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore zfaw-release-key.keystore platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk zfaw

# APK অপ্টিমাইজ করুন
zipalign -v 4 platforms/android/app/build/outputs/apk/release/app-release-unsigned.apk zfaw-app.apk
```

## ৫. অ্যাপ ইনস্টল ও পরীক্ষা করুন

```
# ডিবাগ ভার্সন ইনস্টল করুন (USB দিয়ে কানেক্টেড ডিভাইসে)
adb install -r platforms/android/app/build/outputs/apk/debug/app-debug.apk

# অথবা রিলিজ ভার্সন ইনস্টল করুন
adb install -r zfaw-app.apk
```

## ৬. Google Play Store এ আপলোড করার প্রস্তুতি

- সম্পূর্ণ অ্যাপ আইকন সেট তৈরি করুন
- উপযুক্ত স্ক্রিনশট তৈরি করুন (বিভিন্ন ডিভাইস সাইজে)
- Privacy Policy তৈরি করুন
- Google Play Developer Account খুলুন (একবার $25 ফি)
- Google Play Console এ সম্পূর্ণ অ্যাপের বিবরণ দিয়ে আপলোড করুন 