* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.app {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
    padding: 20px;
}

#loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

h1 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #007bff;
}

h2 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #dc3545;
}

p {
    margin-bottom: 20px;
    font-size: 1rem;
    color: #6c757d;
}

#offline-message {
    text-align: center;
    padding: 20px;
}

#retry-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 1rem;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#retry-button:hover {
    background-color: #0056b3;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #121212;
        color: #f0f0f0;
    }
    
    h1 {
        color: #4da9ff;
    }
    
    p {
        color: #bbbbbb;
    }
}

:root {
    --primary-color: #3498db;
    --secondary-color: #2980b9;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

.sidebar {
    background-color: var(--dark-color);
    color: white;
    height: 100vh;
    position: fixed;
    padding-top: 20px;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    margin-bottom: 5px;
    border-radius: 5px;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

.btn {
    border-radius: 5px;
    padding: 8px 16px;
    transition: all 0.3s;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.dashboard-stats {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    margin: 10px;
    padding: 20px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
    color: white;
}

.stat-students {
    background-color: var(--primary-color);
}

.stat-teachers {
    background-color: var(--success-color);
}

.stat-courses {
    background-color: var(--warning-color);
}

.stat-fees {
    background-color: var(--danger-color);
}

.stat-info h3 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.stat-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* Form styles */
.form-control {
    border-radius: 5px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    border-color: #3498db;
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

/* Custom alert messages */
.alert {
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Profile styles */
.profile-header {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.profile-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-info h2 {
    margin-top: 0;
    font-weight: 600;
}

.profile-info p {
    color: #666;
    margin-bottom: 5px;
}

/* Login page specific styles */
.login-page {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-page .card {
    width: 400px;
    max-width: 90%;
} 