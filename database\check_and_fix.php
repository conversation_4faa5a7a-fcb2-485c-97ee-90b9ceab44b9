<?php
session_start();
require_once '../includes/dbh.inc.php';
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Styling
echo "<!DOCTYPE html>
<html>
<head>
    <title>সিস্টেম চেক ও মেরামত</title>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 20px; }
        .log { margin-bottom: 5px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>সিস্টেম চেক ও মেরামত</h1>
        <div class='card p-3 mb-3'>
            <div id='log'>";

// Log function
function log_message($message, $type = 'info') {
    $class = $type;
    echo "<div class='log $class'>$message</div>";
}

log_message("সিস্টেম চেক শুরু হচ্ছে...", "info");

// List of all required tables for the system
$requiredTables = [
    'users' => "CREATE TABLE IF NOT EXISTS users (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    'departments' => "CREATE TABLE IF NOT EXISTS departments (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        department_name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    'sessions' => "CREATE TABLE IF NOT EXISTS sessions (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        session_name VARCHAR(50) NOT NULL UNIQUE,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        is_active TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    'classes' => "CREATE TABLE IF NOT EXISTS classes (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        class_name VARCHAR(50) NOT NULL,
        department_id INT(11) NULL,
        section VARCHAR(20) DEFAULT 'A',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
    )",
    
    'students' => "CREATE TABLE IF NOT EXISTS students (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id VARCHAR(20) NOT NULL UNIQUE,
        roll_number VARCHAR(20) NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        email VARCHAR(100) NULL,
        phone VARCHAR(20) NULL,
        gender ENUM('male', 'female', 'other') NOT NULL,
        dob DATE NULL,
        address TEXT NULL,
        batch VARCHAR(20) NULL,
        admission_date DATE NULL,
        profile_photo VARCHAR(255) NULL,
        department_id INT(11) NULL,
        class_id INT(11) NULL,
        session_id INT(11) NULL,
        user_id INT(11) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
        FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )",
    
    'subjects' => "CREATE TABLE IF NOT EXISTS subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        subject_name VARCHAR(100) NOT NULL,
        subject_code VARCHAR(20) NOT NULL,
        department_id INT(11) NULL,
        category VARCHAR(255) DEFAULT 'required',
        description TEXT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
    )",
    
    'student_subjects' => "CREATE TABLE IF NOT EXISTS student_subjects (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        subject_id INT(11) NOT NULL,
        category VARCHAR(50) NOT NULL,
        selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        session_id INT(11) NULL,
        UNIQUE KEY(student_id, subject_id),
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
        FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
    )",
    
    'announcements' => "CREATE TABLE IF NOT EXISTS announcements (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        user_id INT(11) NULL,
        for_type VARCHAR(50) DEFAULT 'all',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    )",
    
    'exams' => "CREATE TABLE IF NOT EXISTS exams (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        exam_name VARCHAR(100) NOT NULL,
        course_name VARCHAR(100) NOT NULL,
        exam_date DATE NOT NULL,
        total_marks INT(11) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )",
    
    'results' => "CREATE TABLE IF NOT EXISTS results (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        exam_id INT(11) NOT NULL,
        marks_obtained FLOAT NOT NULL,
        grade VARCHAR(5) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE
    )",
    
    'fees' => "CREATE TABLE IF NOT EXISTS fees (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        student_id INT(11) NOT NULL,
        fee_type VARCHAR(50) NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        paid DECIMAL(10,2) DEFAULT 0,
        due_date DATE NOT NULL,
        payment_status ENUM('paid', 'partial', 'due') DEFAULT 'due',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE
    )",
    
    'courses' => "CREATE TABLE IF NOT EXISTS courses (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        course_name VARCHAR(100) NOT NULL,
        course_code VARCHAR(20) NOT NULL,
        department_id INT(11) NULL,
        credit_hours DECIMAL(4,2) NULL,
        description TEXT,
        is_active TINYINT(1) DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
    )"
];

// Check each table and create if not exists
foreach ($requiredTables as $table => $createQuery) {
    $tableExists = $conn->query("SHOW TABLES LIKE '$table'");
    
    if ($tableExists->num_rows > 0) {
        log_message("'$table' টেবিল ইতিমধ্যে বিদ্যমান।", "success");
    } else {
        log_message("'$table' টেবিল পাওয়া যায়নি, তৈরি করা হচ্ছে...", "warning");
        
        if ($conn->query($createQuery)) {
            log_message("'$table' টেবিল সফলভাবে তৈরি করা হয়েছে।", "success");
            
            // Add sample data for certain tables
            switch ($table) {
                case 'announcements':
                    $insertAnnouncement = "INSERT INTO announcements (title, content) 
                                          VALUES ('সিস্টেমে স্বাগতম', 'কলেজ ম্যানেজমেন্ট সিস্টেমে আপনাকে স্বাগতম। আপনি এখন থেকে এই সিস্টেম ব্যবহার করে সকল কার্যক্রম পরিচালনা করতে পারবেন।')";
                    if ($conn->query($insertAnnouncement)) {
                        log_message("ডেমো ঘোষণা সফলভাবে যোগ করা হয়েছে।", "success");
                    }
                    break;
                
                case 'exams':
                    $insertExam = "INSERT INTO exams (exam_name, course_name, exam_date, total_marks) 
                                  VALUES 
                                  ('মধ্যবর্তী পরীক্ষা', 'বাংলা সাহিত্য', '" . date('Y-m-d', strtotime('+10 days')) . "', 100),
                                  ('সেমেস্টার ফাইনাল', 'ইংরেজী ভাষা', '" . date('Y-m-d', strtotime('+30 days')) . "', 100)";
                    if ($conn->query($insertExam)) {
                        log_message("ডেমো পরীক্ষার তথ্য সফলভাবে যোগ করা হয়েছে।", "success");
                    }
                    break;
                
                case 'courses':
                    $coursesDept = $conn->query("SELECT id FROM departments LIMIT 1");
                    $deptId = 1;
                    if ($coursesDept && $coursesDept->num_rows > 0) {
                        $deptRow = $coursesDept->fetch_assoc();
                        $deptId = $deptRow['id'];
                    }
                    
                    $insertCourses = "INSERT INTO courses (course_name, course_code, department_id, credit_hours, description) 
                                     VALUES 
                                     ('বাংলা সাহিত্য', 'BNG101', $deptId, 3.0, 'বাংলা সাহিত্যের প্রাথমিক পাঠ'),
                                     ('ইংরেজী ভাষা', 'ENG101', $deptId, 3.0, 'ইংরেজী ভাষার প্রাথমিক পাঠ'),
                                     ('গণিত', 'MATH101', $deptId, 4.0, 'প্রাথমিক গণিত'),
                                     ('পদার্থবিজ্ঞান', 'PHY101', $deptId, 3.5, 'পদার্থবিজ্ঞানের মৌলিক ধারণা'),
                                     ('রসায়ন', 'CHEM101', $deptId, 3.5, 'রসায়নের মৌলিক ধারণা')";
                    if ($conn->query($insertCourses)) {
                        log_message("ডেমো কোর্স তথ্য সফলভাবে যোগ করা হয়েছে।", "success");
                    }
                    break;
            }
        } else {
            log_message("'$table' টেবিল তৈরি করতে সমস্যা: " . $conn->error, "error");
        }
    }
}

// Check users
$userCheckQuery = "SELECT COUNT(*) as count FROM users WHERE user_type = 'student'";
$result = $conn->query($userCheckQuery);
$row = $result->fetch_assoc();

if ($row['count'] == 0) {
    log_message("কোন শিক্ষার্থী ইউজার পাওয়া যায়নি, একটি ডেমো ইউজার তৈরি করা হচ্ছে...", "warning");
    
    // Create student user
    $username = "student";
    $password = password_hash("password123", PASSWORD_DEFAULT);
    $userType = "student";
    
    $insertUserQuery = "INSERT INTO users (username, password, user_type) VALUES ('$username', '$password', '$userType')";
    
    if ($conn->query($insertUserQuery)) {
        $userId = $conn->insert_id;
        log_message("শিক্ষার্থী ইউজার সফলভাবে তৈরি করা হয়েছে। ইউজারনেম: student, পাসওয়ার্ড: password123", "success");
        
        // Create department if needed
        $departmentId = null;
        $deptResult = $conn->query("SELECT id FROM departments LIMIT 1");
        
        if ($deptResult->num_rows == 0) {
            $insertDeptQuery = "INSERT INTO departments (department_name, description) VALUES ('সাধারণ বিভাগ', 'সকল শিক্ষার্থীদের জন্য')";
            if ($conn->query($insertDeptQuery)) {
                $departmentId = $conn->insert_id;
                log_message("ডিফল্ট বিভাগ তৈরি করা হয়েছে।", "success");
            }
        } else {
            $deptRow = $deptResult->fetch_assoc();
            $departmentId = $deptRow['id'];
        }
        
        // Check if student already exists
        $studentCheckQuery = "SELECT id FROM students WHERE user_id = $userId";
        $studentResult = $conn->query($studentCheckQuery);
        
        if ($studentResult->num_rows == 0) {
            $studentId = 'STU' . date('Y') . str_pad($userId, 4, '0', STR_PAD_LEFT);
            $insertStudentQuery = "INSERT INTO students (student_id, first_name, last_name, gender, department_id, user_id, email, phone, batch, admission_date) 
                                  VALUES ('$studentId', 'ডেমো', 'শিক্ষার্থী', 'male', $departmentId, $userId, '<EMAIL>', '01700000000', '২০২৩', '" . date('Y-m-d') . "')";
            
            if ($conn->query($insertStudentQuery)) {
                log_message("শিক্ষার্থী প্রোফাইল সফলভাবে তৈরি করা হয়েছে।", "success");
            } else {
                log_message("শিক্ষার্থী প্রোফাইল তৈরি করতে সমস্যা: " . $conn->error, "error");
            }
        }
    } else {
        log_message("শিক্ষার্থী ইউজার তৈরি করতে সমস্যা: " . $conn->error, "error");
    }
}

// Generate CSRF token in session if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    log_message("CSRF টোকেন তৈরি করা হয়েছে।", "success");
}

echo "</div>
        </div>
        <div class='alert alert-success'>
            <h4>সিস্টেম চেক ও মেরামত সম্পন্ন হয়েছে!</h4>
            <p>সব টেবিল চেক করা হয়েছে এবং প্রয়োজনীয় মেরামত করা হয়েছে। এখন আপনি সিস্টেম ব্যবহার করতে পারবেন।</p>
        </div>
        
        <div class='row mt-4'>
            <div class='col-md-4'>
                <div class='card mb-3'>
                    <div class='card-header bg-primary text-white'>
                        শিক্ষার্থী লগইন
                    </div>
                    <div class='card-body'>
                        <p><strong>ইউজারনেম:</strong> student</p>
                        <p><strong>পাসওয়ার্ড:</strong> password123</p>
                        <a href='../index.php' class='btn btn-primary'>লগইন পেজে যান</a>
                    </div>
                </div>
            </div>
            
            <div class='col-md-4'>
                <div class='card mb-3'>
                    <div class='card-header bg-success text-white'>
                        সাইট ন্যাভিগেশন
                    </div>
                    <div class='card-body'>
                        <a href='../index.php' class='btn btn-success mb-2'>হোম পেজ</a>
                        <a href='../student/dashboard.php' class='btn btn-success mb-2'>শিক্ষার্থী ড্যাশবোর্ড</a>
                        <a href='setup_batch.php' class='btn btn-success'>পূর্ণ সেটআপ রান করুন</a>
                    </div>
                </div>
            </div>
            
            <div class='col-md-4'>
                <div class='card mb-3'>
                    <div class='card-header bg-danger text-white'>
                        সাবধানতা
                    </div>
                    <div class='card-body'>
                        <p>যদি আপনার সিস্টেমে কোন সমস্যা থাকে, নিম্নোক্ত পদক্ষেপ নিন:</p>
                        <ol>
                            <li>পূর্ণ সেটআপ রান করুন</li>
                            <li>ব্রাউজার ক্যাশে ক্লিয়ার করুন</li>
                            <li>আবার লগইন করুন</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";

// Close connection
$conn->close();
?> 