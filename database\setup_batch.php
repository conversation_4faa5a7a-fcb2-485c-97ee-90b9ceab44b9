<?php
session_start();
require_once '../includes/dbh.inc.php';

// Setup log function
function log_message($message, $type = 'info') {
    $class = 'text-info';
    if ($type == 'success') $class = 'text-success';
    if ($type == 'error') $class = 'text-danger';
    if ($type == 'warning') $class = 'text-warning';
    
    echo "<div class='$class'>$message</div>";
}

echo "<!DOCTYPE html>
<html>
<head>
    <title>সিস্টেম সেটআপ</title>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { padding: 20px; }
        .log-container { 
            background-color: #f8f9fa; 
            padding: 15px; 
            border-radius: 5px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1 class='mb-4'>সিস্টেম সেটআপ পরিচালক</h1>
        <div class='log-container' id='log'>";

// 1. Check database connection
log_message("ডাটাবেস সংযোগ পরীক্ষা করা হচ্ছে...");
if ($conn->connect_error) {
    log_message("ডাটাবেস সংযোগ ব্যর্থ হয়েছে: " . $conn->connect_error, 'error');
    exit("</div></div></body></html>");
} else {
    log_message("ডাটাবেস সংযোগ সফল হয়েছে!", 'success');
}

// 2. Check and create required tables
log_message("প্রয়োজনীয় টেবিল পরীক্ষা করা হচ্ছে...");

// Users table
$usersTableQuery = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    user_type ENUM('admin', 'teacher', 'student', 'staff') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($usersTableQuery)) {
    log_message("users টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("users টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Sessions table
$sessionsTableQuery = "CREATE TABLE IF NOT EXISTS sessions (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    session_name VARCHAR(50) NOT NULL UNIQUE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($sessionsTableQuery)) {
    log_message("sessions টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("sessions টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Departments table
$departmentsTableQuery = "CREATE TABLE IF NOT EXISTS departments (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($conn->query($departmentsTableQuery)) {
    log_message("departments টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("departments টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Classes table
$classesTableQuery = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_name VARCHAR(50) NOT NULL,
    department_id INT(11) NULL,
    section VARCHAR(20) DEFAULT 'A',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL
)";

if ($conn->query($classesTableQuery)) {
    log_message("classes টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("classes টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Students table
$studentsTableQuery = "CREATE TABLE IF NOT EXISTS students (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id VARCHAR(20) NOT NULL UNIQUE,
    roll_number VARCHAR(20) NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    dob DATE NULL,
    address TEXT NULL,
    city VARCHAR(50) NULL,
    state VARCHAR(50) NULL,
    postal_code VARCHAR(20) NULL,
    country VARCHAR(50) NULL DEFAULT 'Bangladesh',
    admission_date DATE NULL,
    batch VARCHAR(20) NULL,
    profile_photo VARCHAR(255) NULL,
    department_id INT(11) NULL,
    class_id INT(11) NULL,
    session_id INT(11) NULL,
    user_id INT(11) NULL,
    guardian_name VARCHAR(100) NULL,
    guardian_relation VARCHAR(50) NULL,
    guardian_phone VARCHAR(20) NULL,
    guardian_email VARCHAR(100) NULL,
    guardian_address TEXT NULL,
    guardian_occupation VARCHAR(100) NULL,
    role VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($studentsTableQuery)) {
    log_message("students টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("students টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Subjects table
$subjectsTableQuery = "CREATE TABLE IF NOT EXISTS subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    subject_name VARCHAR(100) NOT NULL,
    subject_code VARCHAR(20) NOT NULL,
    department_id INT(11) NULL,
    category VARCHAR(255) DEFAULT 'required',
    description TEXT,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE
)";

if ($conn->query($subjectsTableQuery)) {
    log_message("subjects টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("subjects টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Student Subjects table
$studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(50) NOT NULL,
    selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id INT(11) NULL,
    UNIQUE KEY(student_id, subject_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
)";

if ($conn->query($studentSubjectsTableQuery)) {
    log_message("student_subjects টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("student_subjects টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// Announcements table
$announcementsTableQuery = "CREATE TABLE IF NOT EXISTS announcements (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    user_id INT(11) NULL,
    for_type VARCHAR(50) DEFAULT 'all',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
)";

if ($conn->query($announcementsTableQuery)) {
    log_message("announcements টেবিল তৈরি হয়েছে বা ইতিমধ্যে বিদ্যমান!", 'success');
    
    // Check if there are any announcements
    $checkAnnouncements = $conn->query("SELECT id FROM announcements LIMIT 1");
    if ($checkAnnouncements->num_rows == 0) {
        $insertDemoAnnouncement = "INSERT INTO announcements (title, content, for_type) 
                                 VALUES ('স্বাগতম', 'কলেজ ম্যানেজমেন্ট সিস্টেমে আপনাকে স্বাগতম। এই সিস্টেম ব্যবহার করে আপনি আপনার একাডেমিক তথ্য দেখতে পারবেন।', 'all')";
        
        if ($conn->query($insertDemoAnnouncement)) {
            log_message("ডেমো ঘোষণা সফলভাবে যোগ করা হয়েছে!", 'success');
        } else {
            log_message("ডেমো ঘোষণা যোগ করতে সমস্যা: " . $conn->error, 'error');
        }
    }
} else {
    log_message("announcements টেবিল তৈরি করতে সমস্যা: " . $conn->error, 'error');
}

// 3. Check for admin user
log_message("অ্যাডমিন ব্যবহারকারী পরীক্ষা করা হচ্ছে...");
$adminCheckQuery = "SELECT id FROM users WHERE user_type = 'admin'";
$adminResult = $conn->query($adminCheckQuery);

if ($adminResult && $adminResult->num_rows > 0) {
    log_message("অ্যাডমিন ব্যবহারকারী ইতিমধ্যে বিদ্যমান!", 'success');
} else {
    log_message("কোন অ্যাডমিন ব্যবহারকারী পাওয়া যায়নি, নতুন অ্যাডমিন তৈরি করা হচ্ছে...");
    
    $adminUsername = "admin";
    $adminPassword = password_hash("password123", PASSWORD_DEFAULT);
    
    $insertAdminQuery = "INSERT INTO users (username, password, user_type) 
                          VALUES ('$adminUsername', '$adminPassword', 'admin')";
    
    if ($conn->query($insertAdminQuery)) {
        log_message("অ্যাডমিন ব্যবহারকারী সফলভাবে তৈরি করা হয়েছে!", 'success');
        log_message("ব্যবহারকারীর নাম: admin", 'info');
        log_message("পাসওয়ার্ড: password123", 'info');
    } else {
        log_message("অ্যাডমিন ব্যবহারকারী তৈরি করতে সমস্যা: " . $conn->error, 'error');
    }
}

// 4. Check for student users
log_message("শিক্ষার্থী ব্যবহারকারী পরীক্ষা করা হচ্ছে...");
$studentCheckQuery = "SELECT id FROM users WHERE user_type = 'student'";
$studentResult = $conn->query($studentCheckQuery);

if ($studentResult && $studentResult->num_rows > 0) {
    log_message("শিক্ষার্থী ব্যবহারকারী ইতিমধ্যে বিদ্যমান: " . $studentResult->num_rows . " জন", 'success');
} else {
    log_message("কোন শিক্ষার্থী ব্যবহারকারী পাওয়া যায়নি, নতুন শিক্ষার্থী তৈরি করা হচ্ছে...");
    
    $studentUsername = "student";
    $studentPassword = password_hash("password123", PASSWORD_DEFAULT);
    
    $insertStudentQuery = "INSERT INTO users (username, password, user_type) 
                            VALUES ('$studentUsername', '$studentPassword', 'student')";
    
    if ($conn->query($insertStudentQuery)) {
        $studentUserId = $conn->insert_id;
        log_message("শিক্ষার্থী ব্যবহারকারী সফলভাবে তৈরি করা হয়েছে!", 'success');
        log_message("ব্যবহারকারীর নাম: student", 'info');
        log_message("পাসওয়ার্ড: password123", 'info');
        
        // Create student profile
        $studentId = 'STU' . date('Y') . str_pad($studentUserId, 4, '0', STR_PAD_LEFT);
        $currentDate = date('Y-m-d');
        
        // Get or create a department
        $departmentId = 1;
        $departmentCheck = $conn->query("SELECT id FROM departments LIMIT 1");
        if ($departmentCheck->num_rows == 0) {
            $conn->query("INSERT INTO departments (department_name, description) VALUES ('সাধারণ বিভাগ', 'ডিফল্ট বিভাগ')");
            $departmentId = $conn->insert_id;
        } else {
            $departmentRow = $departmentCheck->fetch_assoc();
            $departmentId = $departmentRow['id'];
        }
        
        $insertStudentProfileQuery = "INSERT INTO students 
            (student_id, first_name, last_name, gender, admission_date, department_id, user_id, dob, phone, address) 
            VALUES 
            ('$studentId', 'ডিফল্ট', 'শিক্ষার্থী', 'male', '$currentDate', $departmentId, $studentUserId, '$currentDate', '01700000000', 'ঢাকা, বাংলাদেশ')";
        
        if ($conn->query($insertStudentProfileQuery)) {
            log_message("শিক্ষার্থী প্রোফাইল সফলভাবে তৈরি করা হয়েছে!", 'success');
            log_message("শিক্ষার্থী আইডি: $studentId", 'info');
        } else {
            log_message("শিক্ষার্থী প্রোফাইল তৈরি করতে সমস্যা: " . $conn->error, 'error');
        }
    } else {
        log_message("শিক্ষার্থী ব্যবহারকারী তৈরি করতে সমস্যা: " . $conn->error, 'error');
    }
}

// 5. Generate a demo session if none exists
log_message("সেশন পরীক্ষা করা হচ্ছে...");
$sessionCheckQuery = "SELECT id FROM sessions";
$sessionResult = $conn->query($sessionCheckQuery);

if ($sessionResult && $sessionResult->num_rows > 0) {
    log_message("সেশন ইতিমধ্যে বিদ্যমান: " . $sessionResult->num_rows . " টি", 'success');
} else {
    log_message("কোন সেশন পাওয়া যায়নি, নতুন সেশন তৈরি করা হচ্ছে...");
    
    $currentYear = date('Y');
    $nextYear = $currentYear + 1;
    $sessionName = $currentYear . '-' . $nextYear;
    $startDate = $currentYear . '-01-01';
    $endDate = $nextYear . '-12-31';
    
    $insertSessionQuery = "INSERT INTO sessions (session_name, start_date, end_date, is_active) 
                            VALUES ('$sessionName', '$startDate', '$endDate', 1)";
    
    if ($conn->query($insertSessionQuery)) {
        log_message("সেশন সফলভাবে তৈরি করা হয়েছে!", 'success');
        log_message("সেশনের নাম: $sessionName", 'info');
    } else {
        log_message("সেশন তৈরি করতে সমস্যা: " . $conn->error, 'error');
    }
}

// 6. Check CSRF implementation
log_message("CSRF সুরক্ষা পরীক্ষা করা হচ্ছে...");

// Generate a CSRF token for the current session if not exists
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    log_message("নতুন CSRF টোকেন তৈরি করা হয়েছে!", 'success');
} else {
    log_message("CSRF টোকেন ইতিমধ্যে বিদ্যমান!", 'success');
}

log_message("CSRF টোকেন: " . substr($_SESSION['csrf_token'], 0, 10) . "...", 'info');

// 7. Summary and navigation links
echo "</div>
    <div class='alert alert-success'>
        <h4>সেটআপ সম্পূর্ণ হয়েছে!</h4>
        <p>আপনার সিস্টেম এখন ব্যবহারের জন্য প্রস্তুত। নিম্নলিখিত লিঙ্কগুলি ব্যবহার করে সিস্টেমে প্রবেশ করুন:</p>
    </div>
    
    <div class='row'>
        <div class='col-md-4'>
            <div class='card mb-3'>
                <div class='card-header bg-primary text-white'>
                    <h5 class='card-title mb-0'>অ্যাডমিন পরিচালনা</h5>
                </div>
                <div class='card-body'>
                    <p><strong>ব্যবহারকারীর নাম:</strong> admin</p>
                    <p><strong>পাসওয়ার্ড:</strong> password123</p>
                    <a href='../index.php' class='btn btn-primary'>লগইন করুন</a>
                </div>
            </div>
        </div>
        
        <div class='col-md-4'>
            <div class='card mb-3'>
                <div class='card-header bg-success text-white'>
                    <h5 class='card-title mb-0'>শিক্ষার্থী পরিচালনা</h5>
                </div>
                <div class='card-body'>
                    <p><strong>ব্যবহারকারীর নাম:</strong> student</p>
                    <p><strong>পাসওয়ার্ড:</strong> password123</p>
                    <a href='../index.php' class='btn btn-success'>লগইন করুন</a>
                </div>
            </div>
        </div>
        
        <div class='col-md-4'>
            <div class='card mb-3'>
                <div class='card-header bg-info text-white'>
                    <h5 class='card-title mb-0'>ডাটাবেস ম্যানেজমেন্ট</h5>
                </div>
                <div class='card-body'>
                    <p>ডাটাবেস সেটআপ এবং ম্যানেজমেন্ট টুলস।</p>
                    <a href='setup.php' class='btn btn-info'>ডাটাবেস সেটআপ</a>
                </div>
            </div>
        </div>
    </div>
    
    <div class='mt-4 text-center'>
        <a href='../index.php' class='btn btn-lg btn-primary'>হোম পেজে ফিরে যান</a>
    </div>
    
    </div>
    <script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>
</body>
</html>";

// Close database connection
$conn->close();
?> 