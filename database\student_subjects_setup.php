<?php
require_once '../includes/dbh.inc.php';

// Create student_subjects table if it doesn't exist
$studentSubjectsTableQuery = "CREATE TABLE IF NOT EXISTS student_subjects (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    student_id INT(11) NOT NULL,
    subject_id INT(11) NOT NULL,
    category VARCHAR(50) NOT NULL,
    selection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id INT(11) NULL,
    UNIQUE KEY(student_id, subject_id),
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
)";

if ($conn->query($studentSubjectsTableQuery)) {
    echo "student_subjects table created or already exists!<br>";
} else {
    echo "Error creating student_subjects table: " . $conn->error . "<br>";
}

echo "<a href='../admin/subject_categories.php'>Go back to subject categories</a>";
?> 