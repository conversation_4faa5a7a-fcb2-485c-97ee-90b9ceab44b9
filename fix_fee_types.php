<?php
require_once 'includes/dbh.inc.php';

echo "Fixing fee_types table structure...\n";

// Check if is_recurring column exists
$result = $conn->query("SHOW COLUMNS FROM fee_types LIKE 'is_recurring'");
if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $alterQuery = "ALTER TABLE fee_types ADD COLUMN is_recurring TINYINT(1) DEFAULT 0 AFTER description";
    if ($conn->query($alterQuery)) {
        echo "Column 'is_recurring' has been added successfully.\n";
    } else {
        echo "Error adding column 'is_recurring': " . $conn->error . "\n";
    }
} else {
    echo "Column 'is_recurring' already exists.\n";
}

// Check if amount column is referenced in the PHP code but doesn't exist in the database
$result = $conn->query("SHOW COLUMNS FROM fee_types LIKE 'amount'");
if ($result->num_rows == 0) {
    // Column doesn't exist, add it
    $alterQuery = "ALTER TABLE fee_types ADD COLUMN amount DECIMAL(10,2) DEFAULT 0 AFTER is_recurring";
    if ($conn->query($alterQuery)) {
        echo "Column 'amount' has been added successfully.\n";
    } else {
        echo "Error adding column 'amount': " . $conn->error . "\n";
    }
} else {
    echo "Column 'amount' already exists.\n";
}

echo "Done.\n"; 