<?php
session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// CSRF Protection
if (isset($_POST['login-submit'])) {
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
        error_log("CSRF token validation failed");
        header("Location: ../index.php?error=securityerror#login-section");
        exit();
    }
    
    require 'dbh.inc.php';
    
    // Set strict security headers
    header("Content-Security-Policy: default-src 'self'; script-src 'self'; object-src 'none'; frame-ancestors 'none'");
    header("X-Content-Type-Options: nosniff");
    header("X-Frame-Options: DENY");
    header("Strict-Transport-Security: max-age=31536000; includeSubDomains");
    header("Referrer-Policy: same-origin");
    header("Permissions-Policy: geolocation=(), camera=(), microphone=()");
    
    // Verify database connection
    if ($conn->connect_error) {
        error_log("Database connection failed: " . $conn->connect_error);
        header("Location: ../index.php?error=dbconnect#login-section");
        exit();
    }
    
    // Sanitize inputs
    $username = filter_var(trim($_POST['username']), FILTER_SANITIZE_STRING);
    $password = $_POST['password']; // Don't sanitize password
    $userType = filter_var(trim($_POST['userType']), FILTER_SANITIZE_STRING);
    
    // Log attempt for debugging
    error_log("Login attempt: Username: $username, User Type: $userType");
    
    // Check if inputs are empty
    if (empty($username) || empty($password)) {
        header("Location: ../index.php?error=emptyfields#login-section");
        exit();
    }
    
    // Rate limiting implementation
    $attemptLimit = 5; // Max attempts allowed
    $timeWindow = 15 * 60; // 15 minutes in seconds
    
    // Get client IP with fallbacks for proxy situations
    $clientIP = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // Could connect to a separate tracking table for more robust rate limiting
    if (isset($_SESSION['login_attempts'][$clientIP])) {
        $attempts = $_SESSION['login_attempts'][$clientIP];
        
        if (count($attempts) >= $attemptLimit) {
            // Check if all attempts were within the time window
            $recentAttempts = array_filter($attempts, function($timestamp) use ($timeWindow) {
                return (time() - $timestamp) < $timeWindow;
            });
            
            if (count($recentAttempts) >= $attemptLimit) {
                error_log("Rate limit exceeded for IP: $clientIP");
                header("Location: ../index.php?error=toomanyrequests&reset=needed#login-section");
                exit();
            }
        }
    }
    
    // Record this attempt
    if (!isset($_SESSION['login_attempts'][$clientIP])) {
        $_SESSION['login_attempts'][$clientIP] = [];
    }
    $_SESSION['login_attempts'][$clientIP][] = time();
    
    // Check if user exists
    $sql = "SELECT * FROM users WHERE username=? AND user_type=?";
    $stmt = $conn->prepare($sql);
    
    if (!$stmt) {
        error_log("Prepare failed: " . $conn->error);
        header("Location: ../index.php?error=sqlerror#login-section");
        exit();
    }
    
    $stmt->bind_param("ss", $username, $userType);
    error_log("Searching for user with username: '$username' and type: '$userType'");
    
    if (!$stmt->execute()) {
        error_log("Execute failed: " . $stmt->error);
        header("Location: ../index.php?error=sqlerror#login-section");
        exit();
    }
    
    $result = $stmt->get_result();
    error_log("Query result rows: " . $result->num_rows);
    
    if ($row = $result->fetch_assoc()) {
        // Verify password
        $pwdCheck = password_verify($password, $row['password']);
        error_log("Password check: " . ($pwdCheck ? "true" : "false") . " for user: $username");
        
        if ($pwdCheck == false) {
            // Add small delay to prevent timing attacks
            sleep(1);
            header("Location: ../index.php?error=wrongpassword&message=ভুল পাসওয়ার্ড। দয়া করে আবার চেষ্টা করুন।");
            exit();
        }
        
        // Clear login attempts on successful login
        if (isset($_SESSION['login_attempts'][$clientIP])) {
            unset($_SESSION['login_attempts'][$clientIP]);
        }
        
        // Regenerate session ID to prevent session fixation
        session_regenerate_id(true);
        
        // Start session
        $_SESSION['userId'] = $row['id'];
        $_SESSION['username'] = $row['username'];
        $_SESSION['userType'] = $row['user_type'];
        $_SESSION['lastActivity'] = time(); // For session timeout
        
        // Redirect based on user type
        switch ($row['user_type']) {
            case 'admin':
                header("Location: ../admin/dashboard.php");
                break;
            case 'teacher':
                header("Location: ../teacher/dashboard.php");
                break;
            case 'student':
                header("Location: ../student/dashboard.php");
                break;
            case 'staff':
                header("Location: ../staff/dashboard.php");
                break;
            default:
                header("Location: ../index.php");
        }
        exit();
    } else {
        // Add small delay to prevent timing attacks and username enumeration
        sleep(1);
        error_log("No user found with username: $username and type: $userType");
        header("Location: ../index.php?error=nouser");
        exit();
    }
    
    $stmt->close();
    $conn->close();
    
} else {
    header("Location: ../index.php");
    exit();
}
?>