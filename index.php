<?php
ob_start(); // Start output buffering
session_start();

// Include database connection
require_once "includes/dbh.inc.php";

// Do all header redirects here
if (isset($_GET['error'])) {
    $error = $_GET['error'];
    $_SESSION['login_error'] = $error;
    if (isset($_GET['message'])) {
        $_SESSION['error_message'] = $_GET['message'];
    }
    // Remove the redirect that's causing the loop
    ob_clean(); // Clear any previous output
}

// Then output HTML
?>
<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>জেড এফ এ ডব্লিউ কলেজ</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .nav-link {
            transition: all 0.3s;
        }
        .nav-link:hover {
            transform: translateY(-3px);
        }
        .subject-card {
            transition: all 0.3s;
            height: 100%;
        }
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-university me-2"></i>জেড এফ এ ডব্লিউ কলেজ
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home me-1"></i> হোম
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-1"></i> বিষয়সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-1"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">
                            <i class="fas fa-info-circle me-1"></i> আমাদের সম্পর্কে
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">
                            <i class="fas fa-envelope me-1"></i> যোগাযোগ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-light ms-2" href="#login-section">
                            <i class="fas fa-sign-in-alt me-1"></i> লগইন
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="container mt-4">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="display-4">জেড এফ এ ডব্লিউ কলেজ</h1>
                <p class="lead">একটি সম্পূর্ণ কলেজ ব্যবস্থাপনা পদ্ধতি, যা সকল শিক্ষার্থী, শিক্ষক ও কর্মীদের জন্য বিকশিত করা হয়েছে।</p>
                <a href="#subjects-section" class="btn btn-primary mt-3">
                    <i class="fas fa-book me-2"></i>বিষয়সমূহ দেখুন
                </a>
            </div>
            <div class="col-md-6">
                <img src="img/college.jpg" alt="কলেজ ছবি" class="img-fluid rounded shadow">
            </div>
        </div>
    </div>
    
    <!-- Notice Section -->
    <div class="container mt-5 mb-2">
        <div class="row">
            <div class="col-12">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0"><i class="fas fa-bullhorn me-2"></i>সর্বশেষ নোটিশ</h3>
                    </div>
                    <div class="card-body">
                        <?php
                        // Check if notices table exists and fetch notices
                        $notices_query = "SHOW TABLES LIKE 'notices'";
                        $notices_result = $conn->query($notices_query);
                        
                        if ($notices_result && $notices_result->num_rows > 0) {
                            // Get latest 3 notices
                            $sql = "SELECT * FROM notices ORDER BY date DESC LIMIT 3";
                            $result = $conn->query($sql);
                            
                            if ($result && $result->num_rows > 0) {
                                echo '<div class="list-group">';
                                while ($row = $result->fetch_assoc()) {
                                    echo '<div class="list-group-item list-group-item-action">';
                                    echo '<div class="d-flex w-100 justify-content-between">';
                                    echo '<h5 class="mb-1">' . htmlspecialchars($row['title']) . '</h5>';
                                    echo '<small>' . htmlspecialchars($row['date']) . '</small>';
                                    echo '</div>';
                                    echo '<p class="mb-1">' . htmlspecialchars(substr($row['content'], 0, 150)) . 
                                         (strlen($row['content']) > 150 ? '...' : '') . '</p>';
                                    echo '</div>';
                                }
                                echo '</div>';
                                echo '<div class="text-end mt-3">';
                                echo '<a href="notices.php" class="btn btn-outline-primary btn-sm">সকল নোটিশ দেখুন <i class="fas fa-arrow-right ms-1"></i></a>';
                                echo '</div>';
                            } else {
                                echo '<p class="text-center mb-0">কোন নোটিশ পাওয়া যায়নি</p>';
                            }
                        } else {
                            echo '<p class="text-center mb-0">নোটিশ তালিকা উপলব্ধ নেই</p>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Subjects Section -->
    <div id="subjects-section" class="container mt-5 mb-5">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h2>আমাদের বিষয়সমূহ</h2>
                <p class="text-muted">আমাদের কলেজে প্রদান করা হয় এমন সকল বিষয়ের তালিকা</p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                        <h4 class="card-title">গণিত</h4>
                        <p class="card-text">বীজগণিত, ক্যালকুলাস, জ্যামিতি, ত্রিকোণমিতি, এবং আরও অনেক কিছু।</p>
                        <a href="subjects/mathematics.php" class="btn btn-outline-primary">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-atom fa-3x text-danger mb-3"></i>
                        <h4 class="card-title">পদার্থবিজ্ঞান</h4>
                        <p class="card-text">বল, শক্তি, তাপগতিবিদ্যা, বিদ্যুৎ, চৌম্বকত্ব এবং আধুনিক পদার্থবিজ্ঞান।</p>
                        <a href="subjects/physics.php" class="btn btn-outline-danger">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-flask fa-3x text-success mb-3"></i>
                        <h4 class="card-title">রসায়ন</h4>
                        <p class="card-text">অর্গানিক, ইনঅর্গানিক, ফিজিক্যাল এবং অ্যানালিটিক্যাল রসায়ন।</p>
                        <a href="subjects/chemistry.php" class="btn btn-outline-success">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-dna fa-3x text-info mb-3"></i>
                        <h4 class="card-title">জীববিজ্ঞান</h4>
                        <p class="card-text">জেনেটিক্স, সেল বায়োলজি, ইকোলজি, মাইক্রোবায়োলজি এবং জুলজি।</p>
                        <a href="subjects/biology.php" class="btn btn-outline-info">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-laptop-code fa-3x text-warning mb-3"></i>
                        <h4 class="card-title">কম্পিউটার সায়েন্স</h4>
                        <p class="card-text">প্রোগ্রামিং, ডাটা স্ট্রাকচার, ডাটাবেস, নেটওয়ার্কিং এবং এআই।</p>
                        <a href="subjects/computer-science.php" class="btn btn-outline-warning">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card subject-card">
                    <div class="card-body text-center">
                        <i class="fas fa-language fa-3x text-secondary mb-3"></i>
                        <h4 class="card-title">বাংলা সাহিত্য</h4>
                        <p class="card-text">বাংলা ভাষা, সাহিত্য, কবিতা, গল্প, উপন্যাস এবং নাটক।</p>
                        <a href="subjects/bengali.php" class="btn btn-outline-secondary">বিস্তারিত দেখুন</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="subjects.php" class="btn btn-primary">
                <i class="fas fa-list me-2"></i>সকল বিষয় দেখুন
            </a>
        </div>
    </div>
    
    <!-- Login Section -->
    <div id="login-section" class="container mt-5 mb-5">
        <div class="row">
            <div class="col-md-6 mx-auto">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">লগইন</h3>
                    </div>
                    
                    <?php if (isset($_GET['message']) && $_GET['message'] == 'session_cleared'): ?>
                    <div class="alert alert-success alert-dismissible fade show mx-3 mt-3" role="alert">
                        সেশন সফলভাবে মুছে ফেলা হয়েছে। আপনি এখন আবার লগইন করার চেষ্টা করতে পারেন।
                        <?php if (isset($_SESSION['cleared_items']) && !empty($_SESSION['cleared_items'])): ?>
                        <hr>
                        <p><small>মুছে ফেলা আইটেম:</small></p>
                        <ul class="mb-0">
                            <?php foreach ($_SESSION['cleared_items'] as $item): ?>
                                <li><small><?php echo htmlspecialchars($item); ?></small></li>
                            <?php endforeach; ?>
                        </ul>
                        <?php unset($_SESSION['cleared_items']); ?>
                        <?php endif; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <?php
                    if (isset($_SESSION['login_error'])):
                        $error = $_SESSION['login_error'];
                        unset($_SESSION['login_error']); // Clear the error after showing
                    ?>
                    <div class="alert 
                        <?php 
                            if ($error == 'emptyfields'): ?>alert-warning
                        <?php elseif ($error == 'wrongpassword'): ?>alert-danger
                        <?php elseif ($error == 'nouser'): ?>alert-danger
                        <?php elseif ($error == 'sqlerror'): ?>alert-danger
                        <?php elseif ($error == 'toomanyrequests'): ?>alert-warning
                        <?php else: ?>alert-warning<?php endif; ?>
                        alert-dismissible fade show mx-3 mt-3" role="alert">
                        
                        <?php 
                            if ($error == 'emptyfields'): 
                                echo "দয়া করে সমস্ত ক্ষেত্র পূরণ করুন।";
                            elseif ($error == 'wrongpassword'): 
                                echo "পাসওয়ার্ড ভুল হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।";
                            elseif ($error == 'nouser'): 
                                echo "এই ইউজারনেম এবং ইউজার টাইপের কোন ব্যবহারকারী খুঁজে পাওয়া যায়নি।";
                            elseif ($error == 'sqlerror'): 
                                echo "সিস্টেমে সমস্যা - দয়া করে পরে আবার চেষ্টা করুন।";
                            elseif ($error == 'toomanyrequests'):
                                echo "অতিরিক্ত লগইন প্রচেষ্টা। দয়া করে কিছুক্ষণ অপেক্ষা করুন বা <a href='reset_login_attempts.php' class='alert-link'>লগইন প্রচেষ্টা রিসেট করুন</a>।";
                            else:
                                echo "একটি ত্রুটি ঘটেছে। দয়া করে আবার চেষ্টা করুন।";
                            endif; 
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="card-body">
                        <form action="includes/login.inc.php" method="post">
                            <div class="mb-3">
                                <label for="username" class="form-label">ইউজারনেম</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">পাসওয়ার্ড</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <div class="mb-3">
                                <label for="userType" class="form-label">লগইন করুন এভাবে</label>
                                <select class="form-select" id="userType" name="userType" required>
                                    <option value="admin">অ্যাডমিন</option>
                                    <option value="teacher">শিক্ষক</option>
                                    <option value="student"<?php echo (isset($_GET['error']) && isset($_GET['type']) && $_GET['type'] == 'student') ? ' selected' : ''; ?>>শিক্ষার্থী</option>
                                    <option value="staff">কর্মচারী</option>
                                </select>
                            </div>
                            <?php
                            // Generate CSRF token if not already set
                            if (!isset($_SESSION['csrf_token'])) {
                                $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                            }
                            ?>
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                            <button type="submit" name="login-submit" class="btn btn-primary w-100">লগইন</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>জেড এফ এ ডব্লিউ কলেজ</h5>
                    <p>একটি সম্পূর্ণ কলেজ ব্যবস্থাপনা পদ্ধতি, যা সকল শিক্ষার্থী, শিক্ষক ও কর্মীদের জন্য বিকশিত করা হয়েছে।</p>
                </div>
                <div class="col-md-4">
                    <h5>দ্রুত লিংক</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white">হোম</a></li>
                        <li><a href="subjects.php" class="text-white">বিষয়সমূহ</a></li>
                        <li><a href="notices.php" class="text-white">নোটিশ</a></li>
                        <li><a href="about.php" class="text-white">আমাদের সম্পর্কে</a></li>
                        <li><a href="contact.php" class="text-white">যোগাযোগ</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5>যোগাযোগ করুন</h5>
                    <address>
                        <i class="fas fa-map-marker-alt me-2"></i> চুয়াডাঙঙ্গা সদর<br>
                        <i class="fas fa-phone me-2"></i> +৮৮০১৭১৭৮৬১৭৬২<br>
                        <i class="fas fa-envelope me-2"></i> <EMAIL>
                    </address>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <p class="mb-0">কপিরাইট &copy; ২০২৫ জেড এফ এ ডব্লিউ কলেজ। সর্বস্বত্ব সংরক্ষিত।</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>