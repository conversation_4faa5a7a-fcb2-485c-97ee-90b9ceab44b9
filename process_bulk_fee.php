<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Process bulk fee addition
if (isset($_POST['action']) && $_POST['action'] === 'add_bulk_fee') {
    
    // Check if all required fields are present
    if (isset($_POST['fee_type']) && !empty($_POST['fee_type']) &&
        isset($_POST['amount']) && !empty($_POST['amount']) && 
        isset($_POST['due_date']) && !empty($_POST['due_date']) && 
        isset($_POST['session_id']) && !empty($_POST['session_id']) && 
        isset($_POST['class_id']) && !empty($_POST['class_id']) && 
        isset($_POST['student_ids']) && is_array($_POST['student_ids']) && !empty($_POST['student_ids'])) {
        
        $feeType = $_POST['fee_type'];
        $amount = floatval($_POST['amount']);
        $dueDate = $_POST['due_date'];
        $sessionId = $_POST['session_id'];
        $classId = $_POST['class_id'];
        $studentIds = $_POST['student_ids'];
        
        // Handle monthly fee with selected months
        $months = [];
        $isMonthlyFee = ($feeType === 'মাসিক বেতন');
        
        if ($isMonthlyFee && isset($_POST['months']) && is_array($_POST['months'])) {
            $months = $_POST['months'];
            
            if (empty($months)) {
                $_SESSION['error'] = "মাসিক বেতনের জন্য কমপক্ষে একটি মাস নির্বাচন করুন!";
                header("Location: fees.php");
                exit();
            }
        }
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            $inserted = 0;
            
            // If it's monthly fee with selected months, create separate fee entries for each month
            if ($isMonthlyFee && !empty($months)) {
                // Each month has a fixed rate of 150 BDT
                $monthlyRate = 150;
                
                foreach ($studentIds as $studentId) {
                    foreach ($months as $month) {
                        // Generate a month-specific fee type name (e.g., "মাসিক বেতন - জানুয়ারি")
                        $monthFeeType = $feeType . " - " . getMonthNameBangla($month);
                        
                        $query = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status) 
                                 VALUES (?, ?, ?, 0, ?, 'due')";
                        $stmt = $conn->prepare($query);
                        $stmt->bind_param("isds", $studentId, $monthFeeType, $monthlyRate, $dueDate);
                        
                        if ($stmt->execute()) {
                            $inserted++;
                        }
                    }
                }
            } else {
                // Regular fee (not monthly or monthly without specific months)
                foreach ($studentIds as $studentId) {
                    $query = "INSERT INTO fees (student_id, fee_type, amount, paid, due_date, payment_status) 
                             VALUES (?, ?, ?, 0, ?, 'due')";
                    $stmt = $conn->prepare($query);
                    $stmt->bind_param("isds", $studentId, $feeType, $amount, $dueDate);
                    
                    if ($stmt->execute()) {
                        $inserted++;
                    }
                }
            }
            
            // Commit transaction
            $conn->commit();
            
            if ($inserted > 0) {
                $_SESSION['success'] = "সফলভাবে $inserted টি ফি যোগ করা হয়েছে!";
            } else {
                $_SESSION['error'] = "কোন ফি যোগ করা হয়নি!";
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $_SESSION['error'] = "ফি যোগ করতে সমস্যা হয়েছে: " . $e->getMessage();
        }
        
    } else {
        $_SESSION['error'] = "সব প্রয়োজনীয় তথ্য প্রদান করুন!";
    }
    
    // Redirect back to fees page
    header("Location: fees.php");
    exit();
}

/**
 * Convert month name to Bangla
 */
function getMonthNameBangla($monthName) {
    $monthNamesBangla = [
        'january' => 'জানুয়ারি',
        'february' => 'ফেব্রুয়ারি',
        'march' => 'মার্চ',
        'april' => 'এপ্রিল',
        'may' => 'মে',
        'june' => 'জুন',
        'july' => 'জুলাই',
        'august' => 'আগস্ট',
        'september' => 'সেপ্টেম্বর',
        'october' => 'অক্টোবর',
        'november' => 'নভেম্বর',
        'december' => 'ডিসেম্বর'
    ];
    
    return $monthNamesBangla[$monthName] ?? $monthName;
} 