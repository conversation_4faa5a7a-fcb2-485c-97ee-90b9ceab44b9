<?php
session_start();

// Check if user is logged in and is a staff
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'staff') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get staff information
$userId = $_SESSION['userId'];
$sql = "SELECT * FROM staff WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$staff = $result->fetch_assoc();

// Get recent students
$recentStudentsQuery = "SELECT s.student_id, s.first_name, s.last_name, s.department, s.admission_date 
                      FROM students s 
                      ORDER BY s.id DESC 
                      LIMIT 5";
$recentStudents = $conn->query($recentStudentsQuery);

// Get fee statistics
$feeStatsQuery = "SELECT 
                    COUNT(CASE WHEN payment_status = 'Paid' THEN 1 END) as paid,
                    COUNT(CASE WHEN payment_status = 'Unpaid' THEN 1 END) as unpaid,
                    COUNT(CASE WHEN payment_status = 'Partial' THEN 1 END) as partial
                  FROM fees";
$feeStats = $conn->query($feeStatsQuery);
$feeData = $feeStats->fetch_assoc();

// Get department statistics
$deptStatsQuery = "SELECT department, COUNT(*) as count 
                  FROM students 
                  GROUP BY department 
                  ORDER BY count DESC 
                  LIMIT 5";
$deptStats = $conn->query($deptStatsQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Dashboard - College Management System</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Staff Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> Fees
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-file-pdf me-2"></i> Reports
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Staff Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card profile-header">
                            <div class="row g-0">
                                <div class="col-md-2 text-center">
                                    <img src="https://via.placeholder.com/150" alt="Staff" class="profile-img">
                                </div>
                                <div class="col-md-10 profile-info">
                                    <div class="card-body">
                                        <h2><?php echo $staff['first_name'] . ' ' . $staff['last_name']; ?></h2>
                                        <p><strong>Staff ID:</strong> <?php echo $staff['staff_id']; ?></p>
                                        <p><strong>Role:</strong> <?php echo $staff['role']; ?></p>
                                        <p><strong>Email:</strong> <?php echo $staff['email']; ?></p>
                                        <p><strong>Phone:</strong> <?php echo $staff['phone']; ?></p>
                                        <p><strong>Joining Date:</strong> <?php echo date('d M Y', strtotime($staff['joining_date'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon stat-fees">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $feeData['paid'] ?? 0; ?></h3>
                            <p>Paid Fees</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon stat-courses">
                            <i class="fas fa-money-check-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $feeData['unpaid'] ?? 0; ?></h3>
                            <p>Unpaid Fees</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon stat-teachers">
                            <i class="fas fa-money-bill-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3><?php echo $feeData['partial'] ?? 0; ?></h3>
                            <p>Partial Payments</p>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <!-- Recent Students -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">Recent Students</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Department</th>
                                                <th>Join Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentStudents && $recentStudents->num_rows > 0): ?>
                                                <?php while ($student = $recentStudents->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $student['student_id']; ?></td>
                                                        <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                        <td><?php echo $student['department']; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($student['admission_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">No students found</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="students.php" class="btn btn-primary btn-sm mt-3">View All Students</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Department Stats -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">Department Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Department</th>
                                                <th>Student Count</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($deptStats && $deptStats->num_rows > 0): ?>
                                                <?php while ($dept = $deptStats->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $dept['department']; ?></td>
                                                        <td><?php echo $dept['count']; ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="2" class="text-center">No department data found</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="manage_fees.php" class="btn btn-outline-primary">
                                        <i class="fas fa-money-bill-wave me-2"></i> Manage Fees
                                    </a>
                                    <a href="fee_report.php" class="btn btn-outline-success">
                                        <i class="fas fa-chart-pie me-2"></i> Fee Reports
                                    </a>
                                    <a href="add_student.php" class="btn btn-outline-warning">
                                        <i class="fas fa-user-plus me-2"></i> Add Student
                                    </a>
                                    <a href="generate_id_cards.php" class="btn btn-outline-danger">
                                        <i class="fas fa-id-card me-2"></i> Generate ID Cards
                                    </a>
                                    <a href="general_reports.php" class="btn btn-outline-dark">
                                        <i class="fas fa-file-pdf me-2"></i> General Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Fee Reminder -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">Fee Reminders</h5>
                            </div>
                            <div class="card-body">
                                <form action="send_reminders.php" method="post">
                                    <div class="mb-3">
                                        <label for="reminderType" class="form-label">Reminder Type</label>
                                        <select class="form-select" id="reminderType" name="reminderType" required>
                                            <option value="all_unpaid">All Unpaid Fees</option>
                                            <option value="overdue">Overdue Fees Only</option>
                                            <option value="upcoming">Upcoming Due Dates</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="messageType" class="form-label">Send Via</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="email" id="emailReminder" name="messageType[]" checked>
                                            <label class="form-check-label" for="emailReminder">
                                                Email
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="sms" id="smsReminder" name="messageType[]">
                                            <label class="form-check-label" for="smsReminder">
                                                SMS
                                            </label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-warning">Send Reminders</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 