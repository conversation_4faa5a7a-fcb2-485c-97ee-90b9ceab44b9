/* Student Dashboard Styles */

/* Sidebar Styles */
.sidebar {
    background-color: #2c3e50;
    color: white;
    height: 100vh;
    position: fixed;
    overflow-y: auto;
    padding-top: 20px;
    padding-bottom: 60px;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    transition: all 0.3s ease;
    z-index: 100;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    margin-bottom: 5px;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background-color: #3498db;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Main Content styles */
.main-content {
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* Custom card styles for student dashboard */
.card {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Profile image style */
.profile-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.profile-img:hover {
    transform: scale(1.05);
}

/* Table hover effects */
.table tbody tr {
    transition: all 0.2s;
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: scale(1.01);
}

/* Button styles */
.btn {
    border-radius: 5px;
    padding: 8px 16px;
    transition: all 0.3s;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Custom header background */
.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 15px 20px;
    font-weight: 600;
} 