<?php
session_start();

// Check if user is logged in and is a student
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student information with department name
$userId = $_SESSION['userId'];
$sql = "SELECT s.*, d.department_name as department 
        FROM students s 
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

// Get upcoming exams
try {
    $upcomingExamsQuery = "SELECT e.exam_name, e.course_name, e.exam_date, e.total_marks
                          FROM exams e
                          WHERE e.exam_date >= CURDATE()
                          ORDER BY e.exam_date ASC
                          LIMIT 5";
    $upcomingExams = $conn->query($upcomingExamsQuery);
} catch (Exception $e) {
    $upcomingExams = null;
}

// Get recent results
try {
    $recentResultsQuery = "SELECT r.marks_obtained, r.grade, e.exam_name, e.course_name
                          FROM results r
                          JOIN exams e ON r.exam_id = e.id
                          JOIN students s ON r.student_id = s.id
                          WHERE s.user_id = ?
                          ORDER BY r.id DESC
                          LIMIT 5";
    $stmt = $conn->prepare($recentResultsQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $recentResults = $stmt->get_result();
} catch (Exception $e) {
    $recentResults = null;
}

// Get fee information
try {
    $feesQuery = "SELECT f.fee_type, f.amount, f.due_date, f.payment_status
                 FROM fees f
                 JOIN students s ON f.student_id = s.id
                 WHERE s.user_id = ?
                 ORDER BY f.due_date DESC
                 LIMIT 5";
    $stmt = $conn->prepare($feesQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $fees = $stmt->get_result();
} catch (Exception $e) {
    $fees = null;
}

// Get announcements
$announcementsQuery = "SELECT * FROM announcements ORDER BY created_at DESC LIMIT 5";
$announcements = $conn->query($announcementsQuery);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>শিক্ষার্থী ড্যাশবোর্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>শিক্ষার্থী প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> প্রোফাইল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_selection.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্সসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell me-2"></i> নোটিফিকেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope me-2"></i> বার্তাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-alt me-2"></i> ইভেন্টসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="assignments.php">
                            <i class="fas fa-tasks me-2"></i> অ্যাসাইনমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="timetable.php">
                            <i class="fas fa-clock me-2"></i> রুটিন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Student Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card profile-header">
                            <div class="row g-0">
                                <div class="col-md-2 text-center">
                                    <img src="https://via.placeholder.com/150" alt="Student" class="profile-img">
                                </div>
                                <div class="col-md-10 profile-info">
                                    <div class="card-body">
                                        <h2><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></h2>
                                        <p><strong>শিক্ষার্থী আইডি:</strong> <?php echo $student['student_id']; ?></p>
                                        <p><strong>রোল নম্বর:</strong> <?php echo $student['roll_number'] ?? 'নির্ধারিত নয়'; ?></p>
                                        <p><strong>বিভাগ:</strong> <?php echo $student['department']; ?></p>
                                        <p><strong>ব্যাচ:</strong> <?php echo $student['batch']; ?></p>
                                        <p><strong>ইমেইল:</strong> <?php echo $student['email']; ?></p>
                                        <p><strong>ফোন:</strong> <?php echo $student['phone']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">আসন্ন পরীক্ষাসমূহ</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>কোর্স</th>
                                                <th>তারিখ</th>
                                                <th>মার্কস</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                                <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $exam['exam_name']; ?></td>
                                                        <td><?php echo $exam['course_name']; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($exam['exam_date'])); ?></td>
                                                        <td><?php echo $exam['total_marks']; ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন আসন্ন পরীক্ষা নেই</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="exams.php" class="btn btn-warning btn-sm mt-3">সকল পরীক্ষা দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Results -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">সাম্প্রতিক ফলাফল</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>কোর্স</th>
                                                <th>মার্কস</th>
                                                <th>গ্রেড</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentResults && $recentResults->num_rows > 0): ?>
                                                <?php while ($result = $recentResults->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $result['exam_name']; ?></td>
                                                        <td><?php echo $result['course_name']; ?></td>
                                                        <td><?php echo $result['marks_obtained']; ?></td>
                                                        <td><?php echo $result['grade']; ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন ফলাফল পাওয়া যায়নি</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="results.php" class="btn btn-success btn-sm mt-3">সকল ফলাফল দেখুন</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Fee Information -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="card-title mb-0">ফি সম্পর্কিত তথ্য</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ফি ধরন</th>
                                                <th>পরিমাণ</th>
                                                <th>শেষ তারিখ</th>
                                                <th>অবস্থা</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($fees && $fees->num_rows > 0): ?>
                                                <?php while ($fee = $fees->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $fee['fee_type']; ?></td>
                                                        <td><?php echo $fee['amount']; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($fee['due_date'])); ?></td>
                                                        <td>
                                                            <?php 
                                                                $statusClass = '';
                                                                $statusText = '';
                                                                
                                                                if ($fee['payment_status'] == 'paid') {
                                                                    $statusClass = 'bg-success';
                                                                    $statusText = 'পরিশোধিত';
                                                                } else if ($fee['payment_status'] == 'unpaid') {
                                                                    $statusClass = 'bg-danger';
                                                                    $statusText = 'অপরিশোধিত';
                                                                } else if ($fee['payment_status'] == 'partial') {
                                                                    $statusClass = 'bg-warning';
                                                                    $statusText = 'আংশিক';
                                                                }
                                                            ?>
                                                            <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">কোন ফি সম্পর্কিত তথ্য নেই</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="fees.php" class="btn btn-danger btn-sm mt-3">সকল ফি দেখুন</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Links -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">Quick Links</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="profile.php" class="btn btn-outline-primary">
                                        <i class="fas fa-user me-2"></i> View Profile
                                    </a>
                                    <a href="subject_selection.php" class="btn btn-outline-success">
                                        <i class="fas fa-book-open me-2"></i> Select Subjects
                                    </a>
                                    <a href="courses.php" class="btn btn-outline-success">
                                        <i class="fas fa-book me-2"></i> View Courses
                                    </a>
                                    <a href="attendance.php" class="btn btn-outline-warning">
                                        <i class="fas fa-calendar-check me-2"></i> Check Attendance
                                    </a>
                                    <a href="results.php" class="btn btn-outline-info">
                                        <i class="fas fa-chart-bar me-2"></i> View Results
                                    </a>
                                    <a href="fees.php" class="btn btn-outline-danger">
                                        <i class="fas fa-money-bill-wave me-2"></i> Pay Fees
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Announcements -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">বিজ্ঞপ্তি</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group announcement-list">
                                <?php if (isset($announcements) && $announcements->num_rows > 0): ?>
                                    <?php while ($announcement = $announcements->fetch_assoc()): ?>
                                        <a href="#" class="list-group-item list-group-item-action">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h5 class="mb-1"><?php echo $announcement['title']; ?></h5>
                                                <small><?php echo date('d M Y', strtotime($announcement['created_at'])); ?></small>
                                            </div>
                                            <p class="mb-1"><?php echo substr($announcement['content'], 0, 100) . '...'; ?></p>
                                        </a>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <div class="list-group-item">
                                        <p class="text-center mb-0">কোন বিজ্ঞপ্তি নেই</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <a href="announcements.php" class="btn btn-primary btn-sm mt-3">সকল বিজ্ঞপ্তি দেখুন</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 