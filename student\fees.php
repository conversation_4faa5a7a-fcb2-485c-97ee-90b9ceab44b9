<?php
session_start();

// Check if user is logged in and is a student
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'student') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get student information with department name
$userId = $_SESSION['userId'];
$sql = "SELECT s.*, d.department_name as department 
        FROM students s 
        LEFT JOIN departments d ON s.department_id = d.id
        WHERE s.user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$student = $result->fetch_assoc();

// Get fee information
$loadSuccess = true;
$errorMessage = "";
try {
    $feesQuery = "SELECT f.id, f.fee_type, f.amount, f.paid, f.due_date, f.payment_status, f.created_at
                 FROM fees f
                 JOIN students s ON f.student_id = s.id
                 WHERE s.user_id = ?
                 ORDER BY f.due_date DESC";
    $stmt = $conn->prepare($feesQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $fees = $stmt->get_result();
    
    if (!$fees) {
        $loadSuccess = false;
        $errorMessage = "ফি তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।";
    }
} catch (Exception $e) {
    $loadSuccess = false;
    $errorMessage = "ফি তথ্য লোড করতে সমস্যা হয়েছে। অনুগ্রহ করে আবার চেষ্টা করুন।";
}

// Calculate total amounts
$totalAmount = 0;
$totalPaid = 0;
$totalDue = 0;

if ($loadSuccess && $fees->num_rows > 0) {
    while ($row = $fees->fetch_assoc()) {
        $totalAmount += $row['amount'];
        $totalPaid += $row['paid'];
    }
    $totalDue = $totalAmount - $totalPaid;
}

?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ফি সমূহ - শিক্ষার্থী ড্যাশবোর্ড</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>শিক্ষার্থী প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> প্রোফাইল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_selection.php">
                            <i class="fas fa-book-open me-2"></i> বিষয় নির্বাচন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> কোর্সসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="fees.php">
                            <i class="fas fa-money-bill-wave me-2"></i> ফি সমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="library.php">
                            <i class="fas fa-book-reader me-2"></i> লাইব্রেরি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notifications.php">
                            <i class="fas fa-bell me-2"></i> নোটিফিকেশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="messages.php">
                            <i class="fas fa-envelope me-2"></i> বার্তাসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="events.php">
                            <i class="fas fa-calendar-alt me-2"></i> ইভেন্টসমূহ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="assignments.php">
                            <i class="fas fa-tasks me-2"></i> অ্যাসাইনমেন্ট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="timetable.php">
                            <i class="fas fa-clock me-2"></i> রুটিন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <h2>ফি সমূহ</h2>
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="dashboard.php">ড্যাশবোর্ড</a></li>
                                <li class="breadcrumb-item active">ফি সমূহ</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <?php if (!$loadSuccess): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $errorMessage; ?>
                    <button type="button" class="btn btn-sm btn-outline-danger ms-3" onclick="window.location.reload();">আবার চেষ্টা করুন</button>
                </div>
                <?php else: ?>

                <!-- Fee Summary -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">মোট ফি</h5>
                                <h2 class="text-primary"><?php echo number_format($totalAmount, 2); ?> ৳</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">পরিশোধিত</h5>
                                <h2 class="text-success"><?php echo number_format($totalPaid, 2); ?> ৳</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">বাকি</h5>
                                <h2 class="text-danger"><?php echo number_format($totalDue, 2); ?> ৳</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Fee Table -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">ফি বিবরণ</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($fees->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>ফি প্রকার</th>
                                            <th>পরিমাণ</th>
                                            <th>পরিশোধিত</th>
                                            <th>বাকি</th>
                                            <th>শেষ তারিখ</th>
                                            <th>অবস্থা</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $fees->data_seek(0);
                                        while ($fee = $fees->fetch_assoc()): 
                                            $due = $fee['amount'] - $fee['paid'];
                                        ?>
                                            <tr>
                                                <td><?php echo $fee['fee_type']; ?></td>
                                                <td><?php echo number_format($fee['amount'], 2); ?> ৳</td>
                                                <td><?php echo number_format($fee['paid'], 2); ?> ৳</td>
                                                <td><?php echo number_format($due, 2); ?> ৳</td>
                                                <td><?php echo date('d M Y', strtotime($fee['due_date'])); ?></td>
                                                <td>
                                                    <?php if ($fee['payment_status'] == 'paid'): ?>
                                                        <span class="badge bg-success">পরিশোধিত</span>
                                                    <?php elseif ($fee['payment_status'] == 'partial'): ?>
                                                        <span class="badge bg-warning">আংশিক পরিশোধিত</span>
                                                    <?php elseif (strtotime($fee['due_date']) < time()): ?>
                                                        <span class="badge bg-danger">সময় অতিক্রান্ত</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">বাকি</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle me-2"></i> কোন ফি তথ্য পাওয়া যায়নি।
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Payment Instructions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">পেমেন্ট নির্দেশনা</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>বিকাশ:</strong> 01XXXXXXXXX (Personal)</p>
                        <p><strong>নগদ:</strong> 01XXXXXXXXX</p>
                        <p><strong>রকেট:</strong> 01XXXXXXXXX</p>
                        <p><strong>ব্যাংক ট্রান্সফার:</strong> Account Name: ABC College, Account Number: *********, Bank Name: XYZ Bank, Branch: Main Branch</p>
                        <div class="alert alert-warning mt-3" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i> পেমেন্ট করার সময় অবশ্যই আপনার <strong>শিক্ষার্থী আইডি</strong> ও <strong>নাম</strong> উল্লেখ করবেন।
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap and jQuery Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Try reloading fee data on button click
            $('.reload-data').click(function() {
                window.location.reload();
            });
        });
    </script>
</body>
</html> 