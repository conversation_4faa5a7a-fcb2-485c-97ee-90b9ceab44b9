<?php
session_start();

// Check if user is logged in and is a teacher
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'teacher') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Get teacher information
$userId = $_SESSION['userId'];
$sql = "SELECT * FROM teachers WHERE user_id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $userId);
$stmt->execute();
$result = $stmt->get_result();
$teacher = $result->fetch_assoc();

// Courses table has been removed, so set these to null
$upcomingExams = null;
$courses = null;

// Old queries that referenced the courses table
// Get upcoming exams for this teacher's department
// $upcomingExamsQuery = "SELECT e.exam_name, c.course_name, e.exam_date, e.total_marks
//                      FROM exams e
//                      JOIN courses c ON e.course_id = c.id
//                      WHERE c.department = ? AND e.exam_date >= CURDATE()
//                      ORDER BY e.exam_date ASC
//                      LIMIT 5";
// $stmt = $conn->prepare($upcomingExamsQuery);
// $stmt->bind_param("s", $teacher['department']);
// $stmt->execute();
// $upcomingExams = $stmt->get_result();

// Get students assigned to this teacher's department
$studentsQuery = "SELECT student_id, first_name, last_name, batch, email
                 FROM students
                 WHERE department = ?
                 ORDER BY batch, last_name
                 LIMIT 10";
$stmt = $conn->prepare($studentsQuery);
$stmt->bind_param("s", $teacher['department']);
$stmt->execute();
$students = $stmt->get_result();

// Get courses in the teacher's department
// $coursesQuery = "SELECT course_code, course_name, credit_hours
//                FROM courses
//                WHERE department = ?
//                LIMIT 5";
// $stmt = $conn->prepare($coursesQuery);
// $stmt->bind_param("s", $teacher['department']);
// $stmt->execute();
// $courses = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teacher Dashboard - College Management System</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>Teacher Panel</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> Students
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="courses.php">
                            <i class="fas fa-book me-2"></i> Courses
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="exams.php">
                            <i class="fas fa-file-alt me-2"></i> Exams
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> Results
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> Attendance
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Teacher Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card profile-header">
                            <div class="row g-0">
                                <div class="col-md-2 text-center">
                                    <img src="https://via.placeholder.com/150" alt="Teacher" class="profile-img">
                                </div>
                                <div class="col-md-10 profile-info">
                                    <div class="card-body">
                                        <h2><?php echo $teacher['first_name'] . ' ' . $teacher['last_name']; ?></h2>
                                        <p><strong>Teacher ID:</strong> <?php echo $teacher['teacher_id']; ?></p>
                                        <p><strong>Department:</strong> <?php echo $teacher['department']; ?></p>
                                        <p><strong>Email:</strong> <?php echo $teacher['email']; ?></p>
                                        <p><strong>Phone:</strong> <?php echo $teacher['phone']; ?></p>
                                        <p><strong>Qualification:</strong> <?php echo $teacher['qualification']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="card-title mb-0">Upcoming Exams</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Exam</th>
                                                <th>Course</th>
                                                <th>Date</th>
                                                <th>Marks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                                <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $exam['exam_name']; ?></td>
                                                        <td><?php echo $exam['course_name']; ?></td>
                                                        <td><?php echo date('d M Y', strtotime($exam['exam_date'])); ?></td>
                                                        <td><?php echo $exam['total_marks']; ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center">No upcoming exams</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="exams.php" class="btn btn-warning btn-sm mt-3">View All Exams</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Students -->
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">Department Students</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Batch</th>
                                                <th>Email</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($students && $students->num_rows > 0): ?>
                                                <?php while ($student = $students->fetch_assoc()): ?>
                                                    <tr>
                                                        <td><?php echo $student['student_id']; ?></td>
                                                        <td><?php echo $student['first_name'] . ' ' . $student['last_name']; ?></td>
                                                        <td><?php echo $student['batch']; ?></td>
                                                        <td><?php echo $student['email']; ?></td>
                                                        <td>
                                                            <a href="view_student.php?id=<?php echo $student['student_id']; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">No students found</td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <a href="students.php" class="btn btn-primary btn-sm mt-3">View All Students</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- Quick Actions -->
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="card-title mb-0">Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="add_result.php" class="btn btn-outline-primary">
                                        <i class="fas fa-plus-circle me-2"></i> Add Results
                                    </a>
                                    <a href="attendance.php" class="btn btn-outline-success">
                                        <i class="fas fa-calendar-check me-2"></i> Take Attendance
                                    </a>
                                    <a href="exams.php" class="btn btn-outline-warning">
                                        <i class="fas fa-file-alt me-2"></i> View Exams
                                    </a>
                                    <a href="reports.php" class="btn btn-outline-danger">
                                        <i class="fas fa-file-pdf me-2"></i> Generate Reports
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 