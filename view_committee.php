<?php
require_once 'includes/dbh.inc.php';

// Check if committee_members table exists
$sql = "SHOW TABLES LIKE 'committee_members'";
$result = $conn->query($sql);

if ($result->num_rows == 0) {
    echo "<div class='alert alert-warning'>পরিচালনা পর্ষদের তালিকা এখনো তৈরি করা হয়নি।</div>";
} else {
    // Get all committee members ordered by priority
    $sql = "SELECT * FROM committee_members ORDER BY priority ASC";
    $result = $conn->query($sql);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>পরিচালনা পর্ষদ - কলেজ ম্যানেজমেন্ট সিস্টেম</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .member-card {
            border-radius: 10px;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
            background-color: white;
        }
        .member-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .member-img-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin: 20px auto;
        }
        .member-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #f8f9fa;
        }
        .default-photo {
            width: 150px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
            color: #6c757d;
            border-radius: 50%;
            font-size: 60px;
            margin: 0 auto;
        }
        .member-info {
            padding: 20px;
            text-align: center;
        }
        .member-name {
            font-size: 1.3rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .member-position {
            color: #6c757d;
            font-style: italic;
            margin-bottom: 15px;
        }
        .member-details {
            color: #212529;
            font-size: 0.9rem;
            text-align: justify;
        }
        .banner {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
            border-radius: 0 0 15px 15px;
        }
        .back-to-home {
            margin-top: 40px;
            margin-bottom: 40px;
        }
    </style>
</head>
<body>
    <div class="banner">
        <div class="container">
            <h1 class="text-center">পরিচালনা পর্ষদ</h1>
            <p class="text-center mb-0">আমাদের প্রতিষ্ঠানের মূল্যবান সদস্যবৃন্দ</p>
        </div>
    </div>

    <div class="container">
        <?php if (isset($result) && $result->num_rows > 0): ?>
            <div class="row g-4">
                <?php while ($row = $result->fetch_assoc()): ?>
                    <div class="col-md-4 mb-4">
                        <div class="member-card shadow">
                            <div class="member-img-container mt-4">
                                <?php if (!empty($row["photo"])): ?>
                                    <img src="<?php echo $row["photo"]; ?>" class="member-img" alt="<?php echo htmlspecialchars($row["name"]); ?>">
                                <?php else: ?>
                                    <div class="default-photo">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="member-info">
                                <div class="member-name"><?php echo htmlspecialchars($row["name"]); ?></div>
                                <div class="member-position"><?php echo htmlspecialchars($row["position"]); ?></div>
                                <?php if (!empty($row["details"])): ?>
                                    <div class="member-details">
                                        <?php echo nl2br(htmlspecialchars($row["details"])); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            </div>
        <?php else: ?>
            <div class="alert alert-info text-center">এখনও কোন সদস্য যোগ করা হয়নি।</div>
        <?php endif; ?>
        
        <div class="text-center back-to-home">
            <a href="index.php" class="btn btn-outline-primary">হোম পেজে ফিরে যান</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 